<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{a1b0f2cf-3646-7174-a521-f380ecdc34d6}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>NodeCanvas</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\NodeCanvas\</OutputPath>
    <DefineConstants>UNITY_2022_3_46;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_STANDALONE;TEXTCORE_1_0_OR_NEWER;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;ODIN_INSPECTOR;ODIN_INSPECTOR_3;ODIN_INSPECTOR_3_1;ODIN_VALIDATOR;ODIN_VALIDATOR_3_1;UNITY_POST_PROCESSING_STACK_V2;NODECANVAS;ODIN_INSPECTOR_3_2;ODIN_INSPECTOR_3_3;ODIN_VALIDATOR_3_2;ODIN_VALIDATOR_3_3;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="D:\Unity\2022.3.46f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="D:\Unity\2022.3.46f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\CreatePrimitive.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\FindObjectsOfType.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\RemoveElementFromList.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\SendEvent.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Pathfinding\Wander.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\_DeprecatedFiles\Legacy_ExecuteStaticFunction_Multiplatform.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Camera\CameraFader.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Composites\FlipSelector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\ComposeVector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\TriggerBoolean.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\GetOtherBlackboardVariable.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\SetListElement.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckEnum.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Composites\Sequencer.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\InstantiateGameObject.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\NestedDTState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Composites\StepIterator.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CheckDistanceToGameObjectAny.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\DTConnection.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\NestedFSMState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\BehaviourTree.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animator\MecanimSetFloat.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\Dictionary Specific\GetDictionaryElement.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\LogicTrees\LogicOwner.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Editor\Inspectors\ActionListPlayerInspector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CheckDistanceToGameObject.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Animator\MecanimCheckFloat.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\ScriptControl\CheckProperty_Multiplatform.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Direct\MoveTowards.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\Nodes\ActionNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\Standalone\SetProperty.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\BTDecorator.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\SetObjectVisibility.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Camera\FadeIn.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\GetComponent.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\ShoutEvent.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\FSMNodeNested.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Dialogue\Say.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\Standalone\ExecuteFunction.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Direct\InputMove.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\DTNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CanSeeTarget2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CanSeeTargetAny2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\ConcurrentSubTree.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\FSMConnection.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\DestroyGameObject.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\DebugBeep.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Input\CheckKeyboardInput.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\SystemEvents\CheckMouse2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Input\GetInputAxis.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\SystemEvents\CheckMouseClick2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetEnumFlag.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Animator\MecanimCheckBool.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\LoadBlackboard.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\SystemEvents\CheckCollision2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckInt.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\Dictionaries\TryGetValue.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetIntRandom.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\IStateCallbackReceiver.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\BTComposite.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\BTConnection.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\LogicTrees\Nodes\TimeNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\Nodes\ConditionNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Audio\PlayAudioAtPosition.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CanSeeTargetAny.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Input\CheckMousePick2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\GetListCount.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\Dictionary Specific\AddElementToDictionary.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\DecomposeVector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Input\GetMouseScrollDelta.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Interruptor.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\SwitchBehaviour.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\BehaviourTreeOwner.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Editor\Inspectors\BehaviourTreeOwnerInspector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetEnum.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Dialogue\SayRandom.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\AddElementToList.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Leafs\NestedDT.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\FSMNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\FSMStateNested.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\_DeprecatedFiles\Legacy_ConcurrentState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\ScriptControl\CheckUnityEvent.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\FindClosestWithTag.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\LookAt.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\FindAllWithLayer.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\LogicTrees\Nodes\EventNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Composites\PrioritySelector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\ImplementedAction_Multiplatform.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Input\GetMousePosition.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\GetAllChildGameObjects.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\SystemEvents\CheckCollision.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CanSeeTarget.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\BTNodeNested.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\ScriptControl\CheckCSharpEvent.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\SystemEvents\CheckTrigger.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckVectorDistance.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\Nodes\StatementNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\PickRandomListElement.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\EmptyState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Utility\CheckEventValue.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckUnityObject.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\Nodes\SubDialogueTree.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Utility\DebugCondition.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\Nodes\MultipleConditionNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\IDialogueActor.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\ShuffleList.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetInt.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Utility\CheckSignal.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Pathfinding\Flee.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SaveBlackboard.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetVector3.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\BehaviourTreeExtensions.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Editor\Inspectors\DialogueTreeInspector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\AnyState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\GraphOwnerControl.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\DialogueActor.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\GetCloserGameObjectInList.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\FSM.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Camera\FadeOut.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckBoolean.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\Standalone\GetProperty.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetBooleanRandom.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\SendEventToObjects.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\Get Self.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Editor\Commands.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\DialogueTreeController.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\SuperActionState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\Standalone\ImplementedAction.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\ConcurrentSubFSM.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Application\LoadScene.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\SystemEvents\CheckMouseClick.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\IsInFront2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Composites\Selector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\Nodes\FinishNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Animator\MecanimIsInTransition.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Movement\PathExists.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Leafs\NestedFSM.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\LogicTrees\LogicConnection.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\SystemEvents\CheckTrigger2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Utility\CheckEvent.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Input\WaitMousePick2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Input\CheckMousePick.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\LogicTrees\Nodes\LogicNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetVariable.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\BTNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\ConditionalEvaluator.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\ScriptControl\CheckFunction_Multiplatform.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\Nodes\Jumper.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Remapper.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckEnumFlag.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\ActionListPlayer\ActionListPlayer.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Dialogue\StartDialogueTree.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\OnVariableChange.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetBoolean.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\GetToString.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetFloat.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animator\MecanimSetTrigger.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animator\MecanimSetInt.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Guard.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Direct\RotateAway.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Pathfinding\FindClosestEdge.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\GetGameObjectPosition.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Composites\ProbabilitySelector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animator\MecanimSetBool.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\Lists\ListIsEmpty.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\GetDistance.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\RemoveComponent.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\DialogueEventArguments.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\Nodes\MultipleChoiceNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Animator\MecanimCheckInt.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\WaitUntil.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\DialogueTree.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Direct\RotateTowards.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CheckSpeed.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Optional.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\_DeprecatedFiles\Legacy_EventReceiverAttribute.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Merge.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\Nodes\ProbabilitySelector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Leafs\ActionNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\ClearList.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\InvokeSignal.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\ScriptControl\Standalone\CheckFunction.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animator\MecanimSetLayerWeight.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\GetProperty_Multiplatform.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Physics\GetOverlapSphereObjects.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetOtherBlackboardVariable.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\SendMessageToType.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\_DeprecatedFiles\Legacy_GoToNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\FindAllWithTag.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\NormalizeVector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Direct\CurveTransformTween.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\NestedBTState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Pathfinding\Patrol.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckVariable.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animation (Legacy)\PlayAnimationSimple.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\LogicTrees\LogicTrees.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Monitor.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\LogicTrees\Nodes\ActionNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\DTNodeNested.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\SetField.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckString.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\ActionState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Composites\Switch.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\SystemEvents\CheckMouse.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Timeout.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Editor\Drawers\StatementDrawer.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Utility\Timeout.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Physics\GetLinecastInfo2DAll.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Leafs\SubTree.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\EvaluateCurve.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\Wait.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\IStatement.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Inverter.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Utility\Probability.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\OnFSMExit.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SampleCurve.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\DebugLogText.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\OnFSMUpdate.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\SetObjectActive.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\UGUI\ButtonClicked.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Repeater.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\IState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\IsActive.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\_DeprecatedFiles\Legacy_ExecuteStaticFunction.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\StringContains.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\SendMessage.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckNull.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\FindAllWithName.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\DebugLogVariable.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CheckDistanceToGameObject2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\RunForever.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Input\WaitMousePick.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CheckLOS.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckBooleanTrigger.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\PickListElement.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\IsWithinLayerMask.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\_DeprecatedFiles\Legacy_RootSwitcher.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\GetIndexOfElement.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\Nodes\OnFSMEnter.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\FSMOwner.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\UGUI\InterceptEvent.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\DialogueActorAsset.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\FindObjectOfType.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\ScriptControl\Standalone\CheckProperty.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\CheckFloat.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CheckDistanceToGameObjectAny2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\_DeprecatedFiles\Legacy_NodeToggler.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\IsInFront.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Pathfinding\MoveToGameObject.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Physics\GetLinecastInfo2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\DebugDrawLine.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\_DeprecatedFiles\Legacy_EnterExitState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\SetObjectEnabled.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Physics\GetLinecastInfo.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Utility\ForceFinishGraph.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animator\MecanimSetLookAt.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\ExecuteFunction_Multiplatform.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Editor\Inspectors\DialogueTreeControllerInspector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\GetField.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\InsertElementToList.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Direct\MoveAway.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\StateMachines\FSMState.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\List Specific\SortGameObjectListByDistance.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Setter.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\FindWithTag.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animator\MecanimPlayAnimation.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\ScriptControl\SetProperty_Multiplatform.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\FindWithName.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\CheckLOS2D.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Input\CheckButtonInput.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animator\MecanimSetIK.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Filter.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\CreateGameObject.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\ScriptControl\CheckField.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\GameObject\FindChildByName.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Composites\Parallel.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\DialogueTrees\DialogueGUI\DialogueUGUI.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\LogicTrees\Nodes\StartNode.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Animation (Legacy)\PlayAnimationAdvanced.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Blackboard\Lists\ListContainsElement.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Composites\BinarySelector.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\GameObject\HasComponent.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Movement\Pathfinding\MoveToPosition.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Decorators\Iterator.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Actions\Blackboard\SetFloatRandom.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Tasks\Conditions\Utility\CheckStateStatus.cs" />
    <Compile Include="Assets\Plugins\ParadoxNotion\NodeCanvas\Modules\BehaviourTrees\Nodes\Leafs\ConditionNode.cs" />
    <None Include="Assets\Plugins\ParadoxNotion\NodeCanvas\NodeCanvas.asmdef" />
    <None Include="Assets\Plugins\ParadoxNotion\NodeCanvas\README.txt" />
    <Reference Include="UnityEngine">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\PlaybackEngines\WebGLSupport\UnityEditor.WebGL.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.visualscripting@1.9.4\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Reflection.Editor">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Reflection.Editor.dll</HintPath>
    </Reference>
    <Reference Include="MTE">
      <HintPath>D:\Project\client\Assets\Plugins\MeshTerrainEditor\Editor\MTE.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.collections@2.5.7\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.ext.nunit@2.0.3\net40\unity-custom\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.collab-proxy@2.4.4\Lib\Editor\PlasticSCM\unityplastic.dll</HintPath>
    </Reference>
    <Reference Include="LitJson">
      <HintPath>D:\Project\client\Assets\Plugins\LitJson.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.collab-proxy@2.4.4\Lib\Editor\PlasticSCM\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization.Config">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.Config.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Editor">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.collab-proxy@2.4.4\Lib\Editor\PlasticSCM\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.collab-proxy@2.4.4\Lib\Editor\PlasticSCM\log4netPlastic.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>D:\Project\client\Assets\Plugins\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.visualscripting@1.9.4\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Attributes">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Hashing">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.collections@2.5.7\Unity.Collections.Tests\System.IO.Hashing\System.IO.Hashing.dll</HintPath>
    </Reference>
    <Reference Include="MTEEditorAssets">
      <HintPath>D:\Project\client\Assets\Plugins\MeshTerrainEditor\EditorResources\MTEEditorAssets.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>D:\Project\client\Assets\Plugins\DOTween\Editor\DOTweenEditor.dll</HintPath>
    </Reference>
    <Reference Include="Excel">
      <HintPath>D:\Project\client\Assets\Editor\u3d-Excel\Excel.dll</HintPath>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>D:\Project\client\Packages\com.code-philosophy.hybridclr\Plugins\dnlib.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.visualscripting@1.9.4\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>D:\Project\client\Packages\com.code-philosophy.hybridclr\Plugins\LZ4.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>D:\Project\client\Assets\Plugins\DOTween\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="proxima-websocket-sharp">
      <HintPath>D:\Project\client\Assets\Plugins\Proxima\WebSocketSharp\proxima-websocket-sharp.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities.Editor">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.nuget.newtonsoft-json@3.2.1\Runtime\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.visualscripting@1.9.4\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.nuget.mono-cecil@1.11.5\Mono.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="Coffee.UIParticle.R">
      <HintPath>D:\Project\client\Library\PackageCache\com.coffee.ui-particle@4.11.2\Runtime\Coffee.UIParticle.R.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus">
      <HintPath>D:\Project\client\Assets\Editor\u3d-Excel\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinValidator.Editor">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinValidator.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.GradleProject">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.GradleProject.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="ParadoxNotion.csproj">
      <Project>{6794afec-0e43-2e73-9838-3836c1849b69}</Project>
      <Name>ParadoxNotion</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEditor.UI.csproj">
      <Project>{27df5c51-0832-0742-f448-c95d51cbbe66}</Project>
      <Name>UnityEditor.UI</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.UI.csproj">
      <Project>{2b8120b2-369f-90b0-cea1-bba27beb3963}</Project>
      <Name>UnityEngine.UI</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
