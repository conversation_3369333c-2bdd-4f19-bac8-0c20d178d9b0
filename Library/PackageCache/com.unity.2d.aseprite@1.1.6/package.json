{"name": "com.unity.2d.aseprite", "version": "1.1.6", "unity": "2021.3", "unityRelease": "15f1", "displayName": "2D Aseprite Importer", "description": "2D Aseprite Importer is a package which enables the import of .aseprite files from the Pixel Art tool Aseprite.", "keywords": ["2d", "Aseprite", "Pixel", "Art", "assetimporter"], "category": "2D", "dependencies": {"com.unity.2d.sprite": "1.0.0", "com.unity.2d.common": "6.0.6", "com.unity.mathematics": "1.2.6", "com.unity.modules.animation": "1.0.0"}, "relatedPackages": {"com.unity.2d.aseprite.tests": "1.1.5"}, "_upm": {"changelog": "### Fixed\n- Fixed an issue where SpriteRenderers would lose their reference if an Aseprite file's name was changed. (DANB-692)"}, "upmCi": {"footprint": "ff6a5439180b5fdb80213092bddcbe2f6e29c1c0"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.2d.aseprite@1.1/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/AsepriteImporter.git", "type": "git", "revision": "74d430549e8f10c1cde9a19b90ceaf626f1111eb"}}