#if UNITY_EDITOR || FBXSDK_RUNTIME
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 3.0.12
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------

namespace Autodesk.Fbx {

public class FbxCollection : FbxObject {
  internal FbxCollection(global::System.IntPtr cPtr, bool ignored) : base(cPtr, ignored) { }

  // override void Dispose() {base.Dispose();}

  public new static FbxCollection Create(FbxManager pManager, string pName) {
    global::System.IntPtr cPtr = NativeMethods.FbxCollection_Create__SWIG_0(FbxManager.getCPtr(pManager), pName);
    FbxCollection ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxCollection(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public new static FbxCollection Create(FbxObject pContainer, string pName) {
    global::System.IntPtr cPtr = NativeMethods.FbxCollection_Create__SWIG_1(FbxObject.getCPtr(pContainer), pName);
    FbxCollection ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxCollection(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public virtual void Clear() {
    NativeMethods.FbxCollection_Clear(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public virtual bool AddMember(FbxObject pMember) {
    bool ret = NativeMethods.FbxCollection_AddMember(swigCPtr, FbxObject.getCPtr(pMember));
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public virtual bool RemoveMember(FbxObject pMember) {
    bool ret = NativeMethods.FbxCollection_RemoveMember(swigCPtr, FbxObject.getCPtr(pMember));
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public int GetMemberCount() {
    int ret = NativeMethods.FbxCollection_GetMemberCount(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public FbxObject GetMember(int pIndex) {
    global::System.IntPtr cPtr = NativeMethods.FbxCollection_GetMember__SWIG_0(swigCPtr, pIndex);
    FbxObject ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxObject(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public FbxObject GetMember() {
    global::System.IntPtr cPtr = NativeMethods.FbxCollection_GetMember__SWIG_1(swigCPtr);
    FbxObject ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxObject(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public virtual bool IsMember(FbxObject pMember) {
    bool ret = NativeMethods.FbxCollection_IsMember(swigCPtr, FbxObject.getCPtr(pMember));
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public override int GetHashCode(){
      return swigCPtr.Handle.GetHashCode();
  }

  public bool Equals(FbxCollection other) {
    if (object.ReferenceEquals(other, null)) { return false; }
    return this.swigCPtr.Handle.Equals (other.swigCPtr.Handle);
  }

  public override bool Equals(object obj){
    if (object.ReferenceEquals(obj, null)) { return false; }
    /* is obj a subclass of this type; if so use our Equals */
    var typed = obj as FbxCollection;
    if (!object.ReferenceEquals(typed, null)) {
      return this.Equals(typed);
    }
    /* are we a subclass of the other type; if so use their Equals */
    if (typeof(FbxCollection).IsSubclassOf(obj.GetType())) {
      return obj.Equals(this);
    }
    /* types are unrelated; can't be a match */
    return false;
  }

  public static bool operator == (FbxCollection a, FbxCollection b) {
    if (object.ReferenceEquals(a, b)) { return true; }
    if (object.ReferenceEquals(a, null) || object.ReferenceEquals(b, null)) { return false; }
    return a.Equals(b);
  }

  public static bool operator != (FbxCollection a, FbxCollection b) {
    return !(a == b);
  }

  public FbxAnimLayer GetAnimLayerMember(int pIndex) {
    global::System.IntPtr cPtr = NativeMethods.FbxCollection_GetAnimLayerMember__SWIG_0(swigCPtr, pIndex);
    FbxAnimLayer ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxAnimLayer(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public FbxAnimLayer GetAnimLayerMember() {
    global::System.IntPtr cPtr = NativeMethods.FbxCollection_GetAnimLayerMember__SWIG_1(swigCPtr);
    FbxAnimLayer ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxAnimLayer(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

}

}

#endif // UNITY_EDITOR || FBXSDK_RUNTIME