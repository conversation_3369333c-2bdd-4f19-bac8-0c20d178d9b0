#if UNITY_EDITOR || FBXSDK_RUNTIME
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 3.0.12
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------

namespace Autodesk.Fbx {

public class FbxCluster : FbxSubDeformer {
  internal FbxCluster(global::System.IntPtr cPtr, bool ignored) : base(cPtr, ignored) { }

  // override void Dispose() {base.Dispose();}

  public new static FbxCluster Create(FbxManager pManager, string pName) {
    global::System.IntPtr cPtr = NativeMethods.FbxCluster_Create__SWIG_0(FbxManager.getCPtr(pManager), pName);
    FbxCluster ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxCluster(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public new static FbxCluster Create(FbxObject pContainer, string pName) {
    global::System.IntPtr cPtr = NativeMethods.FbxCluster_Create__SWIG_1(FbxObject.getCPtr(pContainer), pName);
    FbxCluster ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxCluster(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void SetLinkMode(FbxCluster.ELinkMode pMode) {
    NativeMethods.FbxCluster_SetLinkMode(swigCPtr, (int)pMode);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public FbxCluster.ELinkMode GetLinkMode() {
    FbxCluster.ELinkMode ret = (FbxCluster.ELinkMode)NativeMethods.FbxCluster_GetLinkMode(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void SetLink(FbxNode pNode) {
    NativeMethods.FbxCluster_SetLink(swigCPtr, FbxNode.getCPtr(pNode));
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public FbxNode GetLink() {
    global::System.IntPtr cPtr = NativeMethods.FbxCluster_GetLink(swigCPtr);
    FbxNode ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxNode(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void AddControlPointIndex(int pIndex, double pWeight) {
    NativeMethods.FbxCluster_AddControlPointIndex(swigCPtr, pIndex, pWeight);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public int GetControlPointIndicesCount() {
    int ret = NativeMethods.FbxCluster_GetControlPointIndicesCount(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void SetControlPointIWCount(int pCount) {
    NativeMethods.FbxCluster_SetControlPointIWCount(swigCPtr, pCount);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public void SetTransformMatrix(FbxAMatrix pMatrix) {
    NativeMethods.FbxCluster_SetTransformMatrix(swigCPtr, FbxAMatrix.getCPtr(pMatrix));
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public FbxAMatrix GetTransformMatrix(FbxAMatrix pMatrix) {
    FbxAMatrix ret = new FbxAMatrix(NativeMethods.FbxCluster_GetTransformMatrix(swigCPtr, FbxAMatrix.getCPtr(pMatrix)), false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void SetTransformLinkMatrix(FbxAMatrix pMatrix) {
    NativeMethods.FbxCluster_SetTransformLinkMatrix(swigCPtr, FbxAMatrix.getCPtr(pMatrix));
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public FbxAMatrix GetTransformLinkMatrix(FbxAMatrix pMatrix) {
    FbxAMatrix ret = new FbxAMatrix(NativeMethods.FbxCluster_GetTransformLinkMatrix(swigCPtr, FbxAMatrix.getCPtr(pMatrix)), false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public override int GetHashCode(){
      return swigCPtr.Handle.GetHashCode();
  }

  public bool Equals(FbxCluster other) {
    if (object.ReferenceEquals(other, null)) { return false; }
    return this.swigCPtr.Handle.Equals (other.swigCPtr.Handle);
  }

  public override bool Equals(object obj){
    if (object.ReferenceEquals(obj, null)) { return false; }
    /* is obj a subclass of this type; if so use our Equals */
    var typed = obj as FbxCluster;
    if (!object.ReferenceEquals(typed, null)) {
      return this.Equals(typed);
    }
    /* are we a subclass of the other type; if so use their Equals */
    if (typeof(FbxCluster).IsSubclassOf(obj.GetType())) {
      return obj.Equals(this);
    }
    /* types are unrelated; can't be a match */
    return false;
  }

  public static bool operator == (FbxCluster a, FbxCluster b) {
    if (object.ReferenceEquals(a, b)) { return true; }
    if (object.ReferenceEquals(a, null) || object.ReferenceEquals(b, null)) { return false; }
    return a.Equals(b);
  }

  public static bool operator != (FbxCluster a, FbxCluster b) {
    return !(a == b);
  }

  public int GetControlPointIndexAt(int index) {
    int ret = NativeMethods.FbxCluster_GetControlPointIndexAt(swigCPtr, index);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public double GetControlPointWeightAt(int index) {
    double ret = NativeMethods.FbxCluster_GetControlPointWeightAt(swigCPtr, index);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public enum ELinkMode {
    eNormalize,
    eAdditive,
    eTotalOne
  }

}

}

#endif // UNITY_EDITOR || FBXSDK_RUNTIME