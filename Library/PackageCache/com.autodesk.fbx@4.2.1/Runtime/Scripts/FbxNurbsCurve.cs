#if UNITY_EDITOR || FBXSDK_RUNTIME
//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 3.0.12
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------

namespace Autodesk.Fbx {

public class FbxNurbsCurve : FbxGeometry {
  internal FbxNurbsCurve(global::System.IntPtr cPtr, bool ignored) : base(cPtr, ignored) { }

  // override void Dispose() {base.Dispose();}

  public new static FbxNurbsCurve Create(FbxManager pManager, string pName) {
    global::System.IntPtr cPtr = NativeMethods.FbxNurbsCurve_Create__SWIG_0(FbxManager.getCPtr(pManager), pName);
    FbxNurbsCurve ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxNurbsCurve(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public new static FbxNurbsCurve Create(FbxObject pContainer, string pName) {
    global::System.IntPtr cPtr = NativeMethods.FbxNurbsCurve_Create__SWIG_1(FbxObject.getCPtr(pContainer), pName);
    FbxNurbsCurve ret = (cPtr == global::System.IntPtr.Zero) ? null : new FbxNurbsCurve(cPtr, false);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void InitControlPoints(int pCount, FbxNurbsCurve.EType pVType) {
    NativeMethods.FbxNurbsCurve_InitControlPoints(swigCPtr, pCount, (int)pVType);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public int GetKnotCount() {
    int ret = NativeMethods.FbxNurbsCurve_GetKnotCount(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void SetOrder(int pOrder) {
    NativeMethods.FbxNurbsCurve_SetOrder(swigCPtr, pOrder);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public int GetOrder() {
    int ret = NativeMethods.FbxNurbsCurve_GetOrder(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void SetStep(int pStep) {
    NativeMethods.FbxNurbsCurve_SetStep(swigCPtr, pStep);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public int GetStep() {
    int ret = NativeMethods.FbxNurbsCurve_GetStep(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void SetDimension(FbxNurbsCurve.EDimension pDimension) {
    NativeMethods.FbxNurbsCurve_SetDimension(swigCPtr, (int)pDimension);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public FbxNurbsCurve.EDimension GetDimension() {
    FbxNurbsCurve.EDimension ret = (FbxNurbsCurve.EDimension)NativeMethods.FbxNurbsCurve_GetDimension(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public bool IsRational() {
    bool ret = NativeMethods.FbxNurbsCurve_IsRational(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public int GetSpanCount() {
    int ret = NativeMethods.FbxNurbsCurve_GetSpanCount(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public bool IsPolyline() {
    bool ret = NativeMethods.FbxNurbsCurve_IsPolyline(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public bool IsBezier() {
    bool ret = NativeMethods.FbxNurbsCurve_IsBezier(swigCPtr);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public override int GetHashCode(){
      return swigCPtr.Handle.GetHashCode();
  }

  public bool Equals(FbxNurbsCurve other) {
    if (object.ReferenceEquals(other, null)) { return false; }
    return this.swigCPtr.Handle.Equals (other.swigCPtr.Handle);
  }

  public override bool Equals(object obj){
    if (object.ReferenceEquals(obj, null)) { return false; }
    /* is obj a subclass of this type; if so use our Equals */
    var typed = obj as FbxNurbsCurve;
    if (!object.ReferenceEquals(typed, null)) {
      return this.Equals(typed);
    }
    /* are we a subclass of the other type; if so use their Equals */
    if (typeof(FbxNurbsCurve).IsSubclassOf(obj.GetType())) {
      return obj.Equals(this);
    }
    /* types are unrelated; can't be a match */
    return false;
  }

  public static bool operator == (FbxNurbsCurve a, FbxNurbsCurve b) {
    if (object.ReferenceEquals(a, b)) { return true; }
    if (object.ReferenceEquals(a, null) || object.ReferenceEquals(b, null)) { return false; }
    return a.Equals(b);
  }

  public static bool operator != (FbxNurbsCurve a, FbxNurbsCurve b) {
    return !(a == b);
  }

  public double GetKnotVectorAt(int pIndex) {
    double ret = NativeMethods.FbxNurbsCurve_GetKnotVectorAt(swigCPtr, pIndex);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
    return ret;
  }

  public void SetKnotVectorAt(int pIndex, double aKnot) {
    NativeMethods.FbxNurbsCurve_SetKnotVectorAt(swigCPtr, pIndex, aKnot);
    if (NativeMethods.SWIGPendingException.Pending) throw NativeMethods.SWIGPendingException.Retrieve();
  }

  public enum EDimension {
    e2D = 2,
    e3D
  }

  new public enum EType {
    eOpen,
    eClosed,
    ePeriodic
  }

}

}

#endif // UNITY_EDITOR || FBXSDK_RUNTIME