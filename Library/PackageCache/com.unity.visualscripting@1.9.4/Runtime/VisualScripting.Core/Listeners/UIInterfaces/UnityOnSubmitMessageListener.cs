using UnityEngine.EventSystems;

namespace Unity.VisualScripting
{
    [UnityEngine.AddComponentMenu("")]
    [VisualScriptingHelpURL(typeof(UnityOnSubmitMessageListener))]
    public sealed class UnityOnSubmitMessageListener : <PERSON><PERSON><PERSON><PERSON>, ISubmitHandler
    {
        public void OnSubmit(BaseEventData eventData)
        {
            EventBus.Trigger(EventHooks.OnSubmit, gameObject, eventData);
        }
    }
}
