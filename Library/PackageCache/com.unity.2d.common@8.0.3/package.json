{"name": "com.unity.2d.common", "displayName": "2D Common", "version": "8.0.3", "unity": "2022.2", "description": "2D Common is a package that contains shared functionalities that are used by most of the other 2D packages.", "keywords": ["2d"], "category": "2D", "dependencies": {"com.unity.2d.sprite": "1.0.0", "com.unity.mathematics": "1.1.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.burst": "1.7.3"}, "relatedPackages": {"com.unity.2d.common.tests": "8.0.3"}, "_upm": {"changelog": "- ### Fixed\n- DANB-604 Fix case where Spriteshape vertex array exceeds limit even though it has not reached 64K."}, "upmCi": {"footprint": "fa88ffac2c194de95162b512b7c1589c458de725"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.2d.common@8.0/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/2d.git", "type": "git", "revision": "317cb5a2d84ec8711e66cbcce61bc6179da9d791"}}