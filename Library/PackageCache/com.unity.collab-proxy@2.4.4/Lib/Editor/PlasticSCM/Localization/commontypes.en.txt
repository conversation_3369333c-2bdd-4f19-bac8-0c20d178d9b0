== AccessTokensNotEnabled ==
Access tokens are not enabled on this server. If you believe this to be an error, please contact your server administrator.

== AclNotFound ==
You are working with out-of-date objects. Maybe your client or workspace is out of date, please update it.

== ActivateUserNotInLicenseList ==
The user {0} cannot be activated. To activate a user for the first time, execute Plastic SCM with this user credentials. In order to activate a user with this command, it must have been previously deactivated.

== AddMoveConflict ==
Add move conflict

== AdminCmdReadonlyInvalidAction ==
The action '{0}' is not valid for the readonly admin command.

== AdminCommandNotAllowedOnCloud ==
Admin commands cannot be executed in the Cloud server.

== AdminCommandNotValid ==
The admin command '{0}' is not valid.

== AlreadyCoInWkOrPrivate ==
The element {0} is either a private file or it is already checked out in the current workspace.

== AttAlreadyExists ==
The attribute {0} already exists.

== AttNotFound ==
The specified attribute {0} can't be found.

== AttrNotFound ==
The specified attribute realization can't be found.

== AvailableLdapTypes ==
Available type of LDAP servers

== AzureMaxDbSize ==
The organization '{0}' has reached its size quota. Please contact with support.

== BackOpNoGuiControl ==
Background operation tried to update the GUI, but no GUI control has been defined.

== BadDomainSyntax ==
The specified domain is not valid: {0}.

== BadFormatSeidData ==
Bad format in SEID data: {0}.

== BitmapTooBig ==
Could not export current diagram. It may be too big, try filtering to reduce the size.

== BrNotFound ==
The specified branch {0} can't be found.

== ParentBranchNotFound ==
The parent branch {0} does not exist anymore. Please try to create the branch again from a valid parent branch (refresh your GUI if needed).

== ParentBranchWithoutHead ==
The parent branch {0} is not valid because it does not have a Head. Please create the branch using a different parent branch.

== ShelveIsNotAValidBranchBase ==
A branch cannot be created from a shelve.

== BranchAlreadyExists ==
The branch {0} already exists.

== CannotArchiveNotUploadedRevision ==
The archive operation cannot continue since the requested data was not committed to the blob storage yet. Please wait some seconds and try again.

== CannotCheckSecurityPathInIncompleteCset ==
The security cannot be checked because the path of the item:{0} cannot be calculated in the changeset cs:{1} (br:{2}): the changeset isn't replicated. Please, replicate the missing changeset and try again.

== CannotConnectRepserver ==
Unable to connect to the server {0}

== CannotCreateIostatsFolder ==
Error: unable to create folder for disk test. {0}

== CannotCreateSymlink ==
Cannot create symlink.

== CannotCreateWindowsSymlink ==
Cannot create symlink.
Symlink path: {0}
Target path: {1}
Hint: please enable 'Developer Mode' on Windows.
A) Enter 'For developers' into the Cortana search box in the taskbar, and switch 'Developer Mode' on.
B) Otherwise, you can disable symlink creation altogether by adding the following to your client.conf file: <EnableSymlinkSupportOnWindows>no</EnableSymlinkSupportOnWindows>.

== CannotCreateXlink ==
Can't create the xlink because the entry already exists: {0}

== CannotDecryptData ==
The data {0} is encrypted. The encryption info is needed in order to read it.

== CannotDeleteChangesetItemsLocked ==
The changeset cs:{0} cannot be deleted until the following locked items are released: {1}

== CannotDeleteChangesetShelvesetDependsOnIt ==
The changeset cs:{0} cannot be deleted because sh:{1} depends on it. You must delete sh:{1} first.

== CannotDeleteChangesetChangesetDependsOnIt ==
The changeset cs:{0} cannot be deleted because cs:{1} depends on it. You must delete cs:{1} first.

== CannotDeleteChangesetEmptyBranchDependsOnIt ==
The changeset cs:{0} cannot be deleted because the empty branch {1} depends on it.

== CannotDeleteChangesetLabelPointsToIt ==
The changeset cs:{0} cannot be deleted because label '{1}' points to it. You must delete the label first.

== CannotDeleteChangesetIsMergeSource ==
The changeset cs:{0} cannot be deleted because it is the source of a mergelink. You must delete the merge destination (cs:{1}) first.

== CannotDeleteChangesetIsIntervalMergeSource ==
The changeset cs:{0} cannot be deleted because it is part of an interval merge source.

== CannotDeleteChangesetIsChangesetZero ==
The changeset cs:0 cannot be deleted. It is a fixed point in your repository's history.

== CannotDiffDstCsetNotComplete ==
Cannot show differences between changeset {0} and changeset {1}. The destination changeset {1} has not been replicated to this repository. Make sure that you replicated the branch containing this changeset.

== CannotDiffSourceCsetNotComplete ==
Cannot show differences between changeset {0} and changeset {1}. The source changeset {0} has not been replicated to this repository. Make sure that you replicated the branch containing this changeset.

== CannotEditXlink ==
Can't edit the xlink because it does not exist at path: {0}

== CannotEditXlinkRepository ==
Can't edit the xlink target repository (changes in the name, server or relative server are not allowed).

== CannotEditXlinkType ==
Can't edit the xlink type.

== CannotEditXlinkWithChanges ==
Can't edit the xlink because it contains checkouts.

== CannotReleaseLocks ==
Cannot release the following locked items since the current user is not the administrator nor the owner of the locks: {0}

== CannotRemoveIostatsFolder ==
Error: unable to remove disk test folder. {0}

== CannotRetrieveArchivedData ==
Cannot retrieve the content of the revision {0} at repository {1} because it was archived, and its content is not available on the server.

== CannotRetrievePurgedData ==
Cannot retrieve the content of the revision {0} at repository {1} because it was purged, and its content is not available on the server.

== CannotSaveDataNotCo ==
Data can only be saved for a checked out revision.

== CannotUpdateAssigneeWithMultipleReviewers ==
The assignee could not be updated because the review has more than one reviewer.

== CannotUpdateWorkspaceOutOfDate ==
Cannot perform a partial update when the workspace is out of date.

== CantCheckSecurityRevisions ==
Can't check security for revisions

== CantCheckinSameData ==
Can't checkin same data for item {0}.

== CantCheckout ==
Can't checkout because there are exclusive checkouts

== CantCheckoutExclusive ==
Can't perform an exclusive checkout because there are existing checkouts

== CantConnectServer ==
Cannot connect to the server

== CantConvertSid ==
Can't convert given SID to a valid string format ({0}).

== CantDeleteDirectoryWithCheckouts ==
Directory {0} can't be removed because it contains checkouts.

== CantDiffBinaryFiles ==
The filetype of {0} is not supported to show differences.

== CantDiffBinaryFilesGeneric ==
The filetype of selected item is not supported to show differences.

== CantFindParentBranch ==
Can't find the parent of the branch {0} with id {1} (repository {2})

== CantLabelCheckedoutRevision ==
Can't label a checked out revision.

== CantLabelRevisionWithChangesetlabel ==
Can't label a revision with a changeset type label. Label {0}.

== CantLoadRootItem ==
Can't load the root item. The workspace selector probably contains errors.

== CantLocateChangesetToMoveInDatabase ==
Can't move changeset {0} because it no longer exists in the database.

== CantLocateChangesetToDeleteInDatabase ==
Can't delete changeset {0} because it no longer exists in the database.

== CantLocateMergeSourceChangesetInDatabase ==
Can't calculate merge because source changeset {0} no longer exists in the database.

== CantLocateMergeDestinationChangesetInDatabase ==
Can't calculate merge because changeset {0} no longer exists in the database.

== CantLocateMergeDestinationChangesetInDatabaseTryUpdateWk ==
Can't calculate merge because changeset {0} loaded in your workspace no longer exists in the database.
Update your workspace and retry the merge.

== CantLocateObjectInRep ==
Can't locate object {0} in repository.

== CantLocateParentRevisionForItem ==
Server can't locate parent revision to checkout the item {0}.

== CantLocateRevisionForItem ==
Selector can't locate a revision for the item {0}.

== CantLocateRevisionForItemNotRestore ==
Selector can't locate a revision for item {0}, it will not be restored.

== CantLocateSelectorForItem ==
Server could not locate a suitable selector for the item {0}.

== CantOverwriteControlledItems ==
The item {0} already exists in {1}. Can't overwrite controlled items. Try to delete it and repeat the operation.

== CantReadOrCreateFile ==
Can't read or write the file: '{0}'. Please check if you have write access or if you need admin privileges.

== CantRemoveBranchWithChangesets ==
Can't remove this branch because it has changesets.

== CantRemoveBranchWithChildBranches ==
Can't remove this branch because it has child branches.

== CantRemoveCheckedOut ==
Can't remove {0} because it is checked out.

== CantRestoreInDifferentRep ==
Can't restore an item from a different repository.

== CantRetrieveSymlinkTarget ==
Can't retrieve symlink target for {0}

== CantRetrieveWktree ==
Can't retrieve wkTree for wk '{0}' on '{1}'

== CantRevertRootItem ==
The root item can't be reverted

== CantSetAclItemsRevs ==
Can't set Acl for revisions or items

== CantSetDirType ==
Can't set the type 'directory' to a file.

== CantSpecifyABranchBaseForMainBranch ==
A branch base can't be set for the main repository branch.

== CantSpecifyBranchAsBaseForBranch ==
Branch {0} can't be set as base for branch {1} because a cycle relationship would be created. Check smart branch hierarchy first.

== CantSpecifyBranchBaseOnSameBranch ==
A smart branch base can't be on the same branch.

== CantSpecifyChangesetBaseOnSameBranch ==
Can't use the changeset {0} as base because it is on the same branch.

== CantSpecifyCsetAsBaseForBranch ==
Changeset {0} can't be set as base for branch {1} because a cycle relationship would be created. Check smart branch hierarchy first.

== CantSpecifyrevnoWithsmartbranch ==
Can't specify a revno rule if a smart branch is used. {0}.

== CantUseRegularlabelAsSmartbranchStartingpoint ==
Can't use a regular label type as starting point of a smart branch. {0}.

== ChangeDeleteConflict ==
Change delete conflict

== ChangeMustBeSelected ==
To select the change '{0}' the change '{1}' must be selected too.

== ChangepasswordCommandNotAvailable ==
This command is only available when security config is user/password.

== ChangepasswordCommandNotAvailableOnServer ==
This command is only available when security config is user/password and Plastic SCM Server is not configured to use user/password security mode.

== ChangesetAlreadyExists ==
The specified changeset {0} already exists.

== ChangesetDoesntexist ==
The specified changeset {0} does not exist.

== ShelveDoesntexist ==
The specified shelve {0} does not exist.

== ChangesetIsNotANumber ==
Specified changeset is not a number: {0}.

== ChangesetMoveDstBranchNotEmpty ==
The destination branch is not empty.

== CheckinRevNoCo ==
Revision {0} must be checked out in repository {1} to perform a check in!.

== CheckinNotLoadedItem ==
The item '{0}' is not loaded on the changeset you are working on (cs:{1}).
You most likely updated the workspace with the item cloaked, so it wasn't unloaded.
Please retry the check-in operation excluding this item, then update the entire workspace to fix any inconsistency.

== ChooseLanguage ==
Choose a language (type a number)

== ChooseType ==
Type of server (type a number)

== ChooseWorkingMode ==
Choose a mode (type a number)

== CiMissingRevisionData ==
The checkin operation could not be completed because an internal error occurred transferring the data. Please retry the checkin operation.

== ClientCallcontextNotavailable ==
The call context is not available. Aborting call.

== ClientVersionDoesntMatch ==
The client version ({0}) doesn't match the server version ({1}). Please update your client accordingly.

== ClientWillClose ==
{0} Unity VCS client can't continue and will be closed.

== CloudForbiddenError ==
Data cannot read/write to Unity VCS Cloud. There was an authentication issue. Your local date (client or server) is probably wrong. Change it to fix the issue.

== CmdCantMove ==
Can't move {0} to {1}.

== CmdCantMoveSamePath ==
Can't move {0}. The specified path is the same as the original. Please specify a different one.

== CmdCantMoveDstIsSubfolderSrc ==
Can't move {0} to {1}. Destination is a subfolder from source folder.

== CmdDirPrivate ==
{0} is a private directory. It can't be moved.

== CmdErrorGetrepositoryserver ==
Couldn't connect to the server {0}.

== CmdErrorItemInCi ==
Can't undo change for item {0}, it's checked in.

== CmdErrorPrivateItem ==
Can't perform operation. The item {0} is private.

== CmdErrorPrivatePath ==
Can't perform operation, current path is private.

== CmdGenericItemdoesnotexist ==
The item {0} does not exist in the workspace.

== CmdItemDoesNotExist ==
{0} does not exist.

== CmdErrorLabelIncorrectmarkerspec ==
Incorrect label spec: {0}

== CommentOutOfDate ==
The review comment being edited does not exist anymore. Please refresh your comments view.

== CommentTooLong ==
Comment size is too long. Its current length is {0}, whereas the maximum allowed length is {1}

== CommitFailedAndRollback ==
The operation can't be committed, and it was cancelled.

== CommonAncestorSameItem ==
Common Ancestor can only be calculated from revisions of the same item.

== CommonAncestorSameRev ==
Can't calculate common ancestor of the same revision.

== ConfiguringLanguage ==
Configuring language. These are the available languages.

== ConfiguringLdap ==
Configuring LDAP connection...

== ConfiguringUp ==
Configuring user and password settings ...

== ConfiguringWorkingMode ==
Select the users authentication mode. These are the available modes:

== ConflictCannotBeApplied ==
The conflict resolution cannot be applied because it depends on resolving other conflicts. Please try to resolve conflicts in a different order. The conflict resolution returned the following error: {0}

== ControlInManager ==
The control {0} is already in graphics manager.

== ControlNotPrepared ==
The control was not prepared yet. Try again.

== CouldGetInstanceOf ==
Fatal error: Couldn't get an instance of class {0}.

== CouldntGetRepositoryFromPath ==
Couldn't get a repository from {0}. The item may be private.

== CouldntLoadAsm ==
Can't load assembly {0}.

== CsNotFound ==
The changeset loaded in your workspace cannot be found (changeset ID: {0}). Probably someone deleted it. You need to undo your changes to restore a valid state.

== CycleMoveConflict ==
Cycle move conflict

== DatabaseInfoMoreThanOneRow ==
Table DATABASEINFO should have only one row.

== DeactivateUserNotInLicenseList ==
The user {0} is not an active user.

== DeleteBlobError ==
The data for revision {0} (segment {1}) cannot be deleted from rep {2}.

== GetSizeBlobError ==
The size of the data for revision {0} (segment {1}) cannot be obtained from rep {2}.

== DestNotFound ==
Destination object not found.

== DestinationAlreadyExist ==
Can't move. Destination {0} already exists. Overwriting items is not allowed

== DestinationDirectoryMustBeCheckedoutToMove ==
To move {0} the destination directory must be checked out.

== DestinationMustBeCheckedin ==
The move source '{0}' can't be checkedin without check-in the destination '{1}'.

== DirIdentical ==
Directories are identical.

== DirectoryAlreadyExists ==
Directory {0} already exists.

== DirectoryNotExist ==
The directory {0} does not exist.

== DivergentMoveConflict ==
Divergent move conflict

== DoesNotExist ==
{0} does not exist.

== DomainNotAvailable ==
Domain controller is not available.

== DuplicatedArg ==
Duplicated argument {0}.

== ElementIsNotCi ==
The element {0} is not checked in the current workspace.

== ElementIsNotCo ==
The element {0} is not checked out in the current workspace.

== English ==
English

== ErrorCantCreateDatabase ==
The database {0} can't be created. Check the server log (plastic.server.log). Error: {1}

== ErrorCantImportInvalidPackage ==
Invalid replication package. Invalid header.

== ErrorCantReplicateSameRepository ==
Error, cannot replicate in the same repository.

== ErrorCreatingBranchBaseNoCsetOnParentBranch ==
The branch base has not been set because the parent branch has no changesets on it. Maybe the parent branch is not a smart branch.

== ErrorDatabaseOpen ==
Plastic SCM server {0} wasn't able to open a connection to the database. Check the server log (plastic.server.log). Error: {1}

== ErrorExecutingQuery ==
There was an error executing the query {0}. {1}.

== ErrorExecutingUpgradeCommand ==
Error executing upgrade command: {0}.

== ErrorIncorrectRepserverspec ==
The server spec is not correct: {0}.

== ErrorLinkingMergeNoChangeset ==
Trying to merge-link an object that is not a changeset: ObjectId {0}.

== ErrorLinkingMergeNoLink ==
Trying to merge-link an object that is not a changeset or a interval merge-link: ObjectId {0}.

== ErrorNotValidTriggerType ==
The trigger type '{0}' is not valid for the specified server.

== ErrorOperation ==
The {0} operation finished with errors.

== ErrorOwnerNull ==
Error, the owner is null when leaving the cache.

== ErrorReadingRemotingFile ==
Error reading remoting configuration file: {0}.

== ErrorRemotingFileNotExist ==
Remoting configuration file {0} does not exist.

== ErrorServerconfighaserrors ==
The server configuration file {0} contains errors. Plastic Server is not correctly configured. Execute "plasticd configure" to configure server on text mode, or "configureserver" to launch the gui server configuration wizard. The error is {1}.

== ErrorServerconfignotfound ==
Server config file {0} not found. Plastic Server is not correctly configured. Execute "plasticd configure" to configure server on text mode, or "configureserver" to launch the gui server configuration wizard.

== ErrorSettingItemAttributes ==
There was a problem setting file attributes of item {0}: {1}

== ErrorSpecifiedBranchbasealreadyexists ==
The specified branch base for branch br:{0} already exists.

== ErrorTriggerException ==
Error trying to run trigger {0}-{1} {2} {3}.

== ErrorTriggerResult ==
The trigger {0}-{1} [{2}] failed. {3}.

== EverybodyKey ==
ALL USERS

== EvilTwinConflict ==
Evil twin conflict

== FailConnectionRepServer ==
Couldn't connect to repository server {0}.

== FailedCreateWorkspace ==
Failed to create workspace '{0}' on '{1}'

== FetchWithNoDataNotSupported ==
The server {0} doesn't support the fetch with no data. Please upgrade the server.

== FileAlreadyExists ==
File {0} already exists.

== FilesIdentical ==
Files are identical.

== FindSortingPaginationNotSupported ==
The server {0} doesn't support sorting or pagination. Please upgrade the server.

== FipsCompliantServerEncryptionNoAllowed ==
The server '{0}' requires encryption, but it's not allowed for FIPS compliant servers. <NAME_EMAIL> for further info.

== FormatAndXmlModifiersIncompatible ==
--xml and --format modifiers are not compatible and cannot be used together.

== GameUiCheckinConflictsError ==
The checkin/shelve operation cannot be completed because some local changes cannot be applied on the current server version. Please check the conflicts between your local changes and the current server version.

== GameUiCiHeadOutOfDateError ==
The checkin operation cannot be completed because new changes were created in the repository '{0}' since the operation began. Please retry the checkin operation to take into account the new server changes.

== GameuiCiInsideUnresolvedXlinkNotAllowed ==
The xlink '{0}' cannot be resolved. Changes under an unresolved xlink cannot be checked in.

== GetClientContext ==
Can't get client user and machine context.

== GetRevinfo ==
Could not get revision information for {0}.

== GenericInvalidCredentials ==
Invalid user credentials.

== HydrateNotSupported ==
The server {0} doesn't support hydrate. Please upgrade the server.

== IncompatibleAuthModes ==
The client authentication mode ({0}) doesn't match the server authentication mode ({1}).

== IncorrectAttClientSpec ==
Incorrect attribute specified: {0}.

== IncorrectBrClientSpec ==
Incorrect branch specified: {0}.

== IncorrectBrSpec ==
Incorrect branch specified.

== IncorrectCsClientSpec ==
Incorrect changeset specified: {0}.

== IncorrectLbClientSpec ==
Incorrect label specified: {0}.

== IncorrectRepClientSpec ==
Incorrect repository specified: {0}.

== IncorrectSpec ==
An incorrect spec has been specified.

== InvalidChildrenitemchangetype ==
Param type must be a valid ChildrenItemChangeType defined element.

== InvalidConnectionEncoding ==
The DB connection string has no valid encoding or none at all.

== InvalidCredentials ==
Active Directory or LDAP: Invalid credentials username, password or domain are not valid. Server error: {0}

== InvalidCsetToLabel ==
Cannot label the changeset {0}

== InvalidCulture ==
Can't change to this language. Previous language will be set.

== InvalidDatabaseVersion ==
Invalid database version: {0}.

== InvalidDb ==
The {0} provider is not supported in this edition.

== InvalidDiffchildtype ==
Param child must be a valid childType defined element.

== InvalidDomain ==
Active Directory: Invalid domain: {0}.

== InvalidEncoding ==
Specified encoding is not valid: {0}

== InvalidLanguage ==
Invalid language: {0}.

== InvalidLdapConfigData ==
Invalid parameters for LDAP in client config file.

== InvalidLdapType ==
Invalid LDAP server type: {0}.

== InvalidModuleName ==
Invalid module name {0}. It can't be empty and can't contain /

== InvalidOption ==
Invalid option.

== InvalidOs ==
The Operating System is not supported in this edition.

== InvalidParameter ==
Invalid parameter: {0}.

== InvalidParameterRange ==
Error: parameter {0} must be a value between {1} and {2}.

== InvalidRevisionType ==
Invalid revision type: {0}.

== InvalidServer ==
Active Directory: The server {0} is not valid.

== InvalidServerFormat ==
The server '{0}' has a not valid format. The server valid format is: [protocol://][organization@]server

== InvalidSname ==
Invalid value for server name.

== InvalidUpConfigData ==
Invalid parameters user/password in client config file.

== InvalidValue ==
Invalid value.

== InvalidWorkingMode ==
Invalid SEID working mode in configuration file.

== InvalidWorkingModeConf ==
Invalid working mode: {0}.

== InvalidWorkingModeWinseid ==
Trying to access library with an invalid mode: {0}.

== IsnotFile ==
{0} must be a file, not a directory.

== IsnotWk ==
{0} is not in a workspace.

== ItemAlreadyLoadedOnTree ==
The item {0} is already loaded on tree on path '{1}'.

== ItemCannotBeFoundOnTree ==
The item {0} cannot be found on tree (last known path for item: '{1}').

== ItemCannotBeMovedInsideItself ==
The item '{0}' cannot be moved inside itself '{1}'.

== ItemCantBeFoundOnDir ==
The item {0} couldn't be found in the directory.

== ItemExists ==
The item {0} already exists!

== ItemHandlerNotFound ==
Couldn't get ItemHandler from server {0}.

== ItemIsnotInWk ==
The item is not in a workspace.

== ItemLoadedTwiceOnTree ==
The item {0} is loaded twice at '{1}' and '{2}'. Please remove one of them.

== ItemLockOutOfDate ==
Cannot checkout '{0}' because you don't have the latest version. You will need to undo your changes to update to latest and lock. If you really need to preserve your changes, backup them manually. Please remember to lock this file in the future before making changes.

== ItemNotFound ==
Item {0} not found.

== ItemNotFoundInDirectory ==
The item {0} couldn't be found in the directory.

== ItemNotFoundInTree ==
Item {0} could not be found in the tree. The new tree cannot be built.

== ItemNotFoundOnVirtualAncestor ==
Item {0} not found in virtual ancestor.

== ItemRevisionCantBeFoundOnCset ==
The selected changeset doesn't contain a revision for the item

== ItempathNotInWk ==
Item path not in workspace: {0}.

== ItemsAlreadyLocked ==
These items are exclusively checked out by: {0}

== ItemSmartLockDifferentBranchOutOfDate ==
Cannot checkout '{0}' because you don't load the latest version in the branch '{1}'. You will need to undo your changes and merge from '{1}' or re-create your current branch '{2}' from latest changeset in '{1}' and lock. If you really need to preserve your changes, backup them manually.

== ItemSmartLockRetainedOutOfDate ==
Cannot checkout '{0}' because there is already an existing lock retained in branch '{1}'. You can only checkout the file from that branch or when the lock is released (branch '{1}' is merged in '{2}'). If you really need to preserve your changes, backup them manually.

== LabelDoesntexist ==
The specified label {0} does not exist.

== LbNotFound ==
The specified label can't be found.

== LdapDomain ==
LDAP domain

== LdapEmptyPassword ==
The provided password is empty

== LdapException ==
An error occurred in the LDAP server: {0}

== LdapPassword ==
LDAP password

== LdapPort ==
LDAP port [389]

== LdapReconnectException ==
Error trying to connect with LDAP server {0}

== LdapServer ==
LDAP server: {0}

== LdapUser ==
LDAP user

== LicenseDateExpired ==
Limited by date evaluation license has expired. Expiration date {0}.

== LicenseDaysExpired ==
Limited by days evaluation license has expired.

== LicenseNotFound ==
Plastic SCM server can't work without a license file.

== LicenseRemotelyDisabled ==
Your auto-renewal license has been disabled by an administrator. <NAME_EMAIL> for further info.

== LicenseVersionInvalid ==
Can't use a {0} license with a {1} edition of Plastic SCM.

== ListLocksByRepNotSupported ==
The server {0} doesn't support listing locks by repository. Please upgrade the server to use Smart Locks feature.

== LkNotFound ==
Couldn't find a link of type {0}.

== LoadSpec ==
Can't load the given spec.

== LoadedTwiceConflict ==
Item loaded twice conflict

== MergeBrNecessary ==
Can't perform a merge if no checkout branch is specified.

== MergeDestRevNotCo ==
The destination revision of a mergebranch must be checked out!.

== MergeCouldNotCheckRules ==
Cannot perform the merge. The Merge Rules system can't be initialized. Something unexpected happened. Contact support.

== MergeDidNotSatisfyRulesSrcBranchWasNotReviewed ==
Cannot perform the merge because of the Merge Rules. Review the branch '{0}' to merge it to '{1}'.

== MergeDidNotSatisfyRulesNoMatch ==
The current Merge Rules configuration doesn't allow branch '{0}' to be merged into '{1}'.

== MergeDidNotSatisfyRulesNotFromChildren ==
The current Merge Rules configuration only allows merges to branch '{0}' from its children, and branch '{1}' isn't a direct child.

== MergeDidNotSatisfyRulesNotFromParent ==
The current Merge Rules configuration only allows merges to branch '{0}' from its parent, and branch '{1}' isn't its parent branch.

== MergeDstMustBeCo ==
Merge destination is not checked out: ObjectId {0}.

== MergeNeeded ==
A merge is needed from changeset {0} to changeset {1} (the changeset you are currently loading) in order to checkin. The checkin operation cannot continue. It is necessary to solve the conflicts by merging your current workspace contents with the latest contents of the branch you are currently working on. Then, you can retry the checkin operation.

== MergeNotLocateDestRev ==
Can't locate destination revision of merge operation.

== MergeOpsCannotBeApplied ==
The following merge operations cannot be applied: {0}

== MergeToFoundConflicts ==
The "Merge-to" operation cannot be executed because the merge from changeset cs:{0} to branch br:{1} has conflicts.
It is necessary to run a "Merge from" operation from a workspace to resolve those conflicts.

== MergeToFoundEncryptedConflicts ==
The "Merge-to" operation cannot be executed because the merge from changeset cs:{0} to branch br:{1} has file conflicts that cannot be resolved as they are encrypted.
It is necessary to run a "Merge from" operation from a workspace to resolve those conflicts.

== MergeTypeNotFound ==
Can't find the merge type

== MergeUpdateWithLocallyChanged ==
The update operation cannot be performed because an item locally changed exists: {0}. Please try a merge.

== MergeWithNotLoadedDestRev ==
Merge destination revision can't be 'not loaded'.

== MissingExternalStorageConfiguration ==
The revision data is stored on an external storage, but no external storage was defined. Please add the external storage configuration on the server database configuration.

== MissingOrganization ==
No organization has been provided. You need to work against a specific organization.

== MkAlreadyExists ==
The label {0} already exists.

== MsgError ==
Error:

== MsgNoSelector ==
There is no selector for this workspace. {0}

== NoApplicationToOpen ==
There is no application selected to open the specified file {0}.

== NoCoBrFound ==
No checkout branch found.

== NoCommonAncestor ==
Cannot merge from source changeset {0} to destination changeset {1} in the repository {2}. A common ancestor could not be found. Make sure that you replicated the branch containing it.

== NoCommonAncestorBasedOnId ==
The common ancestor can't be computed based on a revision Id.

== NoContactRepInSelector ==
Could not contact repository in selector: {0}.

== NoDataInDomain ==
Can't search groups in this domain. Invalid domain. ({0}).

== NoDatabaseConfiguration ==
There is no database configuration for the requested provider: {0}.

== NoDomainControllerInAdworkingmode ==
Couldn't find a domain controller in session {0}. This account might not configured under an Active Directory Server. Change your configuration to LDAPWorkingMode and set LDAP credentials.

== NoPortInRemotingFile ==
There is no port specified in remoting configuration file.

== NoPreviousRevisionAvailable ==
There is no previous revision for this item.

== NoRepForClientSpec ==
Can't find repository for object '{0}'. The command has to either include a full object specification (including repository) like '{1}' or be run inside a workspace.

== NoRootRevision ==
Can't find the root revision. Id: {0}.

== NoRowsInQuery ==
The query did not return any row

== NoSelectorOnWks ==
Trying to work with an empty workspace selector.

== NoSuchObject ==
Can't get the GoupInfoObject.

== NoSuchUser ==
No such user: {0}.

== NoWorkspaceSelected ==
There is no workspace selected.

== NotChangedInCurrentWorkspace ==
The item '{0}' is not changed in current workspace.

== NotEmptyIostatsFolder ==
Error: disk test path not empty. Path: {0}

== NotEnoughDiskSizeIostatsFolder ==
Error: not enough disk space to perform disk test. Please specify another path or free up disk space. Path: {0}

== NotFoundGroupInGroupList ==
Referenced group '{0}' has not been found in group list.

== NotFoundUserInUserList ==
Referenced user '{0}' has not been found in user list.

== NotGroupedUsersKey ==
NOT GROUPED USERS

== NotImplementedYet ==
Not implemented yet.

== NotLoaded ==
{0} can't be loaded in the current workspace.

== NotValidDirectoryconflicttype ==
Param type must be a valid DirectoryConflictType defined element.

== NotValidFileconflictty ==
Param type must be a valid FileConflictType defined element.

== NotValidQuery ==
Invalid query.

== NotValidSpecializedtype ==
Remove type isn't a valid type for SpecializeType method.

== NullPath ==
Path cannot be null.

== NullRevInfoNoSelector ==
Could not obtain revision info for root item. Trying to work with an empty selector?

== ObjectLocked ==
The object is currently locked. Try later. {0}.

== WorkspaceLocked ==
An existing {1} operation has locked the workspace '{0}'. Please wait for the {1} operation to finish.

== ObjectWithoutGuid ==
The guid for the object {0} has not been found.

== OidcStateAuthenticationPending ==
The OpenID authentication is not finished.

== SamlStateAuthenticationPending ==
The SAML authentication is not finished.

== OnlyAdminCanAcceptCert ==
Only the server administrator can accept a certificate on the server.

== OnlyAdminCanConfPlasticTube ==
Only the server administrator can modify the Plastic Tube configuration.

== OnlyAdminCanExecuteAdminCommand ==
Only the server administrator can execute an admin command.

== OnlyAdminCanFetchWithNoData ==
Only the server administrator can fetch with no data.

== OnlyAdminCanCreateRemoveLocks ==
Only the server administrator can create or remove locks.

== OnlyAdminCanRenewLicense ==
Only the server administrator can renew the license.

== OnlyAdminCanSetServerEncryption ==
Only the server administrator can configure the encryption for a server.

== OnlyOneRevisionAvailable ==
Item has only one revision.

== OperationCanceled ==
Operation canceled by user.

== OperationNotApplicableToWorkspaces ==
This operation is not applicable to workspaces.

== OrganizationDbCannotBeCreated ==
The DB '{0}' for organization '{1}' cannot be created. Please try again later. If the problem persists, please contact with support.

== OrganizationDoesNotExist ==
Organization '{0}' doesn't exist.

== OrganizationIsDisabled ==
Organization '{0}' is disabled. Please activate it from your dashboard at plasticscm.com. Do not hesitate to contact <NAME_EMAIL> if you have any questions.

== OrganizationIsDeleting ==
Organization '{0}' is no longer available. Do not hesitate to contact <NAME_EMAIL> if you have any questions.

== OrganizationIsMigrating ==
We're currently upgrading cloud organization '{0}'. This process can take several hours, please try again later. For more information please contact <NAME_EMAIL>.

== OrganizationIsMissing ==
Missing organization. No organization found in the call.

== OrganizationInvalidStatus ==
Invalid status '{0}' in organization '{1}'.

== OrganizationMembersEmpty ==
Unable to retrieve Cloud authentication information. Please try again later. Contact <NAME_EMAIL> if the problem persists.

== OrphanFoldersDetected ==
{0} orphan folders detected in tree. Only 1 (the root) is expected

== OrphanTransaction ==
A previous operation was not properly finished. Please try again.

== OwnerKey ==
OWNER

== ParameterNotPresent ==
Parameter {0} is not present or is not valid.

== ParentCo ==
The parent directory {0} must be checked out to add {1}.

== ParentCoToRemove ==
To remove {0} the parent directory must be checked out.

== ParentCoToRestore ==
The parent directory {0} must be checked out to restore {1}.

== ParentDirectoryMustBeCheckedoutToMove ==
To move {0} the parent directory must be checked out.

== ParentNotInRep ==
Item {0} can't be added to repository because its parent {1} can't be loaded.

== ParentRepositoryDoesNotExist ==
Module can't be created because parent repository doesn't exist.

== PathAlreadyExistsBranchesCreate ==
Unable to create the secured path. There is another secured path (without tag) applied to the same list of branches.

A secured path is identified by its path and either an identificative tag or a list of branches when there is no tag.

Please modify the list of branches of the secured path you are about to create, or modify the equivalent secured path already created.

== PathAlreadyExistsBranchesUpdate ==
Unable to update the secured path. There is another secured path (without tag) applied to the same list of branches.

A secured path is identified by its path and either an identificative tag or a list of branches when there is no tag.

Please modify the list of branches of the secured path you are about to change, or modify the equivalent secured path.

== PathAlreadyExistsTagCreate ==
Unable to create the secured path. There is another secured path with the same tag.

A secured path is identified by its path and either an identificative tag or a list of branches when there is no tag.

Please use a different tag name for the secured path you are about to create.

== PathAlreadyExistsTagUpdate ==
Unable to update the secured path. There is another secured path with the same tag.

A secured path is identified by its path and either an identificative tag or a list of branches when there is no tag.

Please use a different tag name for the secured path you are about to change.

== PathNotInRep ==
The secured path can't be created. The path is under an xlink. Use the path permissions dialog of the xlinked repository: {0}.

== PathSpecMultipleMatches ==
Specified secured path: {0} matches with several secured path instances. Please provide a full secured path spec ( 'path#tag' or 'path' with '--branches=list_of_branches' argument ).


== ProblemLoadingExtension ==
There was a problem loading an extension.

== PrunedMergeLocalChgsCannotBeApplied ==
The merge cannot be done because some local changes cannot be processed. Most likely, you don’t have the proper permissions to make changes in some of the paths involved. Check the server log for more information.

== PrunedDiffLocalChgsCannotBeApplied ==
The diff with local changes cannot be calculated because some local changes cannot be processed. Most likely, you don’t have the proper permissions to make changes in some of the paths involved. Check the server log for more information.

== PrunedTreeNotAllowed ==
You don't have permissions to read the whole content inside the path '{0}' in the cs:{1}. The current operations needs to read the whole tree in order to continue. Please, review your path permissions and try again.

== PurgeCanOnlyBeExecutedByItsOwner ==
Only the author can execute the purge.

== PurgeCanOnlyBeDeletedByItsOwner ==
Only the author or the administrator can delete the purge.

== PushAllNotAllowedWithMissingData ==
Can't push the repository because the data of some of its revisions is missing (probably replicated with --nodata). Please hydrate the branches without data and try again (check the help of cm replicate hydrate).

== PushBrNotAllowedWithMissingData ==
Can't push '{0}' because the data of some of its revisions is missing (probably replicated with --nodata). Please hydrate the branch and try again (check the help of cm replicate hydrate).

== ReadBlobError ==
The data for revision {0} (segment {1}) cannot be read from rep {2}.

== RemotingFileCorrupt ==
Error reading/writing remoting configuration file: {0} (Unexpected contents).

== RenameModuleDifferentRep ==
You can't rename a module to a different repository.

== RenameModuleWithoutModuleName ==
You have to specify a module name to rename a module.
Remember it is in the form repo/module. Example: default/module2.

== RepAlreadyExists ==
The repository {0} for repository server {1} already exists.

== RepDoesntExist ==
The repository {0} doesn't exist. Probably, it was deleted and you are working with out-of-date metadata in the client side.

== RepDoesntHaveRootItem ==
Fatal, repository does not contain a root item.

== RepNeeded ==
The current path is not inside a workspace. Please specify a repository using the --repository option.

== RepNotFound ==
The specified repository couldn't be found: {0}.

== RepNotMatched ==
No repository matched the expression: {0}.

== RepdbDoesntExist ==
The repository database {0} doesn't exist on the database backend.

== RepdbIdIncorrect ==
The id specified in the repository database {0} is not correct. It should be a number.

== RepdbNameIncorrect ==
The specified repository database {0} doesn't have a correct name. It should start with {1}.

== ReplicationBrCantbelinked ==
The branch {0} (id:{1}) can't be linked. Aborting.

== ReplicationBrParentCantbelinked ==
The parent branch {0} of the branch {1} (id:{2} guid:{3}) can't be linked. Aborting.

== ReplicationChangesetdoesntexist ==
The specified changeset ({0}) to be used as starting point on replication doesn't exist. Aborting.

== ReplicationCsetBranchCantbelinked ==
The branch {0} of the changeset {1} (guid:{2}) can't be linked. Aborting.

== ReplicationCsetCantbelinked ==
The changeset {0} can't be linked. Aborting.

== ReplicationCsetParentCantbelinked ==
The parent changeset {0} of the changeset {1} (guid:{2}) can't be linked. Aborting.

== ReplicationCsetRootrevCantbelinked ==
The root revision {0} of the changeset {1} (guid:{2}) can't be linked. Aborting.

== ReplicationDataCorrupted ==
Replication data is corrupted and the operation can't be completed. Specific error: '{0}'.

== ReplicationDataEof ==
Can't read more data: End of file.

== ReplicationDirRevCantbelinked ==
The directory revision {0} can't be linked. Aborting.

== ReplicationErrorTryingtosetupauthdatafromfileFiledoesntexist ==
Error trying to set up authentication mode from file. File {0} doesn't exist.

== ReplicationErrorTryingtosetupauthdatafromfileNodata ==
Error trying to set up authentication mode from file. No data on file {0}.

== ReplicationErrorWrongauthmode ==
Wrong authentication mode specified. {0}.

== ReplicationErrorWrongentryintranslationtable ==
Error in translation table at line {0}.

== ReplicationItemCantbelinked ==
The item {0} (id:{1}) can't be linked. Aborting.

== ReplicationLabelCantbelinked ==
The label {0} (id:{1}) can't be linked. Aborting.

== ReplicationLinkCantbelinked ==
The link {0} (id:{1}) can't be linked. Aborting.

== ReplicationMsgBranchRenamed ==
The branch {0} already existed with a different ID, it has been created and renamed to {1}

== ReplicationMsgLabelRenamed ==
The label {0} already existed with a different ID, it has been created and renamed to {1}

== ReplicationParentbranchdoesntexist ==
The parent of branch to clone {0} ({1}) doesn't exist. Aborting.

== ReplicationRevCsetCantbelinked ==
The changeset {0} of the revision {1} (guid:{2}) can't be linked. Aborting.

== ReplicationRevItemCantbelinked ==
The item {0} of the revision {1} (guid:{2}) can't be linked. Aborting.

== ReplicationRevParentCantbelinked ==
The parent revision {0} of the revision {1} (guid:{2}) can't be linked. Aborting.

== RepositoriesDontMatch ==
The specified repositories don't match.

== RepositoryWasntUpgradedCorrectly ==
The repository {0} wasn't upgraded correctly, it can't be used until the problem is solved.

== RepspecsDontMatch ==
The changeset spec or path and the label spec don't refer to the same repository.

== RequestWkNotFound ==
Requested workspace cannot be found.

== ResolvespecItemNotFound ==
Item not found ({0}) in resolveSpec.

== RestoreItemidAlreadyExists ==
This item already exist on this path. It's not needed to restore it.

== RestoreItemnameAlreadyExists ==
Another item has this name on this path. A new name must be selected.

== RevIsNotANumber ==
Specified revision is not a number: {0}.

== RevNoCreated ==
A new revision couldn't be created. {0}.

== RevNotCreatedForCopy ==
Couldn't create a new revision for copy operation.

== RevNotFoundInRep ==
Revision {0} not found in repository {1}.

== RevisionCantBeFound ==
Can't find revision {0}.

== RevisionLoadedTwiceOnTree ==
The revision {0} is loaded twice on changeset {1}, at '{2}' and '{3}'. Please contact with support.

== RevisionNotCo ==
The revision {0} is not checked out.

== RevisionToReloadNotFound ==
The revision to reload was not found

== RevtreeNoRevsToShow ==
Can't show revision tree for item {0}.

== RootitemNumLocated ==
RootItem must be unique in the repository. {0} has been located.

== SaveBeforeAdd ==
You need to save it before add it to Unity VCS.

== ScriptVersionNotValid ==
The {0} version of {1} server is {2}. Please upgrade it to the {3} version.

== SecuredPathMovedCollission ==
The move from '{0}' to '{1}' is not allowed because the existing user permissions are different between the source path and destination path.

== SecurityAccessTokenDoesNotExist ==
The access token '{0}' does not exist or does not belong to you.

== SecurityAccessTokenExpired ==
The access token '{0}' expired.

== SecurityCantActivateUser ==
You don't have permissions to activate user {0}. You need to be repository server administrator in order to be allowed to complete this operation.

== SecurityCantArchive ==
You don't have permissions to archive revisions. You need to be repository server administrator in order to be allowed to complete this operation.

== SecurityCantCreateAccessToken ==
You don't have permissions to create access tokens. If you believe this to be an error, please contact your server administrator.

== SecurityCantDeactivateUser ==
You don't have permissions to deactivate user {0}. You need to be repository server administrator in order to be allowed to complete this operation.

== SecurityCantInheritItself ==
An object can't inherit from its own ACL. Recursion found.

== SecurityCantMergeChange ==
You don't have permissions to perform this merge. You don't have read permission for certain changes in the source contributor.

== SecurityCantRevealAccessToken ==
You don't have permissions to reveal access tokens. If you believe this to be an error, please contact your server administrator.

== SecurityCantUpdateReviewStatus ==
You don't have permission to update the status of the review. Only a reviewer or the repository server administrator can do it.

== SecurityCantRunExecquery ==
Only administrators can execute 'execquery'. You're not an administrator so you can't run 'execquery'.

== SecurityIdUnknown ==
The user id {0} is unknown and it cannot be translated.

== SecurityIncompatibleObjects ==
The object you have specified cannot inherit from the parent object. They are not compatible.

== SecurityInvalidPassword ==
Invalid password. Review your user/password configuration.

== SecurityTokenExpired ==
The auth token expired. User: {0}.

== SecurityNewParentInheritsFromChild ==
The object already inherits its security from the destination object. Recursion found.

== SecurityNoPermForOperation ==
You don't have permissions for operation {0}.

== SecurityNoPermForPathOperation ==
You don't have {0} permission on {1}.

== SecurityNoPermForRepoOperation ==
You don't have {0} permission for repository {1}.

== SecurityNoPermToCreateSuchSecuredPath ==
You don't have permission to create the secured path {0} on the specified repository and branches.

== SecurityObjectDoesntHaveAcl ==
The object doesn't own an ACL. Try selecting its parent.

== SecuritySeidIsInherited ==
The SEID is inherited. It can't be removed in this ACL.

== SecuritySeidNotInAclCantRemove ==
The specified SEID was not found in the object ACL. Check whether it is inherited.

== SecurityUserGroupUnknown ==
Unknown user or group: '{0}'. Please have in mind that user/group names are case sensitive!

== SecurityGroupUnknown ==
Unknown group: '{0}'. Please have in mind that group names are case sensitive!

== SecurityUserInactive ==
The user {0} appears as an inactive user because his/her license has been deactivated. Please activate it and then try again.

== SecurityUserUnknown ==
Unknown user: '{0}'. Please check your current credentials. Remember that user names are case-sensitive!

== SelFewInfo ==
Current selector does not contain enough information to perform checkin for item {0}.

== SelFewInfo2 ==
Current selector does not select any suitable revision for item {0}.

== SelNotHaveCo ==
Current selector does not provide a checkout branch for item {0}.

== SelectorhandlerNoWkMatchRequest ==
SelectorHandler: no workspace matches request.

== SelectorhandlerNoWkidMatch ==
SelectorHandler: no workspace matches the specified id: {0}.

== ServerEncryptionAlreadyDefined ==
The encryption for server '{0}' cannot be configured because it has already been configured.

== ServerEncryptionNeeded ==
The server '{0}' requires encryption, but your server is not configured with the encryption key. To configure your local server make sure you become the admin of the server (owner), try again and you will be asked to enter encryption key. Otherwise, ask your sysadmin or search 'Encrypt all data' in the online docu.

== ServerEncryptionWrongKey ==
The encryption key '{1}' for server '{0}' cannot be read. For more information check the server log.

== ServiceNotInitialized ==
The requested Plastic SCM operation is not yet available. Server is starting up or running upgrade operations.

== ShellParserQuoteError ==
Command arguments parse error. Check the quotes: {0}

== SizelimitExceeded ==
A LDAP query has exceeded the maximum allowed number of objects returned on a single search result. You must configure your LDAP server to increase this value.

== SotSpecDoesNotExist ==
The object {0} does not exist.

== SourceSameAsDest ==
The item is being moved to its current directory. Won't continue.

== Spanish ==
Spanish

== SrcNotFound ==
Source object not found.

== SwitchSelectorErrorInvalidRule ==
Invalid switch to branch/label/changeset rule specified

== SyncAttributeUnparseable ==
The git sync repository attribute cannot be properly parsed.

== SyncMissingParentChangeset ==
The parent changeset of the changeset {0} has not been replicated to this repository. This changeset and its descendants cannot be synchronized. Please exclude the involved branches or replicate the missing changesets.

== SyncMissingParentChangesets ==
The parent changeset of the changesets {0} has not been replicated to this repository. This changeset and its descendants cannot be synchronized. Please exclude the involved branches or replicate the missing changesets.

== SyncReplicaDifferentSources ==
Cannot replicate because the two Unity VCS repositories are in sync with git (or replicated from the originally synced repos). Only one repo can be in sync at the time. The repositories originally synchronized are '{0} - {1}' and '{2} - {3}'. Please contact support for further info.

== TaskUrlNotValid ==
The {0} has not been found or its version is not compatible.

== TestConnectionNotAvailable ==
Test connection not available. Probably the server and client configuration mode are not the same.

== TooManyFailedAuthenticationAttempts ==
Too many failed authentication attempts. Please try again later.

== TreeCantAddDuplicatedName ==
Can't add an entry with the same name. Duplicated child [{0}]. Parent [{1}]

== TriggerErrorMsg ==
{0}

== TriggerInvalidPosition ==
The specified position already exists for the type {0}-{1}.

== TriggerNotExists ==
The specified trigger does not exist.

== TryingToLockTwice ==
Can't perform an exclusive lock of the same item ({0}) from different paths '{1}' and '{2}'.

== TubeConfUnavailable ==
The Plastic Tube configuration is not available. Please check that your plastic server is correctly configured to use Plastic Tube. You can configure it through the 'cm tube config' command.

== UnexpectedError ==
There has been an unexpected error "{0}". For more information check the server log.

== UnlicensedTubeFeature ==
The Plastic Tube is unlicensed in your Plastic SCM server.

== UpCantAddGroupToItself ==
A group can't be added to itself.

== UpCantCreateNewGroupWithMembers ==
Can't create a new user with members.

== UpCorruptGroupInfo ==
Group information is corrupt: '{0}'.

== UpCorruptUserInfo ==
User information is corrupt: '{0}'.

== UpGroupAlreadyContainsGroup ==
Group '{0}' already contains group '{1}'.

== UpGroupAlreadyContainsUser ==
Group '{0}' already contains user '{1}'.

== UpGroupAlreadyExists ==
Group '{0}' already exists.

== UpGroupDoesntContainsGroup ==
Group '{0}' does not contain group '{1}'.

== UpGroupDoesntContainsUser ==
Group '{0}' does not contain user '{1}'.

== UpGroupNotExists ==
Group '{0}' does not exist.

== UpInvalidCredentials ==
Invalid credentials, username or password are not valid.

== UpInvalidGroupname ==
Invalid group name '{0}'.

== UpInvalidPassword ==
Invalid password.

== UpInvalidUsername ==
Invalid user name '{0}'.

== UpPassword ==
Password

== UpUserAlreadyExists ==
User '{0}' already exists.

== UpUserNotExists ==
User '{0}' does not exist.

== UpUsername ==
Username

== UpdateGetRevinfo ==
Could not get revision information for {0}. You may be referencing a non-existing item/branch/label. Or this element could not be labeled with chosen label, or you don't have read/view permission.

== UpdateToLatestNotValidCurrentBranch ==
Your workspace is loading a non-existing branch, changeset, label or shelveset. It is likely that somebody else deleted it. Please switch your workspace to an existing branch, changeset, label or shelveset.

== UpgradeMoreOneRep ==
More than one repserver used in the selector. This scenario is not supported by the workspace upgrade.

== UpgradeRunManualUpdate ==
Workspace Upgrade: root directory can't be found in previous data. Workspace will appear as private items. Run a manual update.

== UserAlreadyActivated ==
The user {0} has been already activated.

== UserAlreadyDeactivated ==
The user {0} has been already deactivated.

== UserLimitExceeded ==
The maximum number of user for this edition has been exceeded.

== UserPassProviderConfigMissing ==
User / password has not been configured. Please run the configuration wizard to set them up.

== UsersCannotObtained ==
System users cannot be obtained.

== UsersExceeded ==
The maximum number of licensed users has been exceeded.

== ViewUnknown ==
Unknown view type {0}

== WithoutMergeInfo ==
DirectoryChanges haven't got the additional information for merge.

== WkAlreadyExists ==
The workspace {0} ({1}) already exists.

== WkAlreadyExistsForUser ==
The workspace {0} of user {1} on machine {2} already exists.

== WkContainsOtherWk ==
Workspace {0} on machine {1} contains workspace {2}.

== WkContainsOtherWkOfUser ==
Workspace {0} on machine {1} contains workspace {2} of user {3}.

== WkDoesntExist ==
The workspace {0} does not exist.

== WktreeCorrupt ==
The workspace metadata file ({0}) is corrupt. Please update the workspace.

== WorkingModeDoesNotSupportUserPasswd ==
The {0} working mode does not support user and password configuration.

== WorkingModeNotInicialized ==
Security Working mode not initialized. Must be Name, Name+Id or LDAP.

== WorkingModeNotSupported ==
The {0} users configuration is not supported on this platform.

== WorkspaceNameAlreadyInUse ==
Workspace name '{0}' is already in use.

== WorkspaceRepAlreadyShared ==
Workspace '{0}' is already shared.

== WorkspaceRepAlreadyLocked ==
Workspace '{0}' is already locked by other concurrent operation.

== WorkspaceShareNotFound ==
Workspace '{0}' is not shared with user '{1}'.

== WorkspaceUvcsConnectionAlreadyExists ==
UVCS connection '{0}' already exists.

== WorkspaceUvcsConnectionDoesntExist ==
UVCS connection '{0}' does not exist.

== WorkspaceUvcsConnectionMetadataOutOfDate ==
Cannot update UVCS connection metadata '{0}' because there are new changes in the server (local id:{1}, server id:{2}).

== WriteBlobError ==
The data for revision {0} (segment {1}) cannot be written on rep {2}.

== WrongParentInCheckin ==
The file {0} is not in sync with the changeset you are working on (cs:{1}).
Most likely you switched to a different branch but the file had local changes and therefore wasn't updated.
Unity VCS can't checkin the file because it would incorrectly overwrite changes.
You can undo the changes in the file, update it, and then try to checkin again.

== XmlFileCorrupt ==
The file {0} contains errors and its configuration cannot be used. Please review its contents. Error: {1}

== MergeDstChangesNotAllowed ==
Cannot merge from source changeset {0} to destination changeset {1} in the repository {2} because there are destination changes and they are not allowed.

== FindAttrNotExist ==
The object type '{0}' is not valid on performed query.

== FindAttrTypeWrong ==
The value '{0}' is not valid for field '{1}' on query "{2}". The field '{1}' type is '{3}'.

== FindAttrTypeWrongValue ==
The value '{0}' is not valid for field '{1}' on query "{2}". The field '{1}' must have one of the following values : {3}

== Incorrectspec ==
Incorrect object specification {0}

== NotValidCmPath ==
The specified path: {0} is not a valid server path.

== FindOrderbyNotValid  ==
The object type {0} cannot order by {1}.

== FindTypeNotExists ==
The object type {0} doesn't exist

== FindListGuidConditionIsNotAlone ==
The condition of the list of guids is not compatible with any other condition. Only if this condition is alone, it will be allowed.

== FindParserError ==
Query error: {0}

== FindParserNullError ==
An error was found on the query

== ErrorPackageFromNodataRepo ==
Can't create a replication package from a repository with data replicated using the --nodata flag. Please hydrate the source repository first.

== ParentCommentOutDate ==
The comment you are trying to reply does not exist anymore. Please refresh your review comments.

== NoneOperationName ==
unspecified

== AddOperationName ==
add

== ApplyLocalChangesOperationName ==
apply local changes

== CheckinOperationName ==
checkin

== CheckoutOperationName ==
checkout

== CopyOperationName ==
copy

== DeleteOperationName ==
delete

== IncomingChangesOperationName ==
incoming changes

== MergeOperationName ==
merge

== MoveOperationName ==
move

== RebaseOperationName ==
rebase

== RevertOperationName ==
revert

== SetSelectorOperationName ==
set selector

== StatusOperationName ==
status

== TestOperationName ==
test

== UndeleteOperationName ==
undelete

== UndoOperationName ==
undo

== UpdateOperationName ==
update

== WorkspaceTreeOperationName ==
workspace tree

== BranchWithNoHead ==
The branch '{0}' is not valid because it does not have a Head. Please switch to a different branch.

== BranchHeadNotFound ==
Branch '{0}' has an invalid Head {1}. Most likely it was deleted. Please update your workspace.

== CantResolveRegionForCloudOrganization ==
Failed to resolve the cloud server for organization {0}. {1}

== BackupInProgress ==
The backup of the {0} database is in progress. Only read operations are allowed on it.

== OrganizationReadonlyEnabled ==
The organization '{0}' is in readonly mode. Only read operations are allowed on any of its repositories or configuration.

== OrganizationReadonlyEnabledUgoLimitExceeded ==
The organization '{0}' has exceeded its maximum free available storage and is now in read-only mode. Only read operations are allowed on any of its repositories or configuration. Please contact an administrator of your organization to upgrade your subscription and continue working with Unity Version Control. More info here: {1}.

== ServerReadonlyEnabled ==
The server is in readonly mode. Only read operations are allowed on any of its repositories or configuration.

== OrganizationMigratedError ==
The organization '{0}' was migrated to a new server.

== InvalidServerCertificateError ==
The server certificate is not valid.

== DeleteChangesetNotAllowedOnGitSyncRep ==
Deleting a changeset is not allowed on a repository that is on sync with Git.

== MoveChangesetNotAllowedOnGitSyncRep ==
Moving a changeset is not allowed on a repository that is on sync with Git.

== DeleteBranchAndChangesetsNotSupported ==
The server {0} doesn't support deleting a branch and its changesets at once. Please upgrade the server or delete the changesets one by one before deleting the branch.

== P4ExecutableDoesNotExist ==
Unable to find the Perforce CLI executable. Please ensure it's available in your PATH environment variable.

== CloudProviderTokenRequired ==
This server allows only token authentication

== CloudProviderProviderRequired ==
The organization '{0}' does not allow to login with '{1}' authentication

== CloudProviderInvalidTokenVersionDetected ==
Detected an invalid token version to try to connect to organization '{0}'

== FetchAllDeletedRevisionsError ==
Can't fetch the repository because some changeset was deleted while the fetch was in progress. Please try again.

== FetchBrDeletedRevisionsError ==
Can't fetch the branch '{0}' because some of its changesets was deleted while the fetch was in progress. Please try again.
