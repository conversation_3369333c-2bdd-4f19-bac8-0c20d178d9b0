== WrongRevFormat3D ==
指定的修訂格式無效

== AcceptResults ==
您是否要接受結果? (Y/N): 

== ActivatedUsers ==
作用中使用者總計:     {0}

== ActiveKey ==
作用中

== Add ==
控制檔案和目錄是否可新增至儲存庫

== AddAndMove ==
新增/移動衝突

== AddMoveConflictAction1 ==
保留兩種變更，將目的地重新命名為

== AddMoveConflictAction2 ==
保留來源變更 (保留新增，並捨棄移動)

== AddMoveConflictAction3 ==
保留目的地變更 (保留移動，並捨棄新增)

== AddNotApplicable ==
無法在工作區載入新項目。可能路徑已在使用中。請取消載入項目 (從設定檢視)，並重試此作業。

== Added ==
已新增

== AddedMovedConflictDestinationOperation ==
從 {0} 移至 {1}

== AddedMovedConflictExplanation ==
在來源已新增項目，而且在目的地已將其他項目移至相同位置。

== AddedMovedConflictSourceOperation ==
已新增 {0}

== AddedOldNameConflict ==
已新增項目，其名稱已在使用中。

== AddedSameNameConflict ==
已新增兩個具有相同名稱的項目

== AdminReadonlyEntered ==
伺服器已進入唯讀模式。

== AdminReadonlyEntering ==
伺服器正在進入唯讀模式。請稍等。

== AdminReadonlyLeaving ==
伺服器正在離開唯讀模式。請稍等。

== AdminReadonlyLeft ==
伺服器已離開唯讀模式。

== Advancedquery ==
控制進階查詢執行

== All ==
全部

== AllowedKey ==
已允許

== AlsoThreeOption ==
您也可以這麼做: 3. 將目的地重新命名為新名稱

== AncestorContributor ==
上層變更集: {0} (分支 {1})

== AppliedRename ==
將套用本地重新命名 {0} -> {1}。

== Applyattr ==
可讓使用者套用屬性

== Applylabel ==
可讓使用者套用標籤

== AskDeleteWorkspace ==
未指定工作區。您是否要刪除目前的工作區? (Y/N): 

== AskDeleteDynamicWorkspace ==
The workspace is dynamic. You will LOSE all of the private items in the workspace if you delete it. Do you want to continue? (Y/N): 

== AttValueField ==
屬性值

== AvailableUsers ==
可用使用者總計: {0}

== BinFileMetadataChangeset ==
變更集: {0}

== BinFileMetadataCreated ==
建立者: {0}

== BinFileMetadataModified ==
修改日期: {0}

== BinFileMetadataSize ==
大小: {0}

== BisyncAutoLinkedCommentFormat ==
變更集 {0} 和 {1} 已自動連結

== BisyncBranchAttributeError ==
分支屬性的格式不正確

== BisyncBranchBothChanges ==
分支 '{0}' 具有新的本地和外部變更 {1}

== BisyncBranchExcluded ==
- {0}: 已排除

== BisyncBranchForeignChanges ==
- {0}: 外部變更 {1}

== BisyncBranchLocalChanges ==
- {0}: 本地變更 {1}

== BisyncBranchMappedNotFound ==
找不到映射至參考 '{1}' 的分支 '{0}'。

== BisyncBranchUpToDate ==
- {0}: 最新 {1}

== BisyncBranches ==
分支

== BisyncBranchesInSync ==
分支均已同步。沒有要推送或提取的變更集。

== BisyncChangesetNotTranslated ==
無法轉譯變更集 {0}

== BisyncChangesetsInvolved ==
涉及變更集

== BisyncChangesetsNotSorted ==
無法正確排序要推送的變更集

== BisyncCheckCommit ==
檢查認可 

== BisyncCommitCheckFailed ==
認可檢查失敗

== BisyncCommitDiscarded ==
已匯入認可 '{0}'。

== BisyncCommitNotFound ==
Plastic 中找不到參考的認可 {0}

== BisyncCommitedForeignChanges ==
已認可的外部變更集 {0} (plastic cset:{1})

== BisyncCommitsCannotBeProcessed ==
有無法處理的認可

== BisyncComplete ==
已完成同步處理

== BisyncCompressing ==
正在壓縮物件

== BisyncConflictBranches ==
包含衝突的分支

== BisyncConnectException ==
連線至 Git 時發生錯誤: {0}

== BisyncCsetNotFoundWithGuid ==
找不到上次更新之帶有 GUID {0} 的 cset

== BisyncCtrlCDetected ==
已按下 Ctrl+C。請等候目前變更集完成同步處理，以避免資料不相符。之後，作業將會正常停止。

== BisyncDownloading ==
正在下載

== BisyncDownloadingMappings ==
正在從伺服器升級映射

== BisyncErrorPushingCs ==
推送 cs:{0} 時發生錯誤。{1}\n{2}

== BisyncExportComplete ==

匯出完成。

== BisyncExportedToCommit ==
已將 cs:{0} 匯出至認可 ({1})

== BisyncExporting ==
正在匯出

== BisyncForeignTreeNotRetrieved ==
無法擷取 rev:{0} 的外部樹狀目錄

== BisyncGetfileNotValid ==
git 提取者不支援依路徑進行的 GetFile 方法

== BisyncGettingInfo ==
接收參考

== BisyncGettingInfoDone ==
接收來自遠端伺服器的參考，已完成

== BisyncInvalidCredentials ==
為儲存庫引入的憑證無效

== BisyncInvalidNodeType ==
節點類型無效

== BisyncInvalidScm ==
已指定無效的 scm

== BisyncItemDiscardedWithoutRevid ==
帶有雜湊 '{1}' 的項目 '{0}' 沒有 revid 映射，因此，將予以捨棄。

== BisyncLastCsetForeign ==
外部 SCM 上的最新 cset: {0}

== BisyncLastEquivalenceFound ==
上次找到的同等項目 {0}

== BisyncLfsException ==
聯絡 GitHub LFS 時發生錯誤: {0}。您可以透過命令列 (cm sync) 使用 '--skipgitlfs' 選項來停用 Git LFS 支援。

== BisyncNoChangesPull ==
沒有從外部 SCM 提取的新修訂

== BisyncNoSettingsMultipleStore ==
未指定同步設定，而且已存放儲存庫 {0} 的多個設定。請指定要同步處理的 git 儲存庫。

== BisyncNoSettingsNoStore ==
未指定同步設定，而且未存放分支/儲存庫 {0} 的任何設定。

== BisyncNothingToCommit ==
沒有要認可的內容!

== BisyncObjectCount ==
物件計數

== BisyncPackaging ==
正在封裝...

== BisyncPendingChanges ==
沒有要推送和提取的變更。

== BisyncPendingChangesExplanation ==
將提取遠端變更，然後您必須將其合併，並將變更推回。

== BisyncProcessingObjects ==
正在處理物件:

== BisyncPullComplete ==
提取完成。

== BisyncPullCsetList ==
正在計算要提取的變更集...

== BisyncPullingChangesets ==
正在匯入

== BisyncPushCsetList ==
沒有要推送的變更。

== BisyncPushErrors ==
無法推送一些參考:

== BisyncReferenceWithoutSha ==
無法處理沒有 sha 的參考

== BisyncRemoteDeletedSkip ==
已刪除遠端儲存庫中的分支 '{0}'，將跳過。

== BisyncRemoteError ==

      遠端錯誤: {0}
    

== BisyncRemoteMappingSkip ==
分支 '{0}' 來自遠端標籤映射，將跳過。

== BisyncResults ==
結果:

== BisyncRevisionDiscardedNotRetrieved ==
無法擷取使用 sha '{1}' 的映射修訂 {0}，因此，將予以捨棄

== BisyncSettingsNotMatch ==
您引入的設定與針對分支/儲存庫 {0} 儲存的設定不相符。儲存的設定為: {1}。映射儲存在此處: {2}。僅限進階使用者: 可以刪除這些映射，並重新執行作業。

== BisyncShaNotFound ==
找不到 revid:{0} 的 sha

== BisyncShaNotValid ==
要處理的 sha 無效

== BisyncSyncStatusSaved ==
已儲存同步狀態: plastic cset {0} -> {1} 認可 {2}

== BisyncTagCannotBeApplied ==
無法套用修訂 '{1}' 的標籤 '{0}'。

== BisyncTreeNotRetrieved ==
無法擷取 cs:{0} 的樹狀目錄

== BisyncTypeWithoutBuilder ==
git 類型 {0} 沒有建置器

== BisyncUploadComplete ==
上傳完成。

== BisyncUploading ==
正在上傳... 

== BisyncWarnPushingCs ==
推送 cs:{0} 時出現警告。{1}\n{2}

== BisyncWrongBranchName ==
錯誤的分支名稱

== BisyncWrongFileFormat ==
檔案 '{0} 損毀。行 '{1}' 的格式錯誤。

== Branch ==
分支

== BranchHistoryEntryBase ==
基底變更集 (僅間隔合併): {0}

== BranchHistoryEntryDate ==
日期: {0}

== BranchHistoryEntryDestination ==
目的地變更集: {0}

== BranchHistoryEntryMergeType ==
合併類型: {0}

== BranchHistoryEntryOwner ==
擁有者: {0}

== BranchHistoryEntrySource ==
來源變更集: {0}

== BranchHistoryEntryType ==
類型: {0}

== CalculatingInitialChangeset ==
正在計算初始變更集

== CannotBeDeletedChanged ==
無法刪除磁碟上的項目 '{0}'，因為該項目已變更。

== CannotBeDownloadChanged ==
無法以新內容更新檔案 '{0}'，因為該檔案已在本機修改。

== CannotChangeRepoPartialSwitch ==
[部分切換] 命令無法變更工作區中設定的儲存庫。請將儲存庫規格從分支規則中移除。

== CannotCheckinIncomingChangesInProgress ==
進行傳入變更作業時，無法簽入。請先完成傳入變更作業，然後再重試簽入。

== CannotCheckinMergeInProgress ==
無法開始簽入作業，因為正在進行合併中: 請先完成簽入作業，然後再簽入變更。進行中的合併: {0} 來自 cset {1}

== CannotDeleteChangesetWithPendingChanges ==
有暫止的變更時，無法刪除目前工作區分支中的變更集。

== CannotDownloadRevision ==
無法從伺服器下載修訂 {0}: {1}

== CannotMakeTube ==
無法建立 Tube。{0}

== CannotMoveChangesetWithPendingChanges ==
有暫止的變更時，無法移動目前工作區分支中的變更集。

== CannotRemoveTube ==
無法移除 Tube。{0}

== CannotRestoreDeleteWithChangesInside ==
無法還原項目 '{0}'，因為刪除該項目時，當中有變更。請復原暫止的變更，並在沒有暫止的變更的情況下，或具有其他衝突解決方案的情況下，重複進行合併。

== CannotStartTubeSession ==
無法啟動 Plastic Tube 工作階段。{0}

== CannotUnlockItems ==
無法解除鎖定下列項目，因為目前使用者不是管理員，也不是鎖定的擁有者: {0}

== CantExecuteAdvQuery ==
無法執行查詢。您目前的目錄可能是私人目錄。

== Change ==
控制檔案和目錄是否可在儲存庫中修改

== ChangeBoth ==
您必須重新命名這兩個元素

== ChangeDelete ==
變更/刪除衝突

== ChangeDeleteConflictActions11 ==
保留來源變更 (保留新增，並捨棄刪除)

== ChangeDeleteConflictActions12 ==
保留目的地變更 (保留刪除，並捨棄新增)

== ChangeDeleteConflictActions21 ==
保留來源變更 (保留變更，並捨棄刪除)

== ChangeDeleteConflictActions22 ==
保留目的地變更 (保留刪除，並捨棄變更)

== ChangeDeleteConflictDestinationOperation ==
已刪除 {0}

== ChangeDeleteConflictExplanation ==
已在來源上{0}項目，而且目的地已刪除該項目或其上層

== ChangeDependencies ==
變更: {0}。相依性:

== ChangeReviewNotFound ==
找不到下列檢閱變更要求 (在簽入註解中指定)，或已在先前的變更集中套用: {0}{1}

== Changecomment ==
可讓使用者變更註解

== Changed ==
已變更

== ChangelistBuiltIn ==
內建

== ChangelistCherryPickSubtractive ==
削減揀選

== ChangelistCherryPicking ==
揀選

== ChangelistDefaultComment ==
預設 Unity VCS 變更清單

== ChangelistHiddenComment ==
包含使用者定義隱藏變更的變更清單

== ChangelistIntervalCherryPick ==
間隔揀選

== ChangelistIntervalCherryPickSubtractive ==
削減間隔揀選

== ChangelistManagementChanged ==
已成功編輯變更清單 '{0}'。

== ChangelistManagementCreated ==
已建立變更清單 '{0}'。

== ChangelistManagementDeleted ==
已成功移除變更清單 '{0}'。

== ChangelistMerge ==
合併

== ChangelistMergeComment ==
合併程序影響的檔案

== ChangelistMergeName ==
{0} 來自 cs:{1}

== Changeset ==
變更集

== CheckedOutKey ==
已簽出

== CheckinParallelMsg ==
多執行緒簽入

== RemainingMsg ==
剩餘

== CheckinParallelUploadNumThreads ==
正在平行上傳 {0} 個區塊

== CheckinProgressUploadingFileData ==
正在將檔案 {0} ({1}) 上傳至儲存庫

== CheckinStatusCancelling ==
正在取消簽入作業

== CheckinStatusConfirming ==
正在確認簽入作業

== CheckinStatusFinished ==
簽入已完成

== CheckinStatusGeneratingdata ==
正在組合簽入資料

== CheckinStatusRestoringpermissions ==
正在還原檔案存取

== CheckinStatusStarting ==
正在啟動簽入作業...

== CheckinStatusUploading ==
正在上傳檔案資料

== CheckinStatusValidating ==
正在驗證簽入資料

== CheckoutCannotBeSaved ==
無法儲存項目 {0} 的內容。

== CherryPick ==
揀選

== Chgowner ==
控制變更擁有者作業

== Chgperm ==
控制變更權限作業

== ChooseResolution ==
請選擇此衝突的解決方案。您想保留哪一個作業?

== Ci ==
控制簽入作業

== CleanDiffCalcMerges ==
正在計算合併至分支 br:{0}@{1}@{2}

== CleanDiffNotifyFinish ==
跳過合併的差異

== CleanDiffNotifyTotal ==
跳過 {0} 個變更集 (合併目的地) 的差異

== CleanDiffProcessCset ==
跳過 cs 的差異:{0}@{1}@{2}

== Cloaked ==
已遮蔽

== CloneDstRepositoryAlreadyExists ==
目的地儲存庫 '{0}' 已存在，而且該儲存庫是空的。

== CloneDstRepositoryCreated ==
目的地儲存庫 '{0}' 已建立。

== CmUndoIncompatibleFlags ==
標誌 '--silent' 和 '--machinereadable' 不相容。

== CmdArchiveErrorRestore ==
還原封存資料時發生錯誤。請記住，您必須是 Unity VCS 伺服器管理員，才能還原封存資料。{0}

== CmdAskMkworkspaceConfirmdeletedir ==
將刪除目錄 {0}。您是否確定 [Y|N]:

== CmdCannotBePerformedInPartialWk ==
工作區是 Gluon 格式，而且需要轉換至「標準」。請執行更新以修正此問題

== CmdCannotBePerformedInStandardWk ==
工作區不是 Gluon 格式。請執行更新以修正此問題。目前是「標準」儲存庫 (也許您從一般 Unity VCS 已使用該儲存庫)。請從 Gluon 執行更新，以修正此問題 (或使用 cm partial)

== CmdErrorAclNoSuchUserOrGroup ==
沒有此類的使用者或群組

== CmdErrorCannotMoveDynamicWk ==
無法移動動態工作區。

== CmdErrorGetfileCantWriteOutput ==
錯誤: 存取路徑 {0} 遭拒，因為檔案是唯讀

== CmdErrorGetfileRevnotfound ==
找不到指定的修訂 {0}

== CmdErrorIncorrecBrspec ==
不正確的分支規格

== CmdErrorIncorrecWkspec ==
不正確的工作區規格

== CmdErrorIncorrectCodeReviewId ==
不正確的程式碼檢閱 ID: {0}

== CmdErrorLabelIncorrectmarkerspec ==
不正確的標籤規格: {0}

== CmdErrorLabelItemNotFound ==
無法套用標籤 {0}。項目是私人的。

== CmdErrorLabelMarkerspecnotfound ==
找不到特定的標籤規格。{0}

== CmdErrorListrepRepserverunknown ==
未知的儲存庫伺服器

== CmdErrorMergeWithModifiedItems ==

    您的工作區有待檢查的變更。
    建議您在合併前先簽入，以避免
復原合併時可能出現的問題。

    如果您熟悉合併運作方式，您可以將
下列金鑰新增至 'client.conf' 檔案，以停用此行為:

        <MergeWithPendingChanges>是</MergeWithPendingChanges>

    請注意: 我們已變更此行為，以避免對新使用者造成問題，
然而若是您熟悉合併的運作方式，啟用它並沒有危險。
    

== CmdErrorNoSuchBranch ==
分支 {0} 不存在於儲存庫 {1} 中。

== CmdErrorNoSuchTypeTrigger ==
類型 {0} 無效。請記住，其格式是子類型-類型。

== CmdErrorNoSuchUser ==
使用者 {0} 不存在

== CmdErrorNoSuchUserDuCommandHint ==
也許您必須使用標誌 "--nosolveuser" 以停用驗證系統中已不存在的使用者。

== CmdErrorNoWkFoundFor ==
找不到 {0} 的工作區

== CmdErrorNotLocateItemhandler ==
找不到有效的 ItemHandler。

== CmdErrorRevertRevnotfound ==
找不到要恢復的指定修訂: {0}

== CmdErrorUndocheckoutCantfindrev ==
找不到指定的修訂 {0}

== CmdErrorUndocheckoutIdNotCheckedout ==
指定的修訂未簽出 {0}

== CmdErrorUndocheckoutIncorrectspec ==
無效的簽出規格: {0}

== CmdErrorUpdateProcessingItem ==
更新 {0} 時發生錯誤。{1}

== CmdErrorWkNotCreatedOnServer ==
無法建立工作區

== CmdMandatoryParameter ==
{0}: 缺少所需的參數 {1}

== CmdMessageAddIgnoredError ==
無法新增項目 {0}。錯誤: {1}

== CmdMessageAddItemadded ==
項目 {0} 已正確新增

== CmdMessageCiIgnoredError ==
無法簽入項目 {0}。錯誤: {1}

== CmdMessageItemcheckout ==
項目 {0} 已正確簽出

== CmdMessageManipulateselectorNoselector ==
未指定選擇器。

== CmdMessageMkworkspaceCorrectlycreated ==
工作區 {0} 已正確建立

== CmdMessageMkworkspaceDynamicWaitToMount ==
plasticfs 可能還需要幾秒鐘的時間來裝載您的新工作區

== CmdMessageMkworkspaceDynamicRequiredParams ==
建立動態工作區時，您必須指定 '--dynamic' 與 '--tree=[tree]' 參數。如需詳細資訊，請參閱 'cm workspace create --help'。

== CmdMessageNoworkspacesfound ==
此機器中沒有工作區。

== CmdMessageNoworkspacesfoundInPath ==
在路徑 {0} 中找不到工作區。

== CmdMessageProceedAdd ==
即將新增選取的項目。請稍候...

== CmdMessageProceedCheckdb ==
檢查資料庫完整性可能需要一些時間。請稍候...

== CmdMessageProceedCheckin ==
即將簽入選取的項目。請稍候...

== CmdMessageProceedCheckout ==
即將簽出選取的項目。請稍候...

== CmdMessageRemoveItem ==
項目 {0} 已移除。

== CmdMessageShowselectorNoselector ==
此工作區中沒有可用的選擇器

== CmdMessageWorkspacedeleted ==
工作區 {0} 已刪除。

== CmdMktriggerPosition ==
已在位置 {0} 上建立觸發器。

== CmdMoved ==
{0} 已移至 {1}

== CmdMsgCopiedFromRep ==
正在從 {1} 下載檔案 {0}

== CmdMsgCreateDir ==
已建立目錄 {0}

== CmdMsgFileAlreadyExistsInWk ==
檔案 {0} 已存在工作區中

== CmdMsgFileChangedInWk ==
工作區中的檔案 {0} 已變更。將不會覆寫。

== CmdMsgFileChangedInWkToRm ==
工作區中的檔案 {0} 已變更。將不會移除。

== CmdMsgFileDateNotChangedInWk ==
工作區中檔案 {0} 的日期未修改

== CmdMsgItemCantLabelNoLabelOnRep ==
修訂 {0} 所在的儲存庫未包含標籤

== CmdMsgLabelCantLabelOtherRep ==
修訂 {0} 與所選取的標籤位於相同的儲存庫

== CmdMsgLabelCorrectlyLabeled ==
變更集 {0} 目前已加上標籤。

== CmdMsgLsNotloaded ==
未載入

== CmdMsgMergeCannotmergecloaked ==
將不會合併元素 {0}，因為該元素已遭鎖定。

== CmdMsgMergeCannotmergedir ==
元素 {0} 已捨棄合併，因為該元素已在合併過程中遭到刪除。

== CmdMsgMergeCannotmergefile ==
元素 {0} 已捨棄合併，因為該元素已在合併過程中遭到刪除。

== CmdMsgMergeDone ==
合併完成

== CmdMsgMergeGoingtocopymerge ==
複製合併 {0}

== CmdMsgMergeGoingtomerge ==
正在合併 {0}

== CmdMsgMergeInvalidinterval ==
選取的變更集間隔無效

== CmdMsgMergeMergingmoveddir ==
目錄 {0} 已移至 {1}，而且正在合併

== CmdMsgMergeMergingmovedfile ==
檔案 {0} 已移至 {1}，而且即將合併

== CmdMsgMergeNomergesdetected ==
未偵測到合併

== CmdMsgMergeNotconnected ==
必須連接合併來源和目的地，才能執行削減合併

== CmdMsgNocheckoutsfound ==
找不到簽出

== CmdMsgNopathfound ==
無法解析路徑

== CmdMsgRenaming ==
將 {0} 重新命名為 {1}

== CmdMsgUpdateStoringCheckout ==
儲存 {0} 的已簽出資料

== CmdMsgUpdateWontOverwriteCo ==
更新不會置換已簽出的檔案。{0}

== CmdMsgWkinfo ==
工作區 {0} 的選擇器:

== CmdMsgWontOverwriteCheckout ==
將不置換已簽出檔案。{0}

== CmdNoLicensedUsers ==
沒有作用中使用者。使用者在系統上執行第一次作業時才會啟用。

== CmdPatchRequiresDiff ==
找不到差異比對，請從下列網址下載: 
http://gnuwin32.sourceforge.net/packages/diffutils.htm

 安裝後，請將其新增至 PATH 環境變數，或使用 
--tool 參數，以指定 diff.exe 的位置。

== CmdPatchRequiresPatch ==
找不到修補程式，請從下列網址下載:
http://gnuwin32.sourceforge.net/packages/patch.htm

安裝後，請將其新增至 PATH 環境變數，或使用 
--tool 參數，以指定 patch.exe 的位置。

== CmdRepServerResultCheckdb ==
儲存庫伺服器: {0}

== CmdRepoResultCheckdb ==
儲存庫: {0}

== CmdSetselectorEndwithdot ==
從 stdin 讀取選擇器設定，單一行以 '.' 結尾

== CmdStatusIncompatibleOptionsShortAndXml ==
--short 和 --xml 選項無法一起使用，因為 --short 未載入詳細狀態資訊。

== CmdUnchangeDone ==
{0} 復原變更已完成。

== CmdUncheckedOut ==
{0} 未正確簽出

== CmdUnexpectedOption ==
{0}: 未預期的選項 {1}

== CmdUpdated ==
{0} 已正確更新

== CommandFinishedSuccesfully ==
命令已順利完成

== Comment ==
註解

== Committing ==
正在簽入

== Configlocks ==
允許新增新鎖定規則

== ConfirmNewPassword ==
確認密碼: 

== ConnectTubeSucceeded ==
Unity VCS 伺服器 ({0}) 已成功連線至 Plastic Tube。

== ConnectionFail ==
嘗試連線至 Unity VCS 伺服器時，測試連線發生問題

== ConnectionOk ==
測試連線已成功執行

== ContinueSolving ==
剩下 {0} 個衝突，您是否要繼續解決衝突? (Y/N): 

== ContinueWithPendingChangesQuestion ==
您的工作區中有變更的檔案。您可以取消切換作業，以檢查變更的檔案，也可以繼續進行作業。如果您繼續進行切換作業，您的已變更檔案將不會遺失，並且在切換作業之後，仍會顯示為已變更。您是否要繼續進行切換作業? (y/n)

== Controlled ==
受控制

== Copied ==
已複製 (新)

== CopiedCannotBeApplied ==
無法複製項目 '{0}'，因為發生錯誤: '{1}'。

== CopyMergeIsRealMerge ==
項目 {0} 目前已載入工作區，所以該項目需要合併，而非複製合併。

== CopyMergeNeeded ==
{1} 的項目 {0} 需要複製合併

== CopyMergePromoted ==
項目 {0} 的複製合併已升級為合併

== CopyingMergeFiles ==
正在複製合併檔案...

== CreatedChangesetNumber ==
已建立變更集 {0}

== CreatedOn ==
已建立

== CreatedShelveNumber ==
已建立擱置 {0}

== CsetNumber ==
變更集號碼

== CurrentOutputDirectory ==
目前的輸出目錄是

== CycleMove ==
週期的移動衝突

== CycleMoveConflictActions1 ==
保留在來源完成的移動 (捨棄在目的地移動)

== CycleMoveConflictActions2 ==
保留在目的地完成的移動 (捨棄在來源移動)

== CycleMoveConflictDestinationOperation ==
從 {0} 移至 {1}

== CycleMoveConflictExplanation ==
來源和目的地已移動兩個項目並發生碰撞，因為它們已建立週期。

== CycleMoveConflictSourceOperation ==
從 {0} 移至 {1}

== DataWritten ==
資料已寫入

== DefaultFormatDiffmetrics ==
已變更: {0}，已新增: {1}，已刪除: {2}

== DeleteAndChange ==
刪除/變更衝突

== DeleteAndMove ==
刪除/移動衝突

== DeleteChangeConflictActions11 ==
保留來源變更 (保留刪除，並捨棄新增)

== DeleteChangeConflictActions12 ==
保留目的地變更 (保留新增，並捨棄刪除)

== DeleteChangeConflictActions21 ==
保留來源變更 (在目的地保留刪除，並捨棄變更)

== DeleteChangeConflictActions22 ==
保留目的地變更 (保留變更，並捨棄刪除)

== DeleteChangeConflictExplanation ==
已在來源刪除項目，在目的地已{0}該項目。

== DeleteChangeConflictSourceOperation ==
已刪除 {0}

== DeleteChangesetDiscardAllChangesNotInteractive ==
有人已刪除您正在使用中的變更集。
這表示您的簽出和本地變更已不再有效。

這是由於基底變更集已消失，所以無法找到實際變更。

您需要復原所有變更，並將您的工作區更新為有效設定。
可以在互動模式中達成此作業 (例如，沒有 stdin 重新導向)。

== DeleteChangesetDiscardAllChangesQuestion ==
有人已刪除您正在使用中的變更集。
這表示您的簽出和本地變更已不再有效。

這是由於基底變更集已消失，所以無法找到實際變更。

您是否要復原所有變更，並將您的工作區更新為有效設定? (y/n)

== DeleteDelete ==
刪除刪除警告

== DeleteDeleteConflictDestinationOperation ==
已刪除 {0}

== DeleteDeleteConflictSourceOperation ==
已刪除 {0}

== DeleteDeleteWarningMessage ==
此項目已捨棄，因為該項目已在來源和目的地刪除

== DeleteMoveConflictActions1 ==
保留來源變更 (保留刪除，並捨棄移動)

== DeleteMoveConflictActions2 ==
保留目的地變更 (保留移動，並捨棄刪除)

== DeleteMoveConflictDestinationOperation ==
從 {0} 移至 {1}

== DeleteMoveConflictExplanation ==
已在來源刪除項目，而且該項目已在目的地移動。

== DeleteMoveConflictSourceOperation ==
已刪除 {0}

== DeleteNotApplicable ==
因為工作區中找不到此項目，所以無法載入項目。您的工作區可能已損毀。請聯絡支援部門。 

== DeletePrivateDeletesSummary ==
已刪除 {0} 個個別檔案和 {1} 個樹狀目錄。

== DeletePrivateDirectoryFailed ==
無法刪除控制的目錄: {0}

== DeletePrivateDryrun ==
請注意: 這是試執行。未刪除任何檔案。

== DeletePrivateFailuresSummary ==
無法刪除 {0} 個個別檔案和 {1} 個樹狀目錄。

== DeletePrivateFileFailed ==
無法刪除控制的檔案: {0}

== DeletePrivateSkipControlledDir ==
已跳過控制的目錄: {0}

== DeletePrivateSkipControlledFile ==
已跳過控制的檔案: {0}

== DeletePrivateSkipIgnoredDir ==
已跳過忽略的目錄: {0}

== DeletePrivateSkipIgnoredFile ==
已跳過忽略的檔案: {0}

== DeletePrivateSkipMissingFileOrDirectory ==
已跳過缺少的檔案或目錄: {0}

== Deleted ==
已刪除

== DeletedPrivateDirectory ==
已刪除目錄: {0}

== DeletedPrivateFile ==
已刪除檔案: {0}

== DeniedKey ==
已拒絕

== DependenciesApplyLocalChanges ==
套用本地變更

== DependenciesCheckin ==
簽入

== DependenciesDescription ==
選取要{0}的部分項目依存於其他也需要包含在此作業中的項目。所有相依項目路徑應該包含在指定的 {0} 路徑。選項 "--dependencies" 也可以用於自動包含所有相依項目。

== DependenciesShelve ==
擱置

== DependenciesUndoChanges ==
復原變更

== Destination ==
目的地

== DestinationRevisionNotFound ==
找不到要升級合併的目的地修訂

== DiffNotDownloadedRevision ==
無法從伺服器下載修訂 {0}。請重試選取不同的檔案，然後再返回此修訂。

== DirConflictCannotBeApplied ==
無法套用來源:'{1}' 和目的地:'{2}' 的衝突 {0}，因為發生錯誤: '{3}'。

== DiscardedAddedWarningMessage ==
已捨棄此項目，因為來源已新增此項目，但項目已載入目的地，所以不需要新增此項目

== DiscardedChangedWarningMessage ==
已捨棄此項目，因為修訂衝突未從來源變更至目的地，或因為來源和目的地載入相同修訂

== DiscardedDeleteWarningMessage ==
已捨棄此項目，因為來源已刪除此項目，但未載入目的地，所以無法刪除此項目

== DiscardedFsProtectionFormat ==
將項目 {0} 的 FS 保護從 {1} 捨棄，因為 {2}

== DiscardedFsProtectionWarningMessage ==
已捨棄此項目，因為其檔案系統保護在來源和目的地中已變更為相同值，來源已變更 fs prots，並且已在目的地刪除，或來源已刪除項目，且目的地已變更 fsprot

== DiscardedMergeConnectedrevision ==
來源修訂已與目的地修訂連接。

== DiscardedMergeFormat ==
已捨棄從 {1} 對項目 {0} 進行合併，因為 {2}

== DiscardedMergeNotconnectrevision ==
來源修訂未與目的地修訂連接。

== DiscardedMergeNotloaded ==
修訂無法再載入 (元素已移除)。

== DiscardedMergeSamerevision ==
來源修訂與工作區中載入的修訂相同。

== DiscardedMovedWarningMessage ==
此項目已捨棄，因為項目已移動至來源和目的地中的相同位置，或已在目的地載入項目

== DiscardedSubtractiveFormat ==
已捨棄從 {1} 對項目 {0} 進行削減合併，因為 {2}

== DisconnectTubeSucceeded ==
Unity VCS 伺服器 ({0}) 已成功從 Plastic Tube 中斷連線。

== DivergentMove ==
分歧的移動衝突

== DivergentMoveConflictActions1 ==
保留在來源完成的移動 (捨棄在目的地移動)

== DivergentMoveConflictActions2 ==
保留在目的地完成的移動 (捨棄在來源移動)

== DivergentMoveConflictDestinationOperation ==
從 {0} 移至 {1}

== DivergentMoveConflictExplanation ==
已在來源和目的地將項目移至兩個不同的位置。

== DivergentMoveConflictSourceOperation ==
從 {0} 移至 {1}

== DownloadMissingFileNotFoundOnSource ==
無法下載 '{0}' (revid:{1})。可能已使用 --nodata 複寫，但在複寫來源 {2}@{3} 中找不到其資料。

== DownloadMissingFileReplicationSourceGuidsResolutionMethodNotAvailable ==
無法下載 '{0}' (revid:{1})。可能已使用 --nodata 複寫，但未升級其複寫來源 {2}@{3}，因此，所需的 API 無法使用。請升級伺服器 {3}。

== DownloadMissingFileReplicationSourceNotAvailable ==
無法下載 '{0}' (revid:{1})。可能已使用 --nodata 複寫，但其複寫來源 {2}@{3} 無法使用。

== DownloadMissingFileWithoutReplicationSource ==
無法下載 '{0}' (revid:{1})。可能已使用 --nodata 複寫，但是在儲存庫 {2}@{3} 中無法使用。

== DstContributor ==
目的地變更集: {0} (分支 {1})

== ElementNewName ==
具有新名稱 {0} 的元素

== ElementOldNewName ==
具有舊名稱 {0} 和新名稱 {1} 的元素

== EnterNewDestinationName ==
請為目的地輸入新名稱:

== Error ==
錯誤

== ErrorCheckingIn ==
簽入 {0} 時發生錯誤。{1}

== ErrorCloneDstRepNotEmpty ==
目的地儲存庫 '{0}' 不是空的，因此無法繼續進行複製作業。

== ErrorCloneDstRepNotFound ==
無法確定複製作業的目的地儲存庫。

== ErrorClonePackageNotValid ==
如果已指定目的地，則無法使用選項 --package。

== ErrorCloneSourceRepoNotSpecified ==
未指定來源儲存庫。

== ErrorCloneSrcAndDstEquals ==
來源和目的地儲存庫相同 ('{0}')。

== ErrorCloneSrcRepNotFound ==
無法從 '{0}' 確定複製作業的來源儲存庫。

== ErrorExecutingQuery ==
執行查詢時發生錯誤: 

== ErrorImportingCommit ==
無法匯入變更集 '{0}'。錯誤: {1} 請聯絡支援團隊。

== ErrorLaunchingEditor ==
無法執行編輯器: {0}

== ErrorPullNodataNotValid ==
選項 --nodata 無法用於複寫套件。

== ErrorPushHydrateNotValid ==
推送期間無法使用選項 hydrate。

== ErrorPushNodataNotValid ==
推送期間無法使用選項 --nodata。

== ErrorReplicateDestinationRepoNotSpecified ==
未指定目的地儲存庫

== ErrorReplicateHydrateSourceNotFound ==
無法自動獲得 '{0}' 的 Hydrate 來源儲存庫。請手動指定來源儲存庫。

== ErrorReplicateNodataNotValid ==
選項 --nodata 無法用於複寫套件，也無法與 --push 選項搭配使用。

== ErrorReplicatePackageNotSpecified ==
未指定複寫套件和目的地儲存庫

== ErrorReplicateSourceBranchNotSpecified ==
未指定來源分支

== ErrorRepositoriesDontMatch ==
您指定的儲存庫不相符

== EvilTwin ==
邪惡雙生衝突

== EvilTwinConflictActions1 ==
保留兩種變更，將目的地重新命名為

== EvilTwinConflictActions2 ==
保留在來源新增的項目 (捨棄在目的地新增)

== EvilTwinConflictActions3 ==
保留在目的地新增的項目 (捨棄在來源新增)

== EvilTwinConflictDestinationOperation ==
已新增 {0}

== EvilTwinConflictExplanation ==
在來源和目的地已新增具有相同名稱的項目，但它們是不同的項目。

== EvilTwinConflictSourceOperation ==
已新增 {0}

== Excluded ==
{0} 已排除。

== ExclusiveCheckoutDetail ==
{0} 已在 {2}@{3} 由使用者 {1} 簽出

== ExclusiveCheckoutDetailShort ==
{0} 已由使用者 {1} 簽出

== ExpirationDate ==
截止日期:        {0}

== False ==
否

== FastUpdCannotBePerformedInPartialWk ==
Gluon 工作區中不允許 [快速更新] 選項。請停用此選項，並重試此作業。

== FastUpdChanges ==
已套用下列變更:

== FastUpdDownloadProgress ==
已下載 {0} 個，共 {1} 個 ({2:0.##%})

== FastUpdProcessProgress ==
已處理 {0} 個，共 {1} 個 ({2:0.##%})

== FastUpdStageApplyingChanges ==
正在套用變更...

== FastUpdStageCalculatingChanges ==
正在計算變更...

== FastUpdStageCancelling ==
正在取消快速更新作業...

== FastUpdStageCompilingInfo ==
正在組合快速更新資料...

== FastUpdStageFinished ==
快速更新完成

== FastUpdWarnings ==
磁碟上無法套用下列變更:

== Fatal ==
重大

== FemaleNone ==
無

== FetchingAcls ==
正在擷取 ACL

== FetchingAttributes ==
正在擷取屬性

== FetchingBranches ==
正在擷取分支

== FetchingChangesets ==
正在擷取變更集

== FetchingChildren ==
正在擷取樹狀目錄

== FetchingFinished ==
正在傳輸中繼資料

== FetchingItems ==
正在擷取項目

== FetchingLabels ==
正在擷取標籤

== FetchingLinks ==
正在擷取連結

== FetchingMetadata ==
正在擷取中繼資料

== FetchingMoveRealizations ==
正在擷取移動作業

== FetchingReferences ==
正在擷取參考

== FetchingReviews ==
正在擷取程式碼檢閱

== FetchingRevisions ==
正在擷取樹狀目錄

== FetchingSeids ==
正在擷取 SEID

== File ==
檔案

== FileModifiedSource ==
已在來源修改檔案 {0}，而且將取代目的地版本

== Files ==
檔案

== FinishedNotOk ==
已取消

== FinishedOk ==
已完成確定

== FinishedStatus ==
複本已中斷 {0}

== FinishedWithErrors ==
已完成，但發生錯誤

== First ==
第一個

== From ==
從

== FsProtectionCannotBeApplied ==
無法套用項目 '{0}' FS 保護，因為發生錯誤: '{1}'。

== GameuiCheckinAddedMissingParentConflictAction ==
請復原新增作業，或將其移至伺服器中的現有位置。

== GameuiCheckinAddedMissingParentConflictDescription ==
無法新增項目 '{0}'，因為伺服器中不再載入其上層目錄。

== GameuiCheckinAddedPathInUseConflictAction ==
請復原新增作業，或將其移至伺服器中的任意位置。

== GameuiCheckinAddedPathInUseConflictDescription ==
無法新增項目 '{0}'，因為有另一個項目載入伺服器中的相同位置。

== GameuiCheckinChangedFileConflictAction ==
請復原您的本地變更，下載最新版本，並重新套用您的本地變更。

== GameuiCheckinChangedFileConflictDescription ==
'{0}' 需要合併。

== GameuiCheckinChangedMissingConflictAction ==
請復原本地變更，或在伺服器中的現有位置還原項目。

== GameuiCheckinChangedMissingConflictDescription ==
無法變更項目 '{0}'，因為伺服器中不再載入該項目。

== GameuiCheckinChangedXlinkConflictAction ==
請復原您的本地變更，下載最新版本，並重新編輯 Xlink。

== GameuiCheckinChangedXlinkConflictDescription ==
無法變更 Xlink '{0}' 的目標，因為在伺服器中已變更。

== GameuiCheckinCopiedLoadedConflictAction ==
請復原您的本地變更，下載最新版本，並重新套用您的本地變更。

== GameuiCheckinCopiedLoadedConflictDescription ==
無法複製項目 '{0}'，因為伺服器中已載入該項目。

== GameuiCheckinCopiedMissingParentConflictAction ==
請復原複製作業，或將其移至伺服器中的現有位置。

== GameuiCheckinCopiedMissingParentConflictDescription ==
無法複製項目 '{0}'，因為伺服器中不再載入其上層目錄。

== GameuiCheckinCopiedPathInUseConflictAction ==
請復原複製作業，或將其移至伺服器中的任意位置。

== GameuiCheckinCopiedPathInUseConflictDescription ==
無法複製項目 '{0}'，因為有另一個項目載入伺服器中的相同位置。

== GameuiCheckinDeletedAlreadyDeletedConflictAction ==
請復原刪除作業，並更新為最新版本。

== GameuiCheckinDeletedAlreadyDeletedConflictDescription ==
無法刪除項目 '{0}'，因為伺服器中不再載入該項目。

== GameuiCheckinDeletedChangedWarningDescription ==
已刪除項目 '{0}' 已在伺服器中變更。如果套用刪除，將會遺失伺服器變更。

== GameuiCheckinDeletedMovedWarningDescription ==
已刪除項目 '{0}' 已在伺服器中移動。如果套用刪除，將會遺失伺服器移動。

== GameuiCheckinDeletedWarningAction ==
您確定要刪除項目: '{0}'?

== GameuiCheckinDirReplacedChangedConflictDescription ==
無法恢復項目 '{0}'，因為伺服器中已變更該項目。

== GameuiCheckinFileReplacedChangedConflictDescription ==
'{0}' 需要合併。

== GameuiCheckinFsProtectionMissingConflictAction ==
請復原本地變更，或在伺服器中的現有位置還原項目。

== GameuiCheckinFsProtectionMissingConflictDescription ==
無法變更項目 '{0}' 的權限，因為伺服器中不再載入該項目。

== GameuiCheckinMovedAlreadyMovedConflictAction ==
請復原移動作業，並更新為最新版本。

== GameuiCheckinMovedAlreadyMovedConflictDescription ==
項目 '{0}' 無法移至 '{1}'，因為該項目已載入伺服器的目的地。

== GameuiCheckinMovedDivergentConflictAction ==
您是否仍要將項目 '{0}' 移至 '{1}'?

== GameuiCheckinMovedDivergentConflictDescription ==
移動的項目 '{0}' 已移動至伺服器中的不同位置。如果套用移動，將會遺失伺服器移動。

== GameuiCheckinMovedDstInUseConflictAction ==
請復原移動作業，或將其移至伺服器中的任意位置。

== GameuiCheckinMovedDstInUseConflictDescription ==
項目 '{0}' 無法移至 '{1}'，因為有另一個項目載入伺服器中的相同位置。

== GameuiCheckinMovedInsideItselfConflictAction ==
請復原移動作業，或將其移至伺服器中的有效位置。

== GameuiCheckinMovedInsideItselfConflictDescription ==
項目 '{0}' 無法在 '{1}' 中移動，因為 '{1}' 已在伺服器中 '{0}' 內載入。

== GameuiCheckinMovedMissingDstConflictAction ==
請復原移動作業，或將項目移至伺服器中的現有位置。

== GameuiCheckinMovedMissingDstConflictDescription ==
項目 '{0}' 無法移至 '{1}'，因為伺服器中不再載入目的地。

== GameuiCheckinMovedMissingItemConflictAction ==
請復原移動作業，或在伺服器中的現有位置還原項目。

== GameuiCheckinMovedMissingItemConflictDescription ==
無法移動項目 '{0}'，因為伺服器中不再載入該項目。

== GameuiCheckinReplacedChangedConflictAction ==
請復原您的恢復作業，請下載最新版本，並進行恢復。

== GameuiCheckinReplacedMissingConflictAction ==
請恢復您的復原作業。

== GameuiCheckinReplacedMissingConflictDescription ==
無法恢復項目 '{0}'，因為伺服器中不再載入該項目。

== GameuiOutOfDateUnresolvedXlink ==
無法解析 Xlink '{0}'。無法更新未解析的 Xlink 下的項目。

== GetfileRevdatanotfound ==
找不到指定規格的資料

== Group ==
群組

== GroupKey ==
群組

== HiddenChanged ==
隱藏已變更

== Ignored ==
忽略清單

== InactiveKey ==
INACTIVE (未授權)

== Info ==
資訊

== IntervalMerge ==
間隔合併

== IntroducingData ==
引入資料

== IssueTrackerCheckinFailed ==
無法將簽入資料記錄至議題追蹤器: {0}

== IssueTrackerNotSupported ==
不支援指定的議題追蹤器 '{0}'。

== ItemAddedSource ==
已在來源新增項目 {0}，並將在合併後新增

== ItemAlreadyChanged ==
因為工作區: '{0}' 中的項目已變更 (已簽出、已進行本地變更、已刪除、已移動或已恢復)，所以不會下載。請復原或簽入本機變更，並重試此作業。

== ItemDeletedSource ==
已在來源刪除項目 {0}，並將在合併後刪除

== ItemFsprotToApply ==
項目 {0} 已變更來源上的檔案系統權限

== ItemMovedSource ==
項目 {0} 已在來源移至 {1}，而且將在合併後移動

== ItemNotFound ==
找不到項目 {0}

== ItemPathAlreadyUsedByChange ==
因為在 '{0}' 有現有的變更，所以不會下載/移動項目。請復原本地變更、取消載入項目 (從設定檢視)，並重試此作業。

== KeyEditable ==
可編輯

== KeyPrivate ==
私人

== KeyWorkspaceRevision ==
工作區修訂

== Label ==
標籤

== LicenseEdition ==
版本:                {0}

== LicenseInformation ==
授權資訊:

== LicenseUsage ==
授權使用狀況:

== LicensedTo ==
授權對象:            {0}

== LicensedUsers ==
授權使用者總計:   {0}

== ListFindObjects ==
可用物件與屬性: 

== ListPermissionsKey ==
可用權限: 

== ListTriggerTypes ==
可用的觸發類型: 

== LkNameField ==
連結名稱

== LoadedRevision ==
修訂 {0}@{1} 已載入

== LoadedTwice ==
項目載入兩次衝突

== LoadedTwiceConflictActions1 ==
保留在來源完成的新增 (捨棄在目的地新增)

== LoadedTwiceConflictActions2 ==
保留在目的地完成的新增 (捨棄在來源新增)

== LoadedTwiceConflictDestinationOperation ==
已新增 {0}

== LoadedTwiceConflictExplanation ==
來源和目的地已新增兩個項目並發生碰撞，因為它們是相同的項目。

== LoadedTwiceConflictSourceOperation ==
已新增 {0}

== LocalAndDownloadedSameFile ==
將不會下載檔案 {0}，因為本機檔案與要下載的檔案內容相符。cset:{1}

== LocatedOnBranch ==
位於分支上

== LogAdded ==
已新增

== LogChanged ==
已變更

== LogDefaultCsFormat ==
變更集號碼: {changesetid}{newline}分支: {branch}{newline}擁有者: {owner}{newline}日期: {date}{newline}註解: {comment}{newline}變更: {newline}{items}------------------------------------------------------------

== LogDefaultCsFormatWithoutChanges ==
變更集號碼: {changesetid}{newline}分支: {branch}{newline}擁有者: {owner}{newline}日期: {date}{newline}註解: {comment}{newline}------------------------------------------------------------

== LogDeleted ==
已刪除

== LogMoved ==
已移動

== MakeTubeSucceeded ==
Tube {0} -> {1} 已正確建立。

== Markers ==
標記

== Merge ==
合併

== MergeBothChanges ==
兩個貢獻者。

== MergeDstChanges ==
目的地貢獻者。

== MergeExtraInfoBase ==
基底

== MergeExtraInfoCommentsLine ==
註解: {0}

== MergeExtraInfoContributorLine ==
{3} 已於 {4} 從 {1}{2} 建立 {0}

== MergeExtraInfoDestination ==
目的地

== MergeExtraInfoRecursiveMergeLine ==
遞迴合併: 這是遞迴合併。這表示有多個上層，而非只有一個。
Unity VCS 將解決此問題，方法是自動合併多個常用上層，以建立單一上層，該上層將用於合併來源和目的地貢獻者。

現在您正在將上層 {0} 與上層 {1} 合併，將產生一個虛擬上層 vcs:{2}，以供稍後使用。

== MergeExtraInfoSource ==
來源

== MergeInProgress ==
您有一個來自變更集 {0}@{1} 的進行中合併。請先結束此合併，然後再從其他來源進行合併

== MergeNeeded ==
檔案 {0} 需要從 {1} 合併至 {2} 基底 {3}。由 {4} 進行變更

== MergeNoneChanges ==
無。

== MergeProgressRecursive ==
遞迴

== MergeProgressString ==
合併檔案 {0}/{1}

== MergeResultMultipleLinkTargets ==
合併結果針對符號連結 {0} 留下多個目標

== MergeSourceFormat ==
{1}@{2} 的 {0}

== MergeSrcChanges ==
來源貢獻者。

== MergeToMergeNeeded ==
分支 {0}@{1}@{2} (掛載 '{3}') 具有多個前端，因為在合併目標作業期間，已建立新變更集。請將執行合併 (或合併目標) 的分支前端，從變更集 {4}@{1}@{2} 整合至分支 {0}@{1}@{2}。

== MergeVirtualChangesetComment ==
遞迴合併期間建立的虛擬變更集

== MergedFile ==
已合併 {0} 個檔案，共 {1} 個: {2}

== Mergefrom ==
從作業控制合併

== MergingFile ==
正在合併 {0} 個檔案，共 {1} 個: {2}

== MessageBranchSelector ==
選取分支以取得修訂 (如果沒有，則選取 /main): 

== MessageCheckingIn ==
正在簽入 {0}... 

== MessageCheckingOut ==
正在簽出 {0}... 

== MessageCoSelector ==
選取將執行簽出的分支 (如果沒有，則選取 /main): 

== MessageContinueWithSelector ==
這是產生的選擇器。是否要使其處於作用中? (Y/N): 

== MessageDone ==
完成

== MessageFileNotFound ==
檔案 {0} 不存在。

== MessageLabelSelector ==
選取要從中取得修訂的標籤: 

== MessageMoreRepositories ==
更多儲存庫? (Y/N): 

== MessageNoRepositories ==
伺服器中沒有儲存庫。 

== MessageNumItemSelector ==
== 儲存庫選擇器 #{1} 的項目選擇器 #{0} ==

== MessageNumRepSelector ==
== 儲存庫選擇器 #{0} ==

== MessageRepositoryName ==
選取儲存庫名稱: 

== MessageRepositoryServer ==
選取儲存庫伺服器: 

== MessageSelectorNotSet ==
尚未設定選擇器。

== Mkattr ==
可讓使用者建立屬性

== Mkchildbranch ==
可讓使用者建立下層分支

== Mklabel ==
可讓使用者建立標籤

== Mkrepository ==
可讓使用者建立儲存庫

== Mktoplevelbranch ==
可讓使用者建立最上層分支

== Mktrigger ==
可讓使用者建立觸發器

== Modified ==
已修改

== Move ==
控制檔案和目錄是否可在儲存庫中移動

== MoveAddConflictAction1 ==
保留兩種變更，將目的地重新命名為

== MoveAddConflictAction2 ==
保留來源變更 (保留移動，並捨棄新增)

== MoveAddConflictAction3 ==
保留目的地變更 (保留新增，並捨棄移動)

== MoveAndAdd ==
移動/新增衝突

== MoveAndDelete ==
移動/刪除衝突

== MoveDeleteConflictActions1 ==
保留來源變更 (保留移動，並捨棄刪除)

== MoveDeleteConflictActions2 ==
保留目的地變更 (保留刪除，並捨棄移動)

== MoveDeleteConflictDestinationOperation ==
已刪除 {0}

== MoveDeleteConflictExplanation ==
已在來源上移動項目，而且目的地已刪除該項目或其上層。

== MoveDeleteConflictSourceOperation ==
從 {0} 移至 {1}

== MoveIgnoredDestinationWasntSelected ==
項目 '{0}' 將不會移動，因為未選取移動 '{1}' 的目的地。請更新工作區，或選取移動的來源和目的地，並重試此作業。

== MoveIgnoredSourceWasntSelected ==
雖然已選取移動 '{1}' 的目的地，但因為未選取項目 '{0}'，所以不會移動。請更新工作區，或選取移動的來源和目的地，並重試此作業。

== MoveItemAlreadyChanged ==
因為工作區: '{0}' 中的項目已變更 (已簽出、已進行本地變更、已刪除、已移動或已恢復)，所以不會移動。請復原或簽入本機變更，並重試此作業。

== MoveNotApplicable ==
項目無法移至 '{0}'。可能目的地路徑已使用。請取消載入項目 (從設定檢視)，並重試此作業。

== MoveSourceDelete ==
移出刪除衝突

== MoveSourceDeleteConflictDestinationOperation ==
已刪除 {0}

== MoveSourceDeleteConflictSourceOperation ==
從 {0} 移至 {1}

== Moved ==
已移動

== MovedAddedConflictDestinationOperation ==
已新增 {0}

== MovedAddedConflictExplanation ==
已在來源移動項目，而且在相同位置的目的地上，已新增具有相同名稱的項目。

== MovedAddedConflictSourceOperation ==
從 {0} 移至 {1}

== MovedEvilTwin ==
移動的邪惡雙生衝突

== MovedEvilTwinConflictActions1 ==
保留兩種變更，將目的地重新命名為

== MovedEvilTwinConflictActions2 ==
保留在來源完成的移動 (捨棄在目的地移動)

== MovedEvilTwinConflictActions3 ==
保留在目的地完成的移動 (捨棄在來源移動)

== MovedEvilTwinConflictDestinationOperation ==
從 {0} 移至 {1}

== MovedEvilTwinConflictExplanation ==
具有相同名稱的兩個不同項目已移至來源和目的地上的相同位置。

== MovedEvilTwinConflictSourceOperation ==
從 {0} 移至 {1}

== MovedLocally ==
在本機移動

== MsgBinaryFile ==
二進位

== MsgDirectory ==
目錄

== MsgItemAdded ==
在 Unity VCS 控制下，而且最近已新增。

== MsgItemAddedNotOnDisk ==
在 Unity VCS 控制下，而且最近已新增，但不在磁碟中。

== MsgItemChanged ==
在 Unity VCS 控制下，而且已在本地變更。

== MsgItemCheckedin ==
在 Unity VCS 控制下，而且已簽入。

== MsgItemCheckedinNotOnDisk ==
在 Unity VCS 控制下，而且已簽入，但不在磁碟中。

== MsgItemCheckedout ==
在 Unity VCS 控制下，而且已簽出。

== MsgItemCheckedoutNotOnDisk ==
在 Unity VCS 控制下，而且已簽出，但不在磁碟中。

== MsgItemIgnored ==
已忽略。

== MsgItemNotOnDisk ==
不在磁碟上。

== MsgItemPrivate ==
私人。

== MsgItemStatus ==
項目 {0} 為 {1}

== MsgLinkFile ==
連結

== MsgPendingDstMergelink ==
至 cs:{0}，位於 {1}@{2}@{3}

== MsgPendingMergelink ==
{0} 來自 cs:{1}，位於 {2}@{3}@{4}

== MsgPrivFile ==
檔案

== MsgPrivate ==
私人

== MsgUnknown ==
未知

== MsgWorkspaceWorkingInfo ==

      儲存庫: {0}
分支: {1}
      標籤: {2}
    

== Multiple ==
多個

== NameAlreadyInDirectory ==
名稱 {0} 已在目錄中

== NameNotValid ==
名稱 {0} 不再有效

== NewBrNameParamNotBrspec ==
新名稱參數必須包含分支名稱，而不是分支規格

== NewNameFor ==
{0} 的新名稱: 

== NewNameInstead ==
新名稱，而不是 {0}: 

== No ==
否

== NoChangesApplied ==
沒有套用的變更

== NoChangesInWk ==
在工作區中 {0} 沒有變更

== NoErrorsDetected ==
未偵測到錯誤。

== NoValidAnswers ==
n;否

== NodeChangeAdded ==
已新增 {0}

== NodeChangeCopied ==
已複製 {0}

== NodeChangeDeleted ==
已刪除 {0}

== NodeChangeModified ==
已修改 {0}

== NodeChangeModifiedAndMoved ==
已修改並從 {0} 移至 {1}

== NodeChangeMoved ==
從 {0} 移至 {1}

== NodeChangeReplaced ==
已取代 {0}

== NotMergedFile ==
未合併 {0} 個檔案，共 {1} 個: {2}

== Of ==
/

== OldRenameRenameNames ==
舊名稱 {0}，重新命名第一個 {1}，重新命名第二個 {2}

== On ==
於

== OnlyModifiedOnSource ==
僅處理來源中已修改的 {0} 個檔案，共 {1} 個: {2}

== OperationAbandoned ==
已放棄

== OperationDisabled ==
此作業已在 Plastic SCM 5.0 中停用

== OperationStartingFetch ==
正開始擷取

== OperationStartingPush ==
正開始推送

== Owner ==
擁有者

== PasswordCantBeEmpty ==
密碼不可為空白。密碼未設定。

== PasswordCorrectlySet ==
密碼已正確設定

== PasswordDoesntMatch ==
密碼不相符。密碼未設定。

== PathInConflictWarningMessage ==
項目 '{0}' 已重新命名為 '{1}'，因為在合併作業期間，此路徑已在使用中

== PathNotFound ==
合併衝突中找不到路徑 {0}

== PathNotFoundInDiff ==
差異比對集合中找不到路徑 {0}

== PathNotUnique ==
路徑 '{0}' 不是唯一的。可能參照任何...

== PathsFsLocked ==
作業無法啟動，因為某些檔案或目錄已由其他程式鎖定。請關閉程式並重試。

== PendingMergeLinks ==
暫止的合併連結

== PerformingSwitch ==
正在執行切換作業...

== PermissionKey ==
權限

== Private ==
私人

== ProcessingBranches ==
正在處理分支

== ProcessingDirectoryConflicts ==
正在處理目錄衝突

== ProcessingDirectoryOperations ==
正在處理目錄作業

== ProcessingDirectoryOperationsApplyingFsProtections ==
正在處理目錄作業 (正在套用檔案系統保護)

== ProcessingDirectoryOperationsDownloadingRevisions ==
正在處理目錄作業 (正在下載修訂)

== ProcessingDirectoryOperationsUpdatingWorkspace ==
正在處理目錄作業 (正在更新工作區)

== PushingAcls ==
正在推送 ACL

== PushingAttributes ==
正在推送屬性

== PushingBranches ==
正在推送分支

== PushingChangesets ==
正在推送變更集

== PushingChildren ==
正在推送樹狀目錄

== PushingFinished ==
已完成推送中繼資料

== PushingItems ==
正在推送項目

== PushingLabels ==
正在推送標籤

== PushingLinks ==
正在推送連結

== PushingMetadata ==
正在推送中繼資料

== PushingMoveRealizations ==
正在推送移動

== PushingReferences ==
正在推送參考

== PushingRevisions ==
正在推送修訂

== Read ==
控制是否可讀取物件

== ReconcilingAcls ==
正在協調 ACL

== ReconcilingObjects ==
正在協調物件

== RemoveTubeSucceeded ==
Tube {0} -> {1} 已正確移除。

== Removed ==
已移除

== RemovedLocally ==
在本機移除

== Rename ==
控制是否可重新命名物件

== RenamedOldNameConflict ==
項目重新命名為仍在使用中的名稱。

== RenamedRenamedConflict ==
兩個貢獻者皆有已重新命名項目。

== RenamedSameNameConflict ==
兩個項目已重新命名為相同名稱

== RenamedToDownloadFile ==
將重新命名檔案 {0}，以下載新版本。cset:{1}

== RenamesNotValidIntroduce ==
重新命名的名稱已不再有效，您必須手動引入新名稱

== Replaced ==
已取代

== Replicateread ==
控制複寫讀取作業

== Replicatewrite ==
控制複寫寫入作業

== ReportCmdMsgFileChangedInWk ==
此檔案已在工作區中變更，不在 Unity VCS 控制範圍內，因此，已保留資料。項目不是最新狀態。如果您要更新項目，請嘗試強制更新此項目

== ReportCmdMsgUpdateStoringCheckout ==
安全儲存此項目的已簽出資料

== Repository ==
儲存庫

== RepositorySpec ==
儲存庫規格

== ResolutionConfigured ==
已使用設定的解決方案解決衝突。

== ResolvedCreatedConflict ==
已在解決衝突 {1} 之後建立衝突 {0}

== ResolvedRemovedConflict ==
已在解決衝突 {1} 之後移除衝突 {0}

== ReviewStatusReviewed ==
已檢閱

== ReviewStatusReworkRequired ==
需要重做

== ReviewStatusUnderReview ==
正在檢閱

== RevisionCheckedout ==
本機

== RevisionHistoryOf ==
{0} 的修訂歷史記錄

== RevisionNotExists ==
修訂不存在

== RevisionNotFound ==
找不到要載入的修訂

== RevisionNumber ==
修訂編號

== RevisionSpec ==
修訂規格

== RevisionType ==
修訂類型

== Rm ==
控制檔案和目錄是否可從儲存庫中移除

== Rmattr ==
可讓使用者移除屬性

== Rmchangeset ==
控制變更集是否可從其分支中移除

== Rmlabel ==
可讓使用者移除標籤

== Rmrepository ==
可讓使用者移除儲存庫

== Rmtrigger ==
可讓使用者移除觸發器

== SearchingForChanged ==
正在搜尋工作區中的已變更項目...

== Second ==
第二個

== SelectorNoChanges ==
無變更。

== Server ==
伺服器

== SetConfigSucceeded ==
Plastic Tube 設定已正確設定。

== SettingNewSelector ==
設定新選擇器...

== ShareRepoSucceeded ==
儲存庫 '{0}' 已正確共用。

== Shelve ==
擱置

== Skip ==
跳過

== Source ==
來源

== SourceDestinationChanged ==
項目 {0} 已在來源中變更，但在合併期間捨棄，因為內容在來源與目的地中是相同的

== SourceDestinationChangedFsprotection ==
項目 {0} 已在來源中變更其檔案系統保護，但在合併期間捨棄，因為它們在來源與目的地中是相同的

== SourceDestinationDeleted ==
項目 {0} 已在來源中刪除，但在合併期間捨棄，因為該項目已在目的地中刪除

== SourceDestinationMoved ==
來源與目的地中的檔案 {0} 已移動至相同位置，因此將捨棄移動。請從 {1} 移動至 {2}

== SrcContributor ==
來源變更集: {0} (分支 {1})

== StartResolveConflict ==
正要解決下列衝突:

== StatColLastmod ==
上次修改

== StatColPath ==
路徑

== StatColSimilarity ==
相似性

== StatColSize ==
大小

== StatColStatus ==
狀態

== StatusKey ==
狀態

== StatusPerfWarningChanged ==
尋找已變更檔案花費太多時間。執行秘訣: {0}

== StatusPerfWarningMoved ==
計算移動檔案花費太多時間。您有太多私人/已移除檔案。執行秘訣: {0}

== StatusPerfWarningPrivates ==
您有太多私人檔案，這會影響效能。了解如何忽略私人檔案: {0}

== SubtractiveIntervalMerge ==
削減間隔合併

== SubtractiveMerge ==
削減合併

== SubtractiveNeeded ==
項目 {0} 需要從 {1} 進行削減合併至 {2} 基底 {3}。由 {4} 進行變更

== SupportBundleCreated ==
在 {0} 建立的支援套件組合

== SupportBundleCreating ==
正在建立新的支援套件組合...

== SupportContactUs ==
請記住，您可以隨時與我們聯絡，網址是 https://www.plasticscm.com/support.
如果您有任何問題、建議
，或需要指導，請隨時與我們聯絡。
    

== SyncAlreadyReplicated ==
無法啟動同步處理，因為目標儲存庫是從與 git 同步處理的儲存庫複寫。原本同步處理的儲存庫是 '{0} - {1}'。如需詳細資訊，請聯絡支援部門。

== To ==
到

== Total ==
總計

== TubeStatusConnected ==
Unity VCS 伺服器 ({0}) 已連線至 Plastic Tube。

== TubeStatusDisconnected ==
Unity VCS 伺服器 ({0}) 未連線至 Plastic Tube。

== TypeNewPassword ==
新密碼: 

== TypeOldPassword ==
舊密碼: 

== UnableToRemoveCset ==
無法刪除選取的變更集 {0}。

== UndoUnableToMove ==
無法將檔案 '{0}' 重新移回 '{1}': {2}

您可能需要先包含已刪除的項目，才能復原變更。請檢查目前選項:

- 如果您使用 CLI，請嘗試將刪除的路徑或 "--deleted" 引數新增至 'undo' 命令。
- 如果您使用 GUI，請開啟 [暫止的變更] 檢視中的 [選項] 對話方塊，請勾選 [顯示項目] 索引標籤中的 [顯示已刪除的檔案和目錄]。

== UndoneAddOperation ==
本地新增作業 {0} 將無法復原。

== UndoneDeleteOperation ==
本地刪除作業 {0} 將無法復原。

== UndoneMoveOperation ==
本地移動作業 {0} -> {1} 將無法復原。

== UnknownError ==
不明錯誤

== Unlimited ==
無限制

== UnloadItemAlreadyChanged ==
因為工作區: '{0}' 中的項目已變更 (已簽出、已進行本地變更、已刪除、已移動或已恢復)，所以不會取消載入。請復原或簽入本機變更，並重試此作業。

== UnneededMergeRevisionLoaded ==
'{0}' 的複製合併已不再需要，因為已載入正確的修訂。

== UnresolvedXlink ==
未解析的 Xlink

== UnshareRepoSucceeded ==
儲存庫 '{0}' 已正確取消共用。

== UnsupportedMergeType ==
不支援的合併類型

== UpdateDeletedSelectorObjectSkipped ==
無法將您的工作區更新為有效的分支設定。其他人可能已刪除擱置或您的工作區已設定的標籤。請將您的工作區切換至現有分支、變更集、標籤或擱置集。

== UpdateProgress ==
已更新 {0} 個檔案，共 {1} 個 (待下載 {2} 個檔案，共 {3} 個/待套用 {4} 個作業，共 {5} 個)

== UpdateProgressCalculating ==
正在計算...

== UpdateProgressDownloadingBigFile ==
正在從 {2} 下載檔案 {0} ({1})

== UpdateProgressDownloadingBlock ==
正在從 {2} 下載 {0} 個檔案的區塊 ({1})

== UpdateProgressUpdating ==
正在更新...

== UpdateStatusCalculating ==
正在計算

== UpdateStatusFinished ==
已完成

== UpdateStatusUpdating ==
正在更新

== UpdateWithChangesFromGluonWorkspaceNotAllowed ==
無法從 Gluon 工作區切換至帶有簽出項目的標準工作區。您可以從 Gluon 簽入 (或復原)，並重試更新。

== UpdateWkIsUpToDate ==
工作區 {0} 已是最新狀態 (cset:{1})

== UpdatingWorkspace ==
Unity VCS 正在更新您的工作區。請稍後...

== UploadingFiles ==
正在上傳 {0} 個檔案

== User ==
使用者

== UserCorrectlyActivated ==
已成功啟用使用者 {0}

== UserCorrectlyDeactivated ==
已成功停用使用者 {0}

== UserInformation ==
授權資訊: 伺服器: {0}

== UserKey ==
使用者

== View ==
控制是否可檢視物件

== VirtualPathDecorator ==
(虛擬上層，cs:{0} 上的修訂)

== WaitingOperation ==
此作業可能會需要幾分鐘的時間。請稍候...

== Warning ==
警告

== WhichChange ==
您想要變更哪一個元素? (來源|目的地)[1|2]

== WhichRename ==
您想要使用哪一個重新命名的名稱? [1|2]

== WillCreateRepo ==
儲存庫 {0} 不存在。將建立一個儲存庫

== WorkspacestatusAddGrp ==
已新增項目 (AD = 已新增，CP = 已複製 (新)，PR = 私人，IG = 已忽略)

== WorkspacestatusChGrp ==
已修改項目 (CH = 已變更，CO = 已簽出，RP = 已取代)

== WorkspacestatusMvGrp ==
已移動項目 (MV = 已移動，LM = 在本機移動)

== WorkspacestatusRmGrp ==
已刪除項目 (DE = 已刪除，LD = 在本機刪除)

== XlinkConflict ==
Xlink 衝突

== XlinkConflictActions1 ==
保留 Xlink 中的來源變更

== XlinkConflictActions2 ==
保留 Xlink 中的目的地變更

== XlinkConflictDestinationOperation ==
已將 Xlink 變更至: {0}

== XlinkConflictExplanation ==
來源與目的地的 Xlink 已變更。

== XlinkConflictSourceOperation ==
已將 Xlink 變更至: {0}

== XlinkWritableConflict ==
Xlink 缺少上層衝突

== XlinkWritableConflictExplanation ==
Xlinked 儲存庫不知道上層變更集以計算合併。使用者必須手動指定上層變更集。

== Yes ==
是

== YesValidAnswers ==
y;是

== CmdConfigureErrorCannotCreateDir ==
Failed when creating a directory [{0}]:{1}

== CmdConfigureErrorWrongParameters ==
錯誤的參數號碼。輸入 cm configure --help 以取得說明

== CmdConfigureErrorReadingParameters ==
讀取參數時發生錯誤；請檢查提供的參數清單。

== CmdConfigureHeader ==
####--- 用戶端設定精靈 ---####

== CmdConfigureSuccess ==
已正確設定 Unity VCS 用戶端。

== CmdConfigureDetectedWorkingMode ==
已偵測到工作模式: {0}

== CmdConfigureDetectedWorkingModeError ==
檢查 Unity VCS 伺服器安全性模式時發生錯誤。
伺服器可能處於離線狀態，或指定的位址不正確。
是否要重新輸入 Unity VCS 伺服器位址?

== CmdConfigureServerParams ==
設定 Unity VCS 伺服器位址/連接埠:

== CmdConfigureServerParamsAddress ==
Unity VCS 伺服器位址 [{0}]

== CmdConfigureServerParamsPort ==
Unity VCS 伺服器連接埠 [{0}]

== CmdConfigureServerParamsSslPort ==
Unity VCS SSL 伺服器連接埠 [{0}]

== CmdConfigureServerParamsUseSsl ==
使用加密 (SSL)?

== CmdConfigureProxyParams ==
是否要設定 proxy 伺服器?

== CmdConfigureProxyParamsAddress ==
Proxy 伺服器位址

== CmdConfigureProxyParamsPort ==
Proxy 伺服器連接埠
