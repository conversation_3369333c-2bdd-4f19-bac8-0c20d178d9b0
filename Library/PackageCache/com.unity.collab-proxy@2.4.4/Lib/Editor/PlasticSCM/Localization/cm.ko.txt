== CmExistingCertificateChanged ==

경고: 이 호스트의 인증서가 키 저장소에 있는 인증서와 일치하지 
않습니다. 서버 ID가 손상되었거나 
중간자 공격이 일어나고 있을 수도 있습니다.

인증서 세부사항:
- 발급 대상: {0}
- 발급자: {1}
- 만료일: {3}
- 인증서 해시: {5}

해당 인증서 변경에 대해 알고 있었고 새 인증서를 신뢰하는 경우, '예'를 선택해 
  Unity VCS 키 저장소에 키를 저장하고 앞으로 이 인증서를 
  신뢰하게 합니다.
키를 저장소에 추가하지 않고 한 번만 연결하려면 
  '아니요'를 선택합니다.
이 연결을 중단하려면 '취소'를 선택합니다. 이는 유일하게
  안전이 보장되는 옵션입니다.



== CmExistingCertificateChangedPrompt ==
예(Y), 아니요(N), 취소(C) 중 선택(기본값은 취소): 

== CmInvalidCertificateHostname ==
경고: 서버 인증서에 제공된 보안 연결 호스트 이름이 
서버 호스트 이름과 일치하지 않습니다. 
인증서가 호스트 이름에 발급되지 않았거나 해당 호스트에 
네트워크 구성 문제가 있을 수 있습니다.

- 인증서 호스트 이름: {0}
- 서버 호스트 이름: {1}

이 호스트에 대한 연결을 계속하려면 '예'를 선택합니다. 그러면 
  인증서 검증이 계속 진행됩니다(권장하지 않음).
연결을 중단하려면 '아니요'를 선택합니다(권장).
    

== CmInvalidCertificateHostnamePrompt ==
예(Y), 아니요(N) 중 선택(Enter를 누르면 '아니요' 자동 선택): 

== CmInvalidCertuiResponse ==
(.) 입력하신 옵션이 유효하지 않습니다.

== CmNewCertificate ==

연결 중인 서버가 저장소에 없는 인증서를 보냈습니다. 
이 서버에 처음 연결하는 경우 이는 정상입니다.

인증서 세부사항: 
- 발급 대상: {0}
- 발급자: {1}
- 만료일: {3}
- 인증서 해시: {5}

이 호스트를 신뢰하는 경우, '예'를 선택해 Unity VCS 키 저장소에
  키를 저장합니다(이 서버에 처음 연결하는 경우 권장)
키를 저장소에 추가하지 않고 한 번만 연결하려면 
  '아니요'를 선택합니다.
이 호스트를 신뢰하지 않는 경우 '취소'를 선택해 연결을 중단합니다.

    

== CmNewCertificatePrompt ==
예(Y), 아니요(N), 취소(C) 중 선택(Enter를 누르면 '취소' 자동 선택): 

== CmdListCommandsAdditionalHeader ==
추가 명령:

== CmdListCommandsAdministrativeHeader ==
관리 명령:

== CmdListCommandsAutomationHeader ==
자동화 명령:

== CmdListCommandsCommandTypes ==

이 목록은 현재 사용 가능한 명령 중 일부입니다. 다음 명령을 사용할 수 있습니다.
      cm showcommands --essential: 필수 명령 표시(기본값)
      cm showcommands --additional: 추가 명령 표시
      cm showcommands --administrative: 관리자가 사용하는 명령 표시
      cm showcommands --security: 보안 및 ACL 관리 명령 표시
      cm showcommands --automation: CLI 자동화 명령 표시
      cm showcommands --all: 모든 명령 목록 표시

== CmdListCommandsEnd ==
        
* 명령 실행:
      cm command_name
  
* 명령 사용법 가져오기:
      cm command_name --usage
      cm command_name -?

* 명령의 도움 받기:
      cm help command_name
      cm command_name --help

명령줄 사용 방법 자세히 알아보기:
https://www.plasticscm.com/documentation/cli/plastic-scm-version-control-cli-guide.shtml

명령 찾기 자세히 알아보기:
https://www.plasticscm.com/documentation/cmfind/plastic-scm-version-control-query-system-guide.shtml

명령 자동화 자세히 알아보기:
https://www.plasticscm.com/documentation/cmdrunner/plastic-scm-version-control-cmdrunner-guide.shtml
      

== CmdListCommandsEssentialHeader ==
필수 명령:

== CmdListCommandsSecurityHeader ==
보안 명령:

== Description ==
설명

== EnterPassword ==
비밀번호: 

== EnterUser ==
사용자: 

== EnterUserCredentials ==
서버 [{0}]에 연결할 자격 증명 입력

== LongName ==
명령

== MsgCommandnotfound ==
'{0}' 명령을 찾을 수 없습니다. 명령 목록을 가져오려면 cm showcommands를 입력하십시오.

== MsgError ==
오류: 

== MsgNocommands ==
정의된 명령이 없습니다.

== ShortName ==
단축형 

== WkUpgradeFinished ==
워크스페이스가 업그레이드되었습니다.

== WkUpgradeStarted ==
워크스페이스 업그레이드 중...
