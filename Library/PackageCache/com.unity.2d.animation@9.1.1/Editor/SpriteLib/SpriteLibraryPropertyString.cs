namespace UnityEditor.U2D.Animation
{
    internal static class SpriteLibraryPropertyString
    {
        public const string overrideEntries = "m_OverrideEntries";
        public const string fromMain = "m_FromMain";
        public const string spriteOverride = "m_SpriteOverride";
        public const string sprite = "m_Sprite";
        public const string overrideEntryCount = "m_EntryOverrideCount";
        public const string name = "m_Name";
        public const string hash = "m_Hash";
        public const string categoryList = "m_CategoryList";
    }
}