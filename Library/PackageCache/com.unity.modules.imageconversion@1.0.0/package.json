{"name": "com.unity.modules.imageconversion", "version": "1.0.0", "type": "module", "displayName": "Image Conversion", "description": "The ImageConversion module implements the ImageConversion class which provides helper methods to convert images from and to PNG, JPEG or EXR formats. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html", "icon": ".icon.png", "dependencies": {}}