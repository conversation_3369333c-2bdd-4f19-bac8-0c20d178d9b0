namespace Editor_ShaderVariantsCollectionsTools
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Sirenix.OdinInspector;
    using Sirenix.OdinInspector.Editor;
    using UnityEditor;
    using UnityEngine;
    using UnityEngine.Rendering.Universal;

    [Serializable]
    public class ShaderVariantsWindowsData
    {
        [BoxGroup("变体收集")]
        [TextArea(1, 1)] [ReadOnly] public string VariantCollectionDesc = "生成的目标变体集,把需要生成的拖到这里";
        [BoxGroup("变体收集")]
        public ShaderVariantCollection generaterVariantCollection;

        [BoxGroup("变体收集")]
        [TextArea(1, 1)] [ReadOnly] public string allMaterialsPathsDesc = "搜集到的材质球路径.";
        [ShowInInspector] [BoxGroup("变体收集")] public HashSet<string> allMaterialsPaths = new();

        [BoxGroup("变体收集")]
        [TextArea(1, 1)] [ReadOnly] public string GlobalShaderKeyWordSetDesc = "通过代码控制开关的变体.";
        /// <summary>
        /// 通过代码控制开关的变体,定义在这里
        /// </summary>
        [ShowInInspector]
        [ReadOnly]
        [BoxGroup("变体收集")]
        public static HashSet<string> GlobalShaderKeyWordSet = new()
        {
            // HDR
            "UNITY_HDR_ON",

            // Light
            "_ADDITIONAL_LIGHTS",

            // shadows
            "VIRTUAL_SHADOW_MAP",
            "VIRUTAL_POINT",
            "_VSM_ENABLE_ANTIALIASING",
            "_CASTING_PUNCTUAL_LIGHT_SHADOW", // 阴影采集的


            //Fog
            "FOG_LINEAR",   // 只开放线性全局Fog,有需要再放开其他两个
            // "FOG_EXP",
            // "FOG_EXP2",
            "PANEL_FOG",

            // UI
            "UNITY_UI_CLIP_RECT",
            "UNITY_UI_ALPHACLIP",
            "W_ALPHA_FIX",
            "W_SEQUEUFRAME",

            // Flow
            "_FLOWMODE_ON",
            "_FLOWMODE_2_ON",

            // stroke
            "STROKE_UV_2",

            // 粒子
            "_particle_instancing_",

        };

        [BoxGroup("变体收集")]
        [TextArea(1, 1)] [ReadOnly] public string materialFindPathsDesc = "要查找的材质球路径目录.";
        [ShowInInspector]
        [BoxGroup("变体收集")]
        public static HashSet<string> materialFindPaths = new()
        {
            "Assets/WorkArt/Materials",
        };

        [BoxGroup("变体收集")]
        [TextArea(1, 1)] [ReadOnly] public string prefabFindPathsDesc = "预制体路径,从预制体上查找依赖的材质球.";
        [ShowInInspector]
        [BoxGroup("变体收集")]
        public static HashSet<string> prefabFindPaths = new()
        {
            "Assets/WorkArt/Prefabs",
        };

        [BoxGroup("变体收集")]
        [TextArea(1, 1)] [ReadOnly] public string allMaterialKeywordDefinesDesc = "所有材质球的变体定义集";
        [ShowInInspector]
        [BoxGroup("变体收集")]
        public static HashSet<string> allMaterialKeywordDefines = new();

        [BoxGroup("变体收集")]
        [TextArea(1, 10)]
        [ReadOnly]
        public string stepDesc =
            "1.收集材质球路径\n" +
            "2.收集材质球上所有的变体\n" +
            "备注:如果需要多次收集变体,材质球没有新增,只要执行一次收集材质球路径即可 (该周期仅在界面打开时生效,重新打开界面需要重新执行1,2步骤)"
            ;


        [BoxGroup("变体收集")]
        [Button("1.收集材质球路径")]
        private void CollectionMaterials()
        {
            allMaterialsPaths.Clear();

            try
            {
                string[] searchInFolders = prefabFindPaths.ToArray();

                if (searchInFolders.Length > 0)
                {
                    string[] perfabsPath = AssetDatabase.FindAssets("t:Prefab", searchInFolders);
                    // List<Renderer> renderers = new();
                    for (var i = 0; i < perfabsPath.Length; i++)
                    {
                        string perfabPath = AssetDatabase.GUIDToAssetPath(perfabsPath[i]);
                        var deps = AssetDatabase.GetDependencies(perfabPath);
                        var mats = deps.ToList().FindAll((dp) => { return dp.EndsWith(".mat"); });
                        if (mats != null && mats.Count > 0)
                        {
                            foreach (var mat in mats)
                            {
                                allMaterialsPaths.Add(mat);
                            }
                        }

                        if (EditorUtility.DisplayCancelableProgressBar("collection material path:", perfabPath,
                            i * 1.0f / perfabsPath.Length))
                        {
                            EditorUtility.ClearProgressBar();
                            return;
                        }

                    }
                }

                searchInFolders = materialFindPaths.ToArray();

                if (searchInFolders.Length > 0)
                {
                    string[] materialsPath = AssetDatabase.FindAssets("t:Material", searchInFolders);

                    for (var i = 0; i < materialsPath.Length; i++)
                    {
                        string materialPath = AssetDatabase.GUIDToAssetPath(materialsPath[i]);
                        allMaterialsPaths.Add(materialPath);
                        // Material material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);

                        if (EditorUtility.DisplayCancelableProgressBar("collection material path:", materialPath,
                            i * 1.0f / materialsPath.Length))
                        {
                            EditorUtility.ClearProgressBar();
                            return;
                        }

                    }
                }


                // foreach (var materialPath in materialsPaths)
                // {
                //     Material material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
                //     if (material != null)
                //     {
                //         allMaterials.Add(material);
                //     }
                // }

                EditorUtility.ClearProgressBar();
            }
            catch (Exception e)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError("CollectionMaterials:" + e);
            }

            Debug.Log("收集结束");
        }
        [BoxGroup("变体收集")]
        [Button("2.收集材质球上所有的变体")]
        private void CollectionMaterialVariants()
        {
            if (generaterVariantCollection == null)
                return;

            // 1.查找所有预制体下的 Material
            // 2.查找所有材质球
            // 3.找出材质球上的所有Keywords,
            // 4.Material  [GPUInstancing+雾效+烘焙]+材质球定义+[业务逻辑Global定义+阴影]组合起来,传递给ShaderVariants,以完成收集工作
            // 5.Keywords 变体可视化,材质球的所有keywords,所有的变体数

            generaterVariantCollection.Clear();

            EditorUtility.SetDirty(generaterVariantCollection);
            AssetDatabase.SaveAssetIfDirty(generaterVariantCollection);
            AssetDatabase.Refresh();

            CollectionShaderVaiants(generaterVariantCollection, allMaterialsPaths);

            EditorUtility.SetDirty(generaterVariantCollection);
            AssetDatabase.SaveAssetIfDirty(generaterVariantCollection);
            AssetDatabase.Refresh();
        }

        private void RemoveByShader(ShaderVariantCollection shaderVariantCollection, Shader removeShader)
        {
            SerializedObject serializedObject = new(shaderVariantCollection);
            SerializedProperty shaders = serializedObject.FindProperty("m_Shaders");
            for (int i = 0; i < shaders.arraySize; i++)
            {
                var entryProp = shaders.GetArrayElementAtIndex(i);
                Shader shader = (Shader)entryProp.FindPropertyRelative("first").objectReferenceValue;
                if (removeShader == shader)
                {
                    shaders.DeleteArrayElementAtIndex(i);
                    serializedObject.ApplyModifiedProperties();
                    break;
                }

            }
        }

        [Button("ClearProgressBar")]
        private void ClearProgressBar()
        {
            EditorUtility.ClearProgressBar();
        }


        [BoxGroup("变体测试")]
        public string guid;

        [ReadOnly]
        [BoxGroup("变体测试")]
        public string guidPath;

        [BoxGroup("变体测试")]
        public Shader targetShader;

        [BoxGroup("变体测试")]
        public string targetShaderPath;

        [BoxGroup("变体测试")]
        public List<Material> GUIDMaterials = new();

        [BoxGroup("变体测试")]
        [Button("查找GUID的资源路径")]
        public void FindGuidPath()
        {
            guidPath = AssetDatabase.GUIDToAssetPath(guid);
        }

        [BoxGroup("变体测试")]
        [Button("查找GUID的材质球shader")]
        public void FindGuidPath2()
        {
            GUIDMaterials.Clear();
            guidPath = AssetDatabase.GUIDToAssetPath(guid);
            int i = 0;
            foreach (var allMaterialsPath in allMaterialsPaths)
            {
                Material material = AssetDatabase.LoadAssetAtPath<Material>(allMaterialsPath);
                if (material == null)
                    continue;

                var shaderPath = AssetDatabase.GetAssetPath(material.shader);

                if (AssetDatabase.AssetPathToGUID(shaderPath) == guid)
                {
                    GUIDMaterials.Add(material);
                }
                if (EditorUtility.DisplayCancelableProgressBar("FindGuidPath2:", material.name, i * 1.0f / allMaterialsPaths.Count))
                {
                    EditorUtility.ClearProgressBar();
                    return;
                }
                i++;
            }

            EditorUtility.ClearProgressBar();
            Debug.LogError("Finish..");
        }

        [BoxGroup("变体测试")]
        [Button("读取targetShader")]
        public void ReadTargetShader()
        {
            targetShader = AssetDatabase.LoadAssetAtPath<Shader>(targetShaderPath);
        }

        [BoxGroup("变体测试")]
        [Button("将GUIDMaterials的shader换成targetShader")]
        public void ProcessMaterials()
        {
            if (targetShader == null)
                return;

            foreach (var guidMaterial in GUIDMaterials)
            {
                guidMaterial.shader = targetShader;
                EditorUtility.SetDirty(guidMaterial);
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Debug.LogError("Finish..");
        }

        private void CollectionShaderVaiants(ShaderVariantCollection variantCollection, HashSet<string> materialPaths)
        {
            bool processSucc = true;
            try
            {
                ShaderVariantsTools.ComputerShaderKeywordParams keywordParams = new(GlobalShaderKeyWordSet, variantCollection, allMaterialKeywordDefines);
                ShaderVariantsTools.InitParams(keywordParams);
                int i = 0;
                foreach (var materialPath in materialPaths)
                {
                    Material material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
                    if (material == null)
                        continue;

                    i++;
                    if (!ShaderVariantsTools.ComputerMaterialKeyworlds(material))
                    {
                        processSucc = false;
                        EditorUtility.ClearProgressBar();
                        break;
                    }
                    if (EditorUtility.DisplayCancelableProgressBar("collection material path:", material.name, i * 1.0f / materialPaths.Count))
                    {
                        processSucc = false;
                        EditorUtility.ClearProgressBar();
                        return;
                    }

                }
            }
            catch (Exception e)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError("CollectionShaderVaiants:" + e);
            }
            EditorUtility.ClearProgressBar();

            if (processSucc)
            {
                Debug.Log("CollectionShaderVaiants Finish.");
            }
            else
            {
                Debug.LogError("CollectionShaderVaiants Failed.");
            }
        }

  [BoxGroup("变体测试")]
        public Material testMaterial;

        [BoxGroup("变体测试")]
        [Button("测试材质球")]
        private void TestMaterialKeyworlds()
        {
            if (testMaterial == null)
                return;

            if (generaterVariantCollection == null)
                return;


            string path = AssetDatabase.GetAssetPath(testMaterial.shader.GetInstanceID());
            string guid = AssetDatabase.AssetPathToGUID(path);
            Debug.Log($"{guid},{path},", testMaterial);

            ShaderVariantsTools.ComputerShaderKeywordParams keywordParams = new(GlobalShaderKeyWordSet, generaterVariantCollection, allMaterialKeywordDefines);
            ShaderVariantsTools.InitParams(keywordParams);
            // generaterVariantCollection.Clear();
            ShaderVariantsTools.ComputerMaterialKeyworlds(testMaterial);
            EditorUtility.SetDirty(generaterVariantCollection);
            AssetDatabase.SaveAssetIfDirty(generaterVariantCollection);
            AssetDatabase.Refresh();
        }

        public ShaderVariantCollection[] ShaderVariantCollections;

        [Button]
        public void FindAllShaderVariants()
        {
            var objs = Resources.FindObjectsOfTypeAll<ShaderVariantCollection>();

            ShaderVariantCollections = objs;

        }

        public List<Shader> shaders = new();
        // [Button]
        // public void LitTest()
        // {
        //     var allShader = Game.Framework.GameResMgr.Ins.GetAllShaders();
        //     shaders = allShader.Select(a => a.Value).ToList();
        // }


        public MeshRenderer meshRenderer;

        public Shader materialShader;

        public Shader abShader;


        [Button]
        public void TestMaterial()
        {
            if (meshRenderer == null)
                return;

            materialShader = meshRenderer.sharedMaterial.shader;

            string name = "Universal Render Pipeline/Lit";


           //prefabs/scene/o_scene_main_city_ex.prefab,
           abShader = shaders.Find(a => a.name == name);
            //Assets/ArtResources/SpaceScenes/InnerCityBuilding/fbx/Materials/Research Centers.mat
        }
        public string assetPath = "prefabs/scene/o_scene_main_city_ex.prefab";

        [Button]
        public void TestCity()
        {

            Game.Framework.GameResMgr.Ins.UnloadAssetImmediate(assetPath, true);
            /*var go = Game.Framework.GameResMgr.Ins.LoadPrefabSync(assetPath);
            GameObject.Instantiate(go);*/
        }
    }
}