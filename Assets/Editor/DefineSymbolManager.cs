using UnityEditor;
using UnityEngine;

public class DefineSymbolManager
{
    // 添加定义符号
    public static void AddDefineSymbol(string symbol)
    {
        BuildTargetGroup buildTargetGroup = EditorUserBuildSettings.selectedBuildTargetGroup;
        string defines = PlayerSettings.GetScriptingDefineSymbolsForGroup(buildTargetGroup);

        if (!defines.Contains(symbol))
        {
            if (!string.IsNullOrEmpty(defines))
                defines += ";";
            defines += symbol;
            PlayerSettings.SetScriptingDefineSymbolsForGroup(buildTargetGroup, defines);
            Debug.Log($"添加符号: {symbol}");
        }
    }

    // 移除定义符号
    public static void RemoveDefineSymbol(string symbol)
    {
        BuildTargetGroup buildTargetGroup = EditorUserBuildSettings.selectedBuildTargetGroup;
        string defines = PlayerSettings.GetScriptingDefineSymbolsForGroup(buildTargetGroup);

        if (defines.Contains(symbol))
        {
            string[] symbols = defines.Split(';');
            System.Text.StringBuilder newDefines = new System.Text.StringBuilder();

            foreach (string s in symbols)
            {
                if (s != symbol && !string.IsNullOrEmpty(s))
                {
                    if (newDefines.Length > 0)
                        newDefines.Append(";");
                    newDefines.Append(s);
                }
            }

            PlayerSettings.SetScriptingDefineSymbolsForGroup(buildTargetGroup, newDefines.ToString());
            Debug.Log($"移除符号: {symbol}");
        }
    }

    // 检查符号是否存在
    public static bool HasDefineSymbol(string symbol)
    {
        BuildTargetGroup buildTargetGroup = EditorUserBuildSettings.selectedBuildTargetGroup;
        string defines = PlayerSettings.GetScriptingDefineSymbolsForGroup(buildTargetGroup);
        return defines.Contains(symbol);
    }
}