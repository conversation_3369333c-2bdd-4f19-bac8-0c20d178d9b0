using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEditor;
using UnityEditor.UI;
using UnityEngine;
using UIFramework;
using UnityEditor.U2D;
[CustomEditor(typeof(UIButton))]
public class UIButtonEditor : ButtonEditor {

    UIButton button;
    SerializedProperty disableImg;
    SerializedProperty disableTextMat;
    SerializedProperty btnText;
    protected override void OnEnable()
    {
        base.OnEnable();

        button = target as UIButton;
        disableImg = serializedObject.FindProperty("disableImg");
        disableTextMat = serializedObject.FindProperty("disableTextMat");
        btnText = serializedObject.FindProperty("btnText");
        
        //Debug.LogFormat("OnEnable {0}", disableImg.propertyType);
    }

    public override void OnInspectorGUI()
    {
        bool isDirty = false;
        /*bool canPassEvent = EditorGUILayout.Toggle("PassEvent", button.canPassEvent);
        if (canPassEvent != button.canPassEvent)
        {
            button.canPassEvent = canPassEvent;
        }*/

        /*string btnName = EditorGUILayout.TextField("BtnName", button.btnName);
        if(btnName != button.btnName) {
            button.btnName = btnName;
        }*/
        EditorGUI.BeginChangeCheck();
        /*ButtonType buttonType = (ButtonType)EditorGUIEnumPopup.EnumPopup("按钮类型", button.buttonType);
        if (buttonType != button.buttonType)
        {
            if (button.buttonType == ButtonType.None)
            {
                var wImages = button.gameObject.GetComponentsInChildren<WImage>();
                if (button.buttonBg == null && wImages != null && wImages.Length == 1)
                {
                    button.buttonBg = wImages[0];
                }
                
                var texts = button.gameObject.GetComponentsInChildren<UITextMeshProUGUI>();
                if (button.butttonTxt == null && texts != null && texts.Length == 1)
                {
                    button.butttonTxt = texts[0];
                }
            }
            
            button.buttonType = buttonType;
            button.ChgButtonType();
        }
        if (buttonType != ButtonType.None)
        {
            EditorGUILayout.PropertyField(buttonBg);
            EditorGUILayout.PropertyField(butttonTxt);
            button.btnScaler = EditorGUILayout.Slider("按钮缩放",button.btnScaler,0,1.5f);

            if (button.buttonBg != null && GUILayout.Button("重新加载按钮类型"))
            {
                button.ChgButtonType();
            }
            
            if (button.buttonBg != null && GUILayout.Button("缩放至指定大小"))
            {
                button.ChgButtonType();
                var sizeDelta = button.buttonBg.GetComponent<RectTransform>().sizeDelta;
                sizeDelta *= button.btnScaler;
                button.buttonBg.GetComponent<RectTransform>().sizeDelta = sizeDelta;

                sizeDelta = button.GetComponent<RectTransform>().sizeDelta;
                sizeDelta *= button.btnScaler;
                button.GetComponent<RectTransform>().sizeDelta = sizeDelta;
                button.butttonTxt.fontSize *= button.btnScaler;
            }
        }*/
        

        if (EditorGUI.EndChangeCheck())
            serializedObject.ApplyModifiedProperties();



        /*int param1 = EditorGUILayout.IntField("Param1", button.param1);
        if (param1 != button.param1) {
            button.param1 = param1;
        }

        string param2 = EditorGUILayout.TextField("Param2", button.param2);
        if (param2 != button.param2) {
            button.param2 = param2;
        }*/

        bool playAudio = EditorGUILayout.Toggle("PlayAudio", button.playAudio);
        if (playAudio != button.playAudio)
        {
            button.playAudio = playAudio;
            isDirty = true;
        }

        int audioId = EditorGUILayout.IntField("AudioId", button.audioId);
        if (audioId != button.audioId) {
            button.audioId = audioId;
            isDirty = true;
        }

        float pressTime = EditorGUILayout.FloatField("PressTime", button.pressTime);
        if (pressTime != button.pressTime)
        {
            button.pressTime = pressTime;
            isDirty = true;
        }

        float pressRate = EditorGUILayout.FloatField("PressRepeat", button.pressRepeat);
        if(pressRate != button.pressRepeat) {
            button.pressRepeat = pressRate;
            isDirty = true;
        }

        bool canPassEvent = EditorGUILayout.Toggle("canPassEvent", button.canPassEvent);
        if(canPassEvent != button.canPassEvent) {
            button.canPassEvent = canPassEvent;
            isDirty = true;
        }

        disableImg.boxedValue = button.disableImg;
        disableTextMat.boxedValue = button.disableTextMat;
        btnText.boxedValue = button.btnText;

        EditorGUILayout.PropertyField(disableImg);
        EditorGUILayout.PropertyField(disableTextMat);
        EditorGUILayout.PropertyField(btnText);

        if (button.disableImg != disableImg.boxedValue as Sprite)
        {
            button.disableImg = disableImg.boxedValue as Sprite;
            isDirty = true;
        }
            

        if (button.btnText != btnText.boxedValue as UITextMeshProUGUI)
        {
            button.btnText = btnText.boxedValue as UITextMeshProUGUI;
            isDirty = true;
        }
            

        if (button.disableTextMat != disableTextMat.boxedValue as Material)
        {
            button.disableTextMat = disableTextMat.boxedValue as Material;
            isDirty = true;
        }

        base.OnInspectorGUI();

        if (isDirty)
        {
            EditorUtility.SetDirty(button); 
        }
    }
    
    
}


[CustomEditor(typeof(UIDragButton))]
public class UIDragButtonEditor : UIButtonEditor
{
    
}
