using System;
using UnityEngine;

namespace Core
{
   public  class LayerTools
    {
        /// <summary>
        /// 默认层级
        /// </summary>
        public const int Layer_Default = 0;
        /// <summary>
        /// UI层级
        /// </summary>
        public const int Layer_UI = 5;
        /// <summary>
        /// 设置层级
        /// </summary>
        /// <param name="go"></param>
        /// <param name="layer"></param>
        public static void SetLayer(GameObject go, int layer)
        {
            if (go == null) return;

            _SetChildNodeLayer(go.transform, layer);
        }
        private static void _SetChildNodeLayer(Transform trans, int layer)
        {
            //无GC
            for (int i = 0; i < trans.childCount; i++)
            {
                trans.GetChild(i).gameObject.layer = layer;
                if (trans.GetChild(i).childCount > 0)
                {
                    _SetChildNodeLayer(trans.GetChild(i), layer);
                }
            }
        }
    }
}
