using System;

namespace Core
{
	public static class ArrayTools
	{
		private static Func<Type, int[], int[], Array> _lpfnCreateInstanceImpl;

		private static int[] _createInstanceImpl_lengths;

		public static int EnsureCapacity(int currentCapacity, int minCapacity)
		{
			int num = 0;
			num = ((currentCapacity <= 0) ? 4 : ((currentCapacity > 256) ? (currentCapacity + 256) : (currentCapacity << 1)));
			if (num < minCapacity)
			{
				num = minCapacity;
			}
			return num;
		}

		internal static Array CreateInstanceImpl(Type elementType, int length)
		{
			if (null == _lpfnCreateInstanceImpl)
			{
				string typeName = "System.Array,mscorlib";
				string methodName = "CreateInstanceImpl";
				Type type = Type.GetType(typeName);
				TypeTools.CreateDelegate(type, methodName, out _lpfnCreateInstanceImpl);
				_createInstanceImpl_lengths = new int[1];
			}
			if (null != _lpfnCreateInstanceImpl)
			{
				_createInstanceImpl_lengths[0] = length;
				return _lpfnCreateInstanceImpl(elementType, _createInstanceImpl_lengths, null);
			}
			return null;
		}
	}
}
