using System;
using System.Collections;
using System.IO;

namespace Core.IO
{
	public class CsvWriter : IDisposable
	{
		private  StreamWriter _writer;

		private readonly char[] _escapedChars = new char[2]
		{
			'"',
			','
		};

		public CsvWriter(string path)
		{
			_writer = new StreamWriter(path);
			_writer.NewLine = "\n";
		}
        public void Flush()
        {
            if(_writer != null)
            {
                _writer.Flush();
            }
           
        }
		public void Dispose()
		{
            if (_writer != null)
            {
                _writer.Close();
                _writer.Dispose();
                _writer = null;
            }
        }

		public void WriteRow(ArrayList items, bool autoClearItems)
		{
			if (items != null && items.Count > 0)
			{
				_WriteItem(items[0] as string);
				int count = items.Count;
				for (int i = 1; i < count; i++)
				{
					_writer.Write(',');
					string item = items[i] as string;
					_WriteItem(item);
				}
				_writer.WriteLine();
				if (autoClearItems)
				{
					items.Clear();
				}
			}
		}

		private void _WriteItem(string item)
		{
			item = (item ?? string.Empty);
			if (item.IndexOfAny(_escapedChars) == -1)
			{
				_writer.Write(item);
				return;
			}
			_writer.Write('"');
			_writer.Write(item.Replace("\"", "\"\""));
			_writer.Write('"');
		}
	}
}
