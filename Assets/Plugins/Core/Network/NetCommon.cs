using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Core.Network
{
    public static class NetCommon
    {
        private static readonly HashSet<int> _waitConfirmBeanTypes = new HashSet<int>();

        public static void Init(int maxReconnectCount, float waitNetworkDelay, float reconnectDelay, int[] waitConfirmBeanTypes, float idelReconnectTime, float battleReconnectTime, float disconnectDelay, float keepAliveInterval)
        {
            NetCommon.maxReconnectCount = maxReconnectCount;
            NetCommon.waitNetworkDelay = waitNetworkDelay;
            NetCommon.reconnectDelay = reconnectDelay;
            reconnectTimeoutInIdel = idelReconnectTime;
            reconnectTimeoutInBattle = battleReconnectTime;
            NetCommon.disconnectDelay = disconnectDelay;
            NetCommon.keepAliveInterval = keepAliveInterval;
            if (waitConfirmBeanTypes != null)
            {
                foreach (int num in waitConfirmBeanTypes)
                {
                    _waitConfirmBeanTypes.Add(num);
                }
            }
        }

        public static bool IsWaitConfirmBeanType(int beanType)
        {
            return _waitConfirmBeanTypes.Contains(beanType);
        }

        public static int maxReconnectCount
        {
            get;
            private set;
        }

        public static float waitNetworkDelay
        {
            get;
            private set;
        }

        public static float reconnectDelay
        {
            get;
            private set;
        }

        public static float reconnectTimeoutInIdel
        {
            get;
            private set;
        }

        public static float reconnectTimeoutInBattle
        {
            get;
            private set;
        }

        public static float disconnectDelay
        {
            get;
            private set;
        }

        public static float keepAliveInterval
        {
            get;
            private set;
        }
    }
}
