using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Sockets;
namespace Core.Network
{
   /// <summary>
   /// 网络客户端
   /// </summary>
    public class NetClient : IDisposable
    {
        //相关接口
        public delegate void SocketConnectedDelegate(int linkId);
        public delegate void SocketErrorDelegate(int linkId, int errorType, int errorCode, string errorMsg);
        public delegate void SocketRecvDelegate(int linkId, IBean bean);
        /// <summary>
        /// Socket连接上
        /// </summary>
        public SocketConnectedDelegate OnSocketConnected;
        /// <summary>
        /// Socket错误
        /// </summary>
        public SocketErrorDelegate OnSocketError;
        /// <summary>
        /// Socket收到包
        /// </summary>
        public SocketRecvDelegate OnSocketRecv;
        /// <summary>
        /// 链接Id
        /// </summary>
        private int _linkId;
        /// <summary>
        /// 链接的服务端IP
        /// </summary>
        private string _lastHost;
        /// <summary>
        /// 链接服务端端口
        /// </summary>
        private int _lastPort;
        /// <summary>
        /// Socket处理
        /// </summary>
        private NetSession _session;
        /// <summary>
        /// 标记
        /// </summary>
        private bool _isSessionAdded;
        

        private readonly Action<IBean> _lpfnProcessOneBean;

        public NetClient( int linkId)
        {
            _linkId = linkId;
            _lpfnProcessOneBean = _ProcessOneBean;
        }

        
        private void _CloseSession()
        {
            if (_session != null)
            {
                _session.Close();
                _session = null;
                DLogger.Warning("[_CloseSession()], LinkId = {0}", _linkId);
            }
        }

        protected virtual void _DoDispose(bool disposing)
        {
            Close();
            OnSocketConnected = null;
            OnSocketError = null;
            OnSocketRecv = null;
        }

        protected virtual void _OnAddSession()
        {
        }
        protected virtual void _OnDelSession()
        {
        }
        private void _onSocketConnected()
        {
            _OnAddSession();
            _isSessionAdded = true;
            if(OnSocketConnected != null)
            {
                OnSocketConnected(_linkId);
            }
        }
        protected virtual OctetsStream _OnEncode(byte[] buf, int pos, int len)
        {
            return null;
        }

        public virtual OctetsStream _OnDecode(byte[] buf, int pos, int len)
        {
            return null;
        }

        /// <summary>
        /// 处理Socket错误
        /// </summary>
        /// <param name="errorType"></param>
        /// <param name="ex"></param>
        private void _OnSessionException(NetStateError errorType, Exception ex)
        {

            SocketError code = SocketError.SocketError;
            if (ex.GetType() == typeof(SocketException))
            {
                code = ((SocketException)ex).SocketErrorCode;
            }

            if (code == SocketError.TimedOut && errorType == NetStateError.ConnectError)
            {
                errorType = NetStateError.TimeOut;
            }
            else if (code == SocketError.Interrupted)
            {
                Core.DLogger.Log("SocketError.Interrupted");
            }
            if (OnSocketError != null)
            {
                OnSocketError(_linkId, (int)errorType, (int)code, ex.Message);
            }

            if (_session == null) //已经关闭连接了就不走下面的了
                return;

            _CloseSession();
        }
        /// <summary>
        /// 处理一个消息包
        /// </summary>
        /// <param name="bean"></param>
        private void _ProcessOneBean(IBean bean)
        {
            try
            {
                //处理消息
                _HandleOneBean(bean);
            }
            catch(Exception exp)
            {
                DLogger.Error("[NetClient]_ProcessOneBean Exception:{0}", exp.Message);
            }
        }
        /// <summary>
        /// 消息处理
        /// </summary>
        /// <param name="bean"></param>
        /// <returns></returns>
        private bool _HandleOneBean(IBean bean)
        {
            if(OnSocketRecv != null)
            {
                OnSocketRecv(_linkId, bean);
            }
            return true;
        }

        public void Close()
        {
            if (_isSessionAdded)
            {
                _isSessionAdded = false;
                _CloseSession();
                _OnDelSession();
            }
        }
        public void CloseTest()
        {
            if(_session != null)
            {
                _session.CloseTest();
            }
        }

        public void ForceCloseSocket()
        {
            if (_session != null)
            {
                _session.ForceCloseSocket();
            }
        }

        /// <summary>
        /// 连接
        /// </summary>
        /// <param name="host">IP地址</param>
        /// <param name="port">端口</param>
        public void Connect(string host, int port)
        {
            if (string.IsNullOrEmpty(host) || (port <= 0))
            {
                throw new ArgumentOutOfRangeException(string.Format("Invalid host={0}, port={1}, linkId={2}", host, port, _linkId));
            }
            _lastHost = host;
            _lastPort = port;
            DLogger.Log("++++++++++ new NetSession(), host={0}, port={1}, linkId={2}", host, port, _linkId);
            NetSession session = new NetSession(this);
            session.Connect(host, port, _onSocketConnected);
            _session = session;
        }

        public void Dispose()
        {
            _DoDispose(true);
        }

        public string GetHostName()
        {
            return _lastHost;
        }

        public int GetPort()
        {
            return _lastPort;
        }
        public NetSession GetSession()
        {
            return _session;
        }
        /// <summary>
        /// 发送网络消息
        /// </summary>
        /// <param name="commandId">消息id</param>
        /// <param name="message"></param>
        /// <param name="packageIndex">包序列号</param>
        /// <returns></returns>
        public bool SendDirect(int commandId, byte[] message, int packageIndex)
        {
            if (Connected)
            {
                OctetsStream osBean = _MarshalOneBean(commandId, message, packageIndex);
                _session.Send(osBean);
                return true;
            }
            else
            {
                _OnSessionException(NetStateError.SendError, new Exception("Socket Session Close"));
                return false;
            }
        }
        /// <summary>
        /// 消息头长度
        /// </summary>
        private const int MESSAGE_HEAD_LEN = 8;
        /// <summary>
        /// CRC偏移量
        /// </summary>
        private const int CRC_OFFSET = 4;
        private OctetsStream _MarshalOneBean(int commandId, byte[] message, int packageIndex)
        {
            OctetsStream stream = new OctetsStream();
            int msgLength = message.Length + MESSAGE_HEAD_LEN;
            stream.Marshal2(msgLength);
            stream.Marshal2(0);
            stream.Marshal2(packageIndex);
            stream.Marshal4(commandId);
            stream.Append(message);

            byte[] crc = CRCTools.CRC16Ex(stream, CRC_OFFSET);
            stream.SetByte(2, crc[1]);
            stream.SetByte(3, crc[0]);

            return stream;
        }

        public virtual void Reconnect()
        {
            if (!string.IsNullOrEmpty(_lastHost) && _lastPort > 0)
            {
                Connect(_lastHost, _lastPort);
            }
        }

        public void Tick(float deltaTime)
        {
            bool isReceivedBeans = false;
            if (_isSessionAdded)
            {
                if (_session != null)
                {
                    isReceivedBeans = _session.ProcessReceivedBeans(_lpfnProcessOneBean) > 0;
                }
            }
            if (_session != null)
            {
                if (_session.GetException() != null && !isReceivedBeans)
                {
                    _OnSessionException(_session.GetStateError(), _session.GetException());
                }
            }
        }
        /// <summary>
        /// 是否连接上
        /// </summary>
        public bool Connected
        {
            get
            {
                return _session != null && _session.Connected;
            }
        }

        public delegate IBean BeanDelegate();

        public delegate void HandlerDelegate(NetClient client, IBean arg);
    }
}
