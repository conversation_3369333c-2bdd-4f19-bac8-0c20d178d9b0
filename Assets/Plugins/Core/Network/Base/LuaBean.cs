using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Core.Network
{
    [Serializable]
    public class LuaBean : IBean, IEquatable<LuaBean>, IComparable<LuaBean>
    {
        public const int BEAN_TYPE = 0x5f5e100;
        public object bean;
        /// <summary>
        /// 消息id
        /// </summary>
        public int commandId;
        /// <summary>
        /// 回包错误码
        /// </summary>
        public int retCode;
        /// <summary>
        /// 消息长度
        /// </summary>
        public int size;

        /// <summary>
        /// 消息索引号
        /// </summary>
        public int pIndex;

        public OctetsStream os;

        public int Serial
        {
            get
            {
                throw new NotImplementedException();
            }

            set
            {
                throw new NotImplementedException();
            }
        }

        public LuaBean()
        {
        }

        public LuaBean(object LuaBean)
        {
            this.os = null;
            this.commandId = 0;
            this.size = 0;
            this.retCode = 0;
        }

        public LuaBean(OctetsStream newOs, int commandId, int size)
        {
            os = newOs;
            this.commandId = commandId;
            this.size = size;
            this.retCode = 0;
            this.bean = null;
        }

        public LuaBean(OctetsStream newOs, int commandId, int size, int retCode)
        {
            this.os = newOs;
            this.commandId = commandId;
            this.size = size;
            this.retCode = retCode;
            this.bean = null;
        }

        public void Assign(ref LuaBean b)
        {
            this.bean = b.bean;
        }

        public object Clone()
        {
            return new LuaBean(this.bean);
        }

        public int CompareTo(IBean b)
        {
            return 1;
        }

        public int CompareTo(LuaBean b)
        {
            return 0;
        }

        public int CompareTo(object b)
        {
            return (!(b is IBean) ? 1 : this.CompareTo((IBean)b));
        }

        public static LuaBean Create()
        {
            return new LuaBean();
        }

        public static IBean CreateIBean()
        {
            return new LuaBean();
        }

        public bool Equals(LuaBean b)
        {
            return true;
        }

        public override bool Equals(object o)
        {
            return true;
        }

        public override int GetHashCode()
        {
            return 1;
        }

        public void Init()
        {
        }

        public int InitSize()
        {
            return 0x40;
        }

        public Octets Marshal(Octets s)
        {
            return s;
        }

        public int MaxSize()
        {
            return 0x400;
        }

        public void Reset()
        {
        }

        public int GetRetCode()
        {
            return this.retCode;
        }

        public void SetPIndex(int pIndex)
        {
            this.pIndex = pIndex;
        }

        public int Type()
        {
            return this.commandId;
        }

        public OctetsStream Unmarshal(OctetsStream s)
        {
            return s;
        }

        public override string ToString()
        {
            return "LuaBean";
        }
        public StringBuilder ToJson(StringBuilder s)
        {
            if (s == null) s = new StringBuilder();
            s.Append("{\"t\":").Append(commandId);
            return s.Append('}');
        }

        public StringBuilder ToJson()
        {
            return ToJson(null);
        }

        public StringBuilder ToLua(StringBuilder s)
        {
            if (s == null) s = new StringBuilder();
            s.Append("{t=").Append(commandId);
            return s.Append('}');
        }

        public StringBuilder ToLua()
        {
            return ToLua(null);
        }
    }
}
