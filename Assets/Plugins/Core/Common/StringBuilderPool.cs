using System.Collections.Generic;
using System.Text;

internal static class StringBuilderPool
{
    // Object pool to avoid allocations.
    private static readonly Pool<StringBuilder> stringBuilderPool = new Pool<StringBuilder>(
        () => new StringBuilder(),
        null,
        sb => sb.Length = 0);

    public static StringBuilder Get()
    {
        return stringBuilderPool.Get();
    }

    public static void Release(StringBuilder toRelease)
    {
        stringBuilderPool.Put(toRelease);
    }
}
