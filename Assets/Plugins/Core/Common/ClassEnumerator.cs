using System;
using System.Reflection;
using UnityEngine;
using Core.Collections;
namespace Core
{
    public class AutoRegisterAttribute : Attribute
    {
    }
    public class ClassEnumerator
    {
        protected ListView<System.Type> Results = new ListView<System.Type>();
        private System.Type AttributeType;
        private System.Type InterfaceType;

        public ClassEnumerator(System.Type InAttributeType, System.Type InInterfaceType, Assembly InAssembly, bool bIgnoreAbstract, bool bInheritAttribute, bool bShouldCrossAssembly)
        {
            AttributeType = InAttributeType;
            InterfaceType = InInterfaceType;
            try
            {
                if (bShouldCrossAssembly)
                {
                    Assembly[] assemblies = AppDomain.CurrentDomain.GetAssemblies();
                    if (assemblies != null)
                    {
                        for (int i = 0; i < assemblies.Length; i++)
                        {
                            Assembly inAssembly = assemblies[i];
                            CheckInAssembly(inAssembly, bIgnoreAbstract, bInheritAttribute);
                        }
                    }
                }
                else
                {
                    CheckInAssembly(InAssembly, bIgnoreAbstract, bInheritAttribute);
                }
            }
            catch (Exception exception)
            {
                DLogger.Error("Error in enumerate classes :" + exception.Message);
            }
        }

        protected void CheckInAssembly(Assembly InAssembly, bool bInIgnoreAbstract, bool bInInheritAttribute)
        {
            System.Type[] types = InAssembly.GetTypes();
            if (types != null)
            {
                for (int i = 0; i < types.Length; i++)
                {
                    System.Type type = types[i];
                    if(InterfaceType == null || InterfaceType.IsAssignableFrom(type))
                    {
                        if(!bInIgnoreAbstract || (bInIgnoreAbstract && !type.IsAbstract))
                        {
                            if(type.GetCustomAttributes(AttributeType, bInInheritAttribute).Length > 0)
                            {
                                Results.Add(type);
                            }
                        }
                    }
                }
            }
        }

        public ListView<System.Type> results
        {
            get
            {
                return Results;
            }
        }
    }
}


