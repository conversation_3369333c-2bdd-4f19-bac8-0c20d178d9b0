using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;

namespace Core
{
    /// <summary>
    /// 扩展ScrollRect，避免Lua内存泄露
    /// </summary>
    public static class ExtendedScrollRect
    {
        #region Unity
        /// <summary>
        /// 扩展ScrollRect.velocity
        /// </summary>
        public static void GetVelocityEx(this ScrollRect target, out float x, out float y)
        {
            x = 0;
            y = 0;
            Vector2 velocity = target.velocity;
            x = velocity.x;
            y = velocity.y;
        }
        /// <summary>
        /// 扩展ScrollRect.velocity
        /// </summary>
        public static void SetVelocityEx(this ScrollRect target, float x, float y)
        {
            target.velocity = new Vector2(x, y);
        }
        /// <summary>
        /// 扩展ScrollRect.normalizedPosition
        /// </summary>
        public static void GetNormalizedPositionEx(this ScrollRect target, out float x, out float y)
        {
            x = 0;
            y = 0;
            Vector2 normalizedPosition = target.normalizedPosition;
            x = normalizedPosition.x;
            y = normalizedPosition.y;
        }
        /// <summary>
        /// 扩展ScrollRect.normalizedPosition
        /// </summary>
        public static void SetNormalizedPositionEx(this ScrollRect target, float x, float y)
        {
            target.normalizedPosition = new Vector2(x, y);
        }
        #endregion
        #region DG.Tweening
        /// <summary>
        /// 扩展ScrollRect.DONormalizedPos
        /// </summary>
        public static Tweener DONormalizedPosEx(this ScrollRect target, float xPos, float yPos, float duration, bool snapping = false)
        {
            return target.DONormalizedPos(new Vector2(xPos, xPos), duration, snapping);
        }
        #endregion
    }
}
