using System;
using UnityEngine;
using TMPro;

namespace Core
{
   public static class ExtendedTMP_InputField
    {
        private static Color _colorReuse = Color.white;
        /// <summary>
        /// TMP_InputField.selectionColor
        /// </summary>
        public static void GetSelectionColorEx(this TMP_InputField target, out float r, out float g, out float b, out float a)
        {
            r = 0;
            g = 0;
            b = 0;
            a = 0;
            Color color = target.selectionColor;
            r = color.r;
            g = color.g;
            b = color.b;
            a = color.a;
        }
        /// <summary>
        /// TMP_InputField.selectionColor
        /// </summary>
        public static void SetColorEx(this TMP_InputField target, float r, float g, float b, float a)
        {
            _colorReuse.r = r;
            _colorReuse.g = g;
            _colorReuse.b = b;
            _colorReuse.a = a;
            target.selectionColor = _colorReuse;
        }
        /// <summary>
        /// TMP_InputField.caretColor
        /// </summary>
        public static void GetCaretColorEx(this TMP_InputField target, out float r, out float g, out float b, out float a)
        {
            r = 0;
            g = 0;
            b = 0;
            a = 0;
            Color color = target.caretColor;
            r = color.r;
            g = color.g;
            b = color.b;
            a = color.a;
        }
        /// <summary>
        /// TMP_InputField.caretColor
        /// </summary>
        public static void SetCaretColorEx(this TMP_InputField target, float r, float g, float b, float a)
        {
            _colorReuse.r = r;
            _colorReuse.g = g;
            _colorReuse.b = b;
            _colorReuse.a = a;
            target.caretColor = _colorReuse;
        }
    }
}
