#define UNITY_EDITOR
using System;
using System.Collections;

namespace Core
{
	public static class CoroutineMgr
	{
		private static readonly CoroutinePool _pool;

		static CoroutineMgr()
		{
			_pool = new CoroutinePool();
		}

		public static void StartCoroutine(IEnumerator routine)
		{
			if (null == routine)
			{
				return;
			}
			try
			{
				if (TickTools.IsTimeout() || routine.MoveNext())
				{
					_pool.Spawn(routine, isRecyclable: true);
				}
			}
			catch (Exception ex)
			{
				DLogger.Error("[CoroutineManager.StartCoroutine()] ex={0}", ex);
			}
		}

		public static void StartCoroutine(IEnumerator routine, out CoroutineItem item)
		{
			item = null;
			if (null == routine)
			{
				return;
			}
			try
			{
				if (TickTools.IsTimeout() || routine.MoveNext())
				{
					item = _pool.Spawn(routine, isRecyclable: false);
				}
			}
			catch (Exception ex)
			{
                DLogger.Error("[CoroutineManager.StartCoroutine()] ex={0}", ex);
			}
		}

		public static bool KillCoroutine(IEnumerator routine)
		{
			if (null != routine)
			{
				int count = _pool.Count;
				for (int i = 0; i < count; i++)
				{
					CoroutineItem coroutineItem = _pool[i];
					if (coroutineItem.routine == routine)
					{
						coroutineItem.Kill();
						return true;
					}
				}
			}
			return false;
		}

		public static void KillCoroutine(ref IEnumerator routine)
		{
			if (null != routine)
			{
				KillCoroutine(routine);
				routine = null;
			}
		}

		internal static void Tick(float deltaTime)
		{
			if (_pool.Count <= 0)
			{
				return;
			}
			bool isRecycle = false;
			int count = _pool.Count;
			for (int i = 0; i < count; i++)
			{
				if (TickTools.IsTimeout())
				{
					break;
				}
				CoroutineItem coroutineItem = _pool[i];
				if (!coroutineItem.isDone && !coroutineItem.isKilled)
				{
					try
					{
						IEnumerator routine = coroutineItem.routine;
						IIsYieldable isYieldable = routine.Current as IIsYieldable;
						if (isYieldable == null || isYieldable.isYieldable)
						{
							coroutineItem.isDone = !routine.MoveNext();
						}
					}
					catch (Exception ex)
					{
						coroutineItem.isDone = true;
                        DLogger.Error("[CoroutineManager.Tick()] ex={0}, StackTrace={1}", ex, ex.StackTrace);
					}
				}
				if (coroutineItem.isDone || coroutineItem.isKilled)
				{
                    isRecycle = true;
				}
			}
			if (isRecycle)
			{
				_pool.Recycle();
			}
		}

		internal static void Clear()
		{
			_pool.Clear();
		}
	}
}
