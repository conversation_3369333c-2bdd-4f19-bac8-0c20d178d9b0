using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.Build.Content;
using UnityEditor.Build.Pipeline;
using UnityEditor.Build.Pipeline.Interfaces;
using UnityEngine;
using UnityEngine.Build.Pipeline;

namespace BuildsAssets
{
    public class BuildPipelineUtils
    {
        
        public static CompatibilityAssetBundleManifest BuildAssetBundles(EBuildPippeline pippeline,string buildId,
            string outputPath,List<AssetBundleBuild> bundleBuilds,BuildAssetBundleOptions options, BuildTarget targetPlatform)
        {
            CompatibilityAssetBundleManifest bundleManifest = null;
            if (pippeline == EBuildPippeline.UniversalBuildPipeline)
            {
                BundleBuildContent content = new(bundleBuilds); 
                bundleManifest = BuildAssetBundlesFromSBP(buildId,outputPath,
                    content,options,targetPlatform);
                if (bundleManifest == null)
                {
                    Debug.LogError("Build Faild.");
                    return null;
                }
            }
            else if (pippeline == EBuildPippeline.AssetBundle_ScriptableBuildPipeline)
            {
                bundleManifest = AB_BuildPipeline.Run(outputPath,buildId ,options,
                    targetPlatform, bundleBuilds);
            }
            else if (pippeline == EBuildPippeline.Raw_ScriptableBuildPipeline)
            {
                bundleManifest = RawBuildPipeline.Run(outputPath,buildId ,options,
                    targetPlatform, bundleBuilds);
            }
            else
            {
                throw new NotSupportedException($"{pippeline}");
                return null;
            }
            return bundleManifest;
        } 
        
        public static CompatibilityAssetBundleManifest BuildAssetBundlesFromSBP(string buildId,
            string outputPath, IBundleBuildContent content, BuildAssetBundleOptions options, BuildTarget targetPlatform)
        {
            var group = BuildPipeline.GetBuildTargetGroup(targetPlatform);
            var parameters = new BundleBuildParametersEx(buildId,targetPlatform, group, outputPath);
            if ((options & BuildAssetBundleOptions.ForceRebuildAssetBundle) != 0)
                parameters.UseCache = false;

            if ((options & BuildAssetBundleOptions.AppendHashToAssetBundleName) != 0)
                parameters.AppendHash = true;

#if UNITY_2018_3_OR_NEWER
            if ((options & BuildAssetBundleOptions.ChunkBasedCompression) != 0)
                parameters.BundleCompression = UnityEngine.BuildCompression.LZ4;
            else if ((options & BuildAssetBundleOptions.UncompressedAssetBundle) != 0)
                parameters.BundleCompression = UnityEngine.BuildCompression.Uncompressed;
            else
                parameters.BundleCompression = UnityEngine.BuildCompression.LZMA;
#else
            if ((options & BuildAssetBundleOptions.ChunkBasedCompression) != 0)
                parameters.BundleCompression = BuildCompression.DefaultLZ4;
            else if ((options & BuildAssetBundleOptions.UncompressedAssetBundle) != 0)
                parameters.BundleCompression = BuildCompression.DefaultUncompressed;
            else
                parameters.BundleCompression = BuildCompression.DefaultLZMA;
#endif

            if ((options & BuildAssetBundleOptions.DisableWriteTypeTree) != 0)
                parameters.ContentBuildFlags |= ContentBuildFlags.DisableWriteTypeTree;

            IBundleBuildResults results;
            ReturnCode exitCode = ContentPipeline.BuildAssetBundles(parameters, content, out results);
            if (exitCode < ReturnCode.Success)
                return null;

            var manifest = ScriptableObject.CreateInstance<CompatibilityAssetBundleManifest>();
            manifest.SetResults(results.BundleInfos);
            return manifest;
        }


        public static AssetBundleManifest CompatibilityAssetBundleManifestToAssetBundleManifest(CompatibilityAssetBundleManifest bundleManifest)
        {
            AssetBundleManifest results = new();
            var bundleNames =  bundleManifest.GetAllAssetBundles();
            
            for (var i = 0; i < bundleNames.Length; i++)
            {
                string bName = bundleNames[i];
                string[] dep = bundleManifest.GetAllDependencies(bName);
                List<string> depList = new List<string>();
                for (int j = 0; j < dep.Length; j++)
                {
                    string str = dep[j];
                    /*if (bName.StartsWith("prefabs/ui/")) //UIprefab去除图片依赖
                    {
                        if (str.EndsWith(".spriteatlas") || str.EndsWith(".png") || str.EndsWith(".jpg"))
                            continue;
                    }*/

                    if (str.EndsWith("shadervariants"))
                        continue;

                    if (bName != str)
                        depList.Add(str);
                }
                depList.Sort(SortDepth);
                results.BundleDetailsList.Add(new BundleDetails()
                {
                    m_FileName = bName,
                    m_Dependencies = depList.ToArray(),
                });
            }
            return results;
        }

        static int SortDepth(string a, string b)
        {
            string endA = Path.GetExtension(a);
            string endB = Path.GetExtension(b);
            if (endA == endB)
                return a.CompareTo(b);
            else
            {
                int pointA = GetSortPoint(endA);
                int pointB = GetSortPoint(endB);
                if (pointA != pointB)
                    return pointA > pointB ? -1 : 1;
                else
                    return a.CompareTo(b);
            }
        }

        static int GetSortPoint(string ext)
        {
            int ret = 0;
            switch (ext)
            {
                case ".asset":
                    ret = 100;
                    break;
                case ".shadervariants":
                    ret = 99;
                    break;
                case ".png":
                    ret = 97;
                    break;
                case ".tga":
                    ret = 98;
                    break;
                case ".ttf":
                    ret = 95;
                    break;
                case ".mat":
                    ret = 90;
                    break;
                case ".fbx":
                    ret = 81;
                    break;
                case ".anim":
                    ret = 82;
                    break;
                case ".prefab":
                    ret = 80;
                    break;
            }
            return ret;
        }
    }
}