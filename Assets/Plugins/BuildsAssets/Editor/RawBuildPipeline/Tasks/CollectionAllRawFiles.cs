
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEditor.Build.Pipeline;
using UnityEditor.Build.Pipeline.Injector;
using UnityEditor.Build.Pipeline.Interfaces;

namespace BuildsAssets
{
    public class CollectionAllRawFiles  : IBuildTask
    {
        
        [InjectContext(ContextUsage.In)]
        IBundleBuildContent m_BuildContent;
        
        [InjectContext(ContextUsage.Out)]
        IContextObject m_Collection;
        
        public int Version { get => 1; }
        public ReturnCode Run()
        {
            CollectionContextObject collection = new CollectionContextObject();

            HashSet<(string,string)> allFiles = new();
            
            foreach (var keyValuePair in m_BuildContent.BundleLayout)
            {
                allFiles.Add(( keyValuePair.Key, AssetDatabase.GUIDToAssetPath(keyValuePair.Value[0]) ));
            }
            
            collection.files = allFiles.ToList();
            m_Collection = collection;
            return ReturnCode.Success;
        }
    }
}