using System.Collections.Generic;
using UnityEditor;
using UnityEditor.Build.Content;
using UnityEditor.Build.Pipeline;
using UnityEditor.Build.Pipeline.Interfaces;
using UnityEngine;
using UnityEngine.Build.Pipeline;

namespace BuildsAssets
{
    public class ResouceBuild
    {
        public struct ResBuildParams
        {
            
            /// <summary>
            /// 任务
            /// </summary>
            public IList<IBuildTask> tasks;
            
            /// <summary>
            /// 输出目录
            /// </summary>
            public string outputPath;
            
            /// <summary>
            /// 打包方式
            /// </summary>
            public BuildAssetBundleOptions options;
            
            /// <summary>
            /// 输出目标平台
            /// </summary>
            public BuildTarget targetPlatform;
            
            /// <summary>
            /// 需要打的AB
            /// </summary>
            public List<AssetBundleBuild> bundleBuilds;

            /// <summary>
            /// 构建的ID
            /// </summary>
            public string buildId;
        }
        
        public static CompatibilityAssetBundleManifest Run(ResBuildParams buildParams)
        {
            IBundleBuildParameters parameters = GetBundleBuildParameters(buildParams.buildId,buildParams.outputPath,buildParams.options,buildParams.targetPlatform);
            BundleBuildContent buildContent = new(buildParams.bundleBuilds); // 把ab 塞进去
            IBundleBuildResults results;
            ReturnCode returnCode = ContentPipeline.BuildAssetBundles(parameters, buildContent, out results,buildParams.tasks);
           
            if (returnCode != ReturnCode.Success)
            {
                Debug.LogError("打包失败" + returnCode);
            }
            else
            {
                CompatibilityAssetBundleManifest assetBundleManifest = ScriptableObject.CreateInstance<CompatibilityAssetBundleManifest>();
                assetBundleManifest.SetResults(results.BundleInfos);
                return assetBundleManifest;
            }

            return null;
        }

        static IBundleBuildParameters GetBundleBuildParameters(string buildId,string outputPath,BuildAssetBundleOptions options, BuildTarget targetPlatform)
        {
            var group = BuildPipeline.GetBuildTargetGroup(targetPlatform);
            var parameters = new BundleBuildParametersEx(buildId,targetPlatform, group, outputPath);
            if ((options & BuildAssetBundleOptions.ForceRebuildAssetBundle) != 0)
                parameters.UseCache = false;

            if ((options & BuildAssetBundleOptions.AppendHashToAssetBundleName) != 0)
                parameters.AppendHash = true;

#if UNITY_2018_3_OR_NEWER
            if ((options & BuildAssetBundleOptions.ChunkBasedCompression) != 0)
                parameters.BundleCompression = UnityEngine.BuildCompression.LZ4;
            else if ((options & BuildAssetBundleOptions.UncompressedAssetBundle) != 0)
                parameters.BundleCompression =  UnityEngine.BuildCompression.Uncompressed;
            else
                parameters.BundleCompression =  UnityEngine.BuildCompression.LZMA;
#else
            if ((options & BuildAssetBundleOptions.ChunkBasedCompression) != 0)
                parameters.BundleCompression = BuildCompression.DefaultLZ4;
            else if ((options & BuildAssetBundleOptions.UncompressedAssetBundle) != 0)
                parameters.BundleCompression = BuildCompression.DefaultUncompressed;
            else
                parameters.BundleCompression = BuildCompression.DefaultLZMA;
#endif

            if ((options & BuildAssetBundleOptions.DisableWriteTypeTree) != 0)
                parameters.ContentBuildFlags |= ContentBuildFlags.DisableWriteTypeTree;

            return parameters;
        }
    }
}