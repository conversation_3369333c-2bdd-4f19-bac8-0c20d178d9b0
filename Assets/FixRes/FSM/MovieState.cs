using System;
using System.Collections.Generic;
using Core.FSM;
using Game.Framework;
using GameMain;

namespace Game.FSM
{
    /// <summary>
    /// Logo影片和开场影片
    /// </summary>
    [GameState]
    public class MovieState : BaseState
    {
        public const int MaxMovieTime = 8000;
        public const string LogoMovieName = "Videos/cbg_sdk_video.mp4";
        private int totalTime = 0;
        private bool isCheckGo = false;
        public override void OnStateEnter()
        {
            totalTime = 0;
            isCheckGo = false;
            VideoPlayerMgr.Ins.PlayVideo(LogoMovieName, () =>
            {
                GameStateMgr.Instance.FirstLaunch();
            });
            UIUpdateController.Ins.ShowWindow();
        }

        public override void OnStateLeave()
        {
            isCheckGo = false;
        }
        public override void Tick(int deltaMs)
        {
            totalTime += deltaMs;
            if(totalTime >= MaxMovieTime)
            {
                if(isCheckGo == false)
                {
                    isCheckGo = true;
                    GameStateMgr.Instance.FirstLaunch();
                }
            }
        }
    }
}
