using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Core;
using Core.Collections;
using Core.FSM;
using Game.Download;
using Game.Framework;
using GameMain;

namespace Game.FSM
{
    /// <summary>
    /// 下载更新配置状态（主要是先下载分包配置文件）
    /// </summary>
    [GameState]
   public class ConfDownloadState : BaseState
    {
        // 需要下载的文件
        private SortedTable<string, ResVerionDb> _szNeedUpdateFileDict = null;
        //本地资源文件列表
        private Dictionary<string, ResVerionDb> _szLocalFileDict = null;
        //Web下载的文件列表
        private Dictionary<string, ResVerionDb> _szRemoteFileDict = null;
        //下载器
        private DownloaderResource _downloader = null;
        public override void OnStateEnter()
        {
            _InitResDownloadList();
            _hadGoto = false;
            UIUpdateController.Ins.ShowWindow();
            UIUpdateController.Ins.SetPercent(0f);
            //有资源更新就去下载
            if (_szNeedUpdateFileDict.Count > 0)
            {
                _downloader = new DownloaderResource(_szNeedUpdateFileDict, DownloadUtility.MaxThreadCount, DownloadType.Default, _SetEmeory, _SetStart, _SetProgress, _SetRecheck, _SetError, _SetFinished);
            }
            else
            {
                _GoResDownload();
            }
        }
        public override void OnStateLeave()
        {
            if (_downloader != null)
                _downloader.StopDownload();
            os.dispose(ref _downloader);
            _hadGoto = false;
        }
        public override void Tick(int deltaMs)
        {
            if(_downloader != null)
            {
                _downloader.Tick(deltaMs);
                if(_downloader.Done)
                {
                    _GoResDownload();
                }
            }
        }
        /// <summary>
        /// 初始化资源下载列表
        /// </summary>
        private void _InitResDownloadList()
        {
            _szLocalFileDict = PackageMgr.Ins.GetAllFullResVerionDb();
            //如果没有初始化web下载下来的文件列表
            if (!PackageMgr.Ins.IsRemoteDbInited())
            {
                PackageMgr.Ins.LoadRemoteResVerionDb();
            }
            _szRemoteFileDict = PackageMgr.Ins.GetAllRemoteResVerionDb();
            // 比较web下载下来的文件列表和本地的两个文件列表的差异数据
            _szNeedUpdateFileDict = new SortedTable<string, ResVerionDb>();
            string dataFolderRoot = PathTools.DataPathName + "/";

            //记录不需要下载的内容
            StringBuilder sb = new StringBuilder();
            foreach (var remoteItem in _szRemoteFileDict)
            {
                string updateFilePath = remoteItem.Key;
                uint crc = remoteItem.Value.crc;
                int updateFileSize = remoteItem.Value.TotalSize;
                int resType = remoteItem.Value.ResType;

                bool isUpdate = false;
                //1.不是配置文件的直接忽略
                if (!updateFilePath.Contains(dataFolderRoot))
                {
                    isUpdate = false;
                    //DLogger.Log("[" + updateFilePath + "] Not Datas File，Do Not Need Download！");
                    continue;
                }
                // 2.先找这个文件本地是否存在，不存在直接下载
                if (_szLocalFileDict.ContainsKey(updateFilePath) == false)
                {
                    isUpdate = true;
                    //DLogger.Log("[" + updateFilePath + "]Not Exists，Need Download！");
                }
                else
                {
                    // 3.如果存在，比较文件大小，一样大就比较MD5，不一样大就直接下载
                    if (!_szLocalFileDict.ContainsKey(updateFilePath))
                    {
                        isUpdate = true;
                        //DLogger.Log("[" + updateFilePath + "] Not Exists，Need Download！");
                    }
                    else
                    {
                        ResVerionDb resInfo = _szLocalFileDict[updateFilePath];
                        //文件大小和MD5检查
                        if (resInfo.TotalSize != updateFileSize)
                        {
                            isUpdate = true;
                            //DLogger.Log("[" + updateFilePath + "] FileSize，Need Download！");
                        }
                        else
                        {
                            if (!resInfo.crc.Equals(crc))
                            {
                                isUpdate = true;
                                //DLogger.Log("[" + updateFilePath + "] MD5，Need Download！");
                            }
                        }
                    }
                }

                if (isUpdate)
                {
                    //特殊文件不下载
                    if (updateFilePath.ToLower().Equals(PathTools.AppFileName))
                    {
                        isUpdate = false;
                        DLogger.Log("[" + updateFilePath + "] 特殊文件不需要下载！");
                    }
                }
                else
                {
                    sb.AppendLine(string.Format("{0},{1},{2},{3}", updateFilePath, crc, updateFileSize, resType));
                }
                DLogger.Log("path : {0}  need down : {1}", updateFilePath, isUpdate);

                // 如果需要更新
                if (isUpdate)
                {
                    _szNeedUpdateFileDict.Add(updateFilePath, new ResVerionDb(updateFilePath, crc, updateFileSize, resType));
                }
            }

            //比较完写file_list，把不需要下载的先记录下来
            string fileListPath = PathTools.GetFullPath(PathTools.UpdateFileListName, PathTools.AP2PIP.PersistentDataPath);
            if (File.Exists(fileListPath))
                File.Delete(fileListPath);

            //sb.AppendLine();//写下一行
            using (FileStream fs = new FileStream(fileListPath, FileMode.CreateNew))
            {
                using (StreamWriter sw = new StreamWriter(fs))
                {
                    sw.Write(sb.ToString());
                }
            }


            //清除比较资源
            _szLocalFileDict = null;
            _szRemoteFileDict = null;
        }
        /// <summary>
        /// 设置开始下载
        /// </summary>
        public override void OnStateConfirmed()
        {
            if (_downloader != null)
            {
                _downloader.SetStartDownload();
            }
        }
        /// <summary>
        /// 获取下载状态
        /// </summary>
        /// <returns></returns>
        public override int OnGetSubState()
        {
            if (_downloader != null)
            {
                return (int)_downloader.Phases;
            }
            return 0;
        }
        private void _SetEmeory()
        {
            if (_downloader == null)
            {
                return;
            }
            long totalSize = _downloader.TotalBytes;
            long downSize = _downloader.DownloadedBytes;
            if (downSize < totalSize)
            {
                //不提示
                _downloader.Phases = DownloadPhase.StartDownloadResource;
            }
            else
            {
                _SetFinished();
                _downloader.Phases = DownloadPhase.Done;
            }
        }
        private void _SetStart()
        {
            UIUpdateController.Ins.SetUpdateMsg(Localization.GetValue(LanguageCode.UpdatingConfig));
        }
        private float _progress = 0;
        private string _downloadProgress = "";
        private string _downloadSpeed = "";
        private void _SetProgress()
        {
            if (_downloader == null)
            {
                return;
            }
            long downSize = _downloader.DownloadedBytes;
            long totalSize = _downloader.TotalBytes;
            if(downSize > totalSize)
            {
                downSize = totalSize;
            }
            if (totalSize > 0)
            {
                _progress = downSize * 1f / totalSize;
            }
            if (_progress > 1f)
            {
                _progress = 1f;
            }
            long bytesReceived = _downloader.ReceivedBytes;
            double totalSeconds = _downloader.TotalSeconds();
            _downloadProgress = String.Format("{0:F}", downSize / 1024.0 / 1024.0) + "MB" + " / " + String.Format("{0:F}", totalSize / 1024.0 / 1024.0) + "MB";
            _downloadSpeed = string.Format("{0} kb/s", (bytesReceived / 1024d / totalSeconds).ToString("0.00"));
            UIUpdateController.Ins.SetUpdateProgressAndSpeed(_downloadProgress, _downloadSpeed, _progress);
        }
        /// <summary>
        /// 资源检查
        /// </summary>
        private void _SetRecheck()
        {
            UIUpdateController.Ins.SetUpdateMsg(Localization.GetValue(LanguageCode.CheckForUpdate));
        }
        /// <summary>
        /// 处理下载资源错误
        /// </summary>
        private void _SetError()
        {
            //提示错误
            UIUpdateController.Ins.ShowResDownloadError();
        }
        /// <summary>
        /// 下载完成
        /// </summary>
        private void _SetFinished()
        {
            //重新初始化分包列表
            PackageMgr.Ins.LoadSubpackageResList();
            UIUpdateController.Ins.SetUpdateProgressAndSpeedNull();
        }
        private bool _hadGoto = false;
        /// <summary>
        /// <summary>
        /// 开始下载lua等资源
        /// </summary>
        private void _GoResDownload()
        {
            if (_hadGoto == true)
            {
                return;
            }
            _hadGoto = true;
            GameStateMgr.Instance.ResDownload();
        }
    }
}
