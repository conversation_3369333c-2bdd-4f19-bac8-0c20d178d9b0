using NodeCanvas.Framework;
using ParadoxNotion.Design;
using UnityEngine;

namespace GameHotfixs.Battles
{
    [Category("✫ Utils")]
    [Description("Trans Position ByUnityCtrl .")]
    public class TransPositionByUnityCtrl : ActionTask
    {
        public BBParameter<BattleUnitCtrl> mTransUnitCtrl;

        public BBParameter<Vector3> mOffset;

        public BBParameter<bool> mUseForward;

        protected override string info {
            get { return $"Trans Position {mTransUnitCtrl},{mOffset},{mUseForward} "; }
        }

        protected override void OnExecute()
        {
            if (mUseForward.value)
            {
                Vector3 offset = mTransUnitCtrl.value.transform.TransformVector(mOffset.value);
                mTransUnitCtrl.value.SetPosition(mTransUnitCtrl.value.GetPosition() + offset);
            }
            else
            {
                mTransUnitCtrl.value.SetPosition(mTransUnitCtrl.value.GetPosition() + mOffset.value);
            }

            EndAction();
        }
    }
}
