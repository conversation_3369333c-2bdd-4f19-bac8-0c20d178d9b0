using Cysharp.Threading.Tasks;
using Game.Framework;
using GameHotfixs.Battles;
using NodeCanvas.Framework;
using NodeCanvas.StateMachines;
using ParadoxNotion.Design;
using UnityEngine;


namespace GameHotfixs.Battles
{
    /// <summary>
    /// 把Lua的对象绑定到行为树上
    /// </summary>

    public static class BattleUnitUtils
    {
        public static BattleUnitCtrl GetOrAddBattleUnitCtrl(GameObject shipEntity)
        {
            BattleUnitCtrl unit = shipEntity.GetComponent<BattleUnitCtrl>();
            if (unit == null)
                unit = shipEntity.AddComponent<BattleUnitCtrl>();
            return unit;
        }

        public static void SetBlockboardValues(GameObject go,string key,Vector3 value)
        {
            go.GetComponent<Blackboard>()?.SetVariableValue(key,value);
        }

        
    }

}
