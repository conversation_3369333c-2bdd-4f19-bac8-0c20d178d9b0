using DG.Tweening;
using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// 贝塞尔路径组件
/// 提供更高级的路径移动功能
/// </summary>
public class BezierPath : MonoBehaviour
{
    [Header("路径设置")]
    [SerializeField] private List<Transform> waypoints = new List<Transform>();
    [SerializeField] private bool loop = false;
    [SerializeField] private bool lookAtDirection = false;
    [SerializeField] private float lookAheadDistance = 0.1f;

    [Header("动画设置")]
    [SerializeField] private float duration = 5f;
    [SerializeField] private Ease easeType = Ease.InOutQuad;
    [SerializeField] private bool autoPlay = false;

    [Header("可视化")]
    [SerializeField] private bool showPath = true;
    [SerializeField] private Color pathColor = Color.yellow;
    [SerializeField] private int pathResolution = 100;

    private Tween currentTween;
    private Vector3[] pathPoints;

    void Start()
    {
        if (autoPlay)
        {
            PlayPath();
        }
    }

    /// <summary>
    /// 播放路径动画
    /// </summary>
    public void PlayPath()
    {
        if (waypoints.Count < 2)
        {
            Debug.LogWarning("路径至少需要2个点");
            return;
        }

        UpdatePathPoints();

        // 停止当前动画
        currentTween?.Kill();

        // 创建新的动画
        currentTween = DOTween.To(() => 0f, t =>
        {
            Vector3 position = CalculateBezierCurve(pathPoints, t);
            transform.position = position;

            // 如果需要朝向移动方向
            if (lookAtDirection && t < 1f)
            {
                Vector3 lookAtPos = CalculateBezierCurve(pathPoints, Mathf.Min(1f, t + lookAheadDistance));
                Vector3 direction = (lookAtPos - position).normalized;
                if (direction != Vector3.zero)
                {
                    transform.rotation = Quaternion.LookRotation(direction);
                }
            }

        }, 1f, duration)
        .SetEase(easeType)
        .SetLoops(loop ? -1 : 1, LoopType.Restart);
    }

    /// <summary>
    /// 停止路径动画
    /// </summary>
    public void StopPath()
    {
        currentTween?.Kill();
    }

    /// <summary>
    /// 暂停路径动画
    /// </summary>
    public void PausePath()
    {
        currentTween?.Pause();
    }

    /// <summary>
    /// 恢复路径动画
    /// </summary>
    public void ResumePath()
    {
        currentTween?.Play();
    }

    /// <summary>
    /// 设置路径进度
    /// </summary>
    public void SetProgress(float progress)
    {
        if (pathPoints == null || pathPoints.Length == 0)
            UpdatePathPoints();

        progress = Mathf.Clamp01(progress);
        Vector3 position = CalculateBezierCurve(pathPoints, progress);
        transform.position = position;
    }

    /// <summary>
    /// 更新路径点
    /// </summary>
    private void UpdatePathPoints()
    {
        pathPoints = new Vector3[waypoints.Count];
        for (int i = 0; i < waypoints.Count; i++)
        {
            pathPoints[i] = waypoints[i].position;
        }
    }

    /// <summary>
    /// 添加路径点
    /// </summary>
    public void AddWaypoint(Transform waypoint)
    {
        if (!waypoints.Contains(waypoint))
        {
            waypoints.Add(waypoint);
        }
    }

    /// <summary>
    /// 移除路径点
    /// </summary>
    public void RemoveWaypoint(Transform waypoint)
    {
        waypoints.Remove(waypoint);
    }

    /// <summary>
    /// 清空路径点
    /// </summary>
    public void ClearWaypoints()
    {
        waypoints.Clear();
    }

    /// <summary>
    /// 获取路径长度
    /// </summary>
    public float GetPathLength()
    {
        if (pathPoints == null || pathPoints.Length < 2)
            return 0f;

        return DOTweenBezierExtensions.GetBezierLength(pathPoints, pathResolution);
    }

    private void OnDrawGizmos()
    {
        if (!showPath || waypoints.Count < 2)
            return;

        UpdatePathPoints();

        // 绘制路径
        Gizmos.color = pathColor;
        Vector3 previousPoint = pathPoints[0];

        for (int i = 1; i <= pathResolution; i++)
        {
            float t = i / (float)pathResolution;
            Vector3 currentPoint = CalculateBezierCurve(pathPoints, t);
            Gizmos.DrawLine(previousPoint, currentPoint);
            previousPoint = currentPoint;
        }

        // 绘制路径点
        Gizmos.color = Color.red;
        for (int i = 0; i < pathPoints.Length; i++)
        {
            Gizmos.DrawSphere(pathPoints[i], 0.2f);

            // 绘制序号
#if UNITY_EDITOR
            UnityEditor.Handles.Label(pathPoints[i] + Vector3.up * 0.5f, i.ToString());
#endif
        }

        // 绘制连接线
        Gizmos.color = Color.gray;
        for (int i = 1; i < pathPoints.Length; i++)
        {
            Gizmos.DrawLine(pathPoints[i - 1], pathPoints[i]);
        }
    }

    /// <summary>
    /// 计算贝塞尔曲线上的点
    /// </summary>
    private Vector3 CalculateBezierCurve(Vector3[] points, float t)
    {
        if (points.Length == 1)
            return points[0];

        Vector3[] newPoints = new Vector3[points.Length - 1];
        for (int i = 0; i < newPoints.Length; i++)
        {
            newPoints[i] = Vector3.Lerp(points[i], points[i + 1], t);
        }

        return CalculateBezierCurve(newPoints, t);
    }

    // 公开属性
    public List<Transform> Waypoints => waypoints;
    public bool Loop { get => loop; set => loop = value; }
    public float Duration { get => duration; set => duration = value; }
    public Ease EaseType { get => easeType; set => easeType = value; }
    public bool LookAtDirection { get => lookAtDirection; set => lookAtDirection = value; }
}
