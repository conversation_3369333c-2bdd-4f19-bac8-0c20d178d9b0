using UnityEngine;

/// <summary>
/// 贝塞尔曲线可视化组件
/// 用于在Scene视图中预览贝塞尔曲线路径
/// </summary>
public class BezierCurveVisualizer : MonoBehaviour
{
    [Header("曲线设置")]
    [SerializeField] private Transform[] controlPoints;
    [SerializeField] private int resolution = 50;
    [SerializeField] private Color curveColor = Color.green;
    [SerializeField] private Color pointColor = Color.red;
    [SerializeField] private float pointSize = 0.1f;

    [Header("2D设置 (RectTransform)")]
    [SerializeField] private bool use2D = false;
    [SerializeField] private RectTransform[] controlPoints2D;

    private void OnDrawGizmos()
    {
        if (use2D)
        {
            DrawBezierCurve2D();
        }
        else
        {
            DrawBezierCurve3D();
        }
    }

    private void DrawBezierCurve3D()
    {
        if (controlPoints == null || controlPoints.Length < 2)
            return;

        // 获取控制点位置
        Vector3[] points = new Vector3[controlPoints.Length];
        for (int i = 0; i < controlPoints.Length; i++)
        {
            if (controlPoints[i] != null)
                points[i] = controlPoints[i].position;
        }

        // 绘制曲线
        Gizmos.color = curveColor;
        Vector3 previousPoint = points[0];

        for (int i = 1; i <= resolution; i++)
        {
            float t = i / (float)resolution;
            Vector3 currentPoint = CalculateBezierCurve(points, t);
            Gizmos.DrawLine(previousPoint, currentPoint);
            previousPoint = currentPoint;
        }

        // 绘制控制点
        Gizmos.color = pointColor;
        for (int i = 0; i < points.Length; i++)
        {
            Gizmos.DrawSphere(points[i], pointSize);

            // 绘制控制点连线
            if (i > 0)
            {
                Gizmos.color = Color.gray;
                Gizmos.DrawLine(points[i - 1], points[i]);
                Gizmos.color = pointColor;
            }
        }
    }

    private void DrawBezierCurve2D()
    {
        if (controlPoints2D == null || controlPoints2D.Length < 2)
            return;

        // 获取控制点位置
        Vector3[] points = new Vector3[controlPoints2D.Length];
        for (int i = 0; i < controlPoints2D.Length; i++)
        {
            if (controlPoints2D[i] != null)
                points[i] = controlPoints2D[i].position;
        }

        // 绘制曲线
        Gizmos.color = curveColor;
        Vector3 previousPoint = points[0];

        for (int i = 1; i <= resolution; i++)
        {
            float t = i / (float)resolution;
            Vector3 currentPoint = CalculateBezierCurve(points, t);
            Gizmos.DrawLine(previousPoint, currentPoint);
            previousPoint = currentPoint;
        }

        // 绘制控制点
        Gizmos.color = pointColor;
        for (int i = 0; i < points.Length; i++)
        {
            Gizmos.DrawSphere(points[i], pointSize);

            // 绘制控制点连线
            if (i > 0)
            {
                Gizmos.color = Color.gray;
                Gizmos.DrawLine(points[i - 1], points[i]);
                Gizmos.color = pointColor;
            }
        }
    }

    /// <summary>
    /// 计算任意阶贝塞尔曲线上的点（De Casteljau算法）
    /// </summary>
    private Vector3 CalculateBezierCurve(Vector3[] points, float t)
    {
        if (points.Length == 1)
            return points[0];

        Vector3[] newPoints = new Vector3[points.Length - 1];
        for (int i = 0; i < newPoints.Length; i++)
        {
            newPoints[i] = Vector3.Lerp(points[i], points[i + 1], t);
        }

        return CalculateBezierCurve(newPoints, t);
    }

    /// <summary>
    /// 设置3D控制点
    /// </summary>
    public void SetControlPoints3D(Transform[] points)
    {
        controlPoints = points;
        use2D = false;
    }

    /// <summary>
    /// 设置2D控制点
    /// </summary>
    public void SetControlPoints2D(RectTransform[] points)
    {
        controlPoints2D = points;
        use2D = true;
    }

    /// <summary>
    /// 获取曲线上指定t值的位置
    /// </summary>
    public Vector3 GetPositionOnCurve(float t)
    {
        if (use2D && controlPoints2D != null && controlPoints2D.Length >= 2)
        {
            Vector3[] points = new Vector3[controlPoints2D.Length];
            for (int i = 0; i < controlPoints2D.Length; i++)
            {
                if (controlPoints2D[i] != null)
                    points[i] = controlPoints2D[i].position;
            }
            return CalculateBezierCurve(points, t);
        }
        else if (!use2D && controlPoints != null && controlPoints.Length >= 2)
        {
            Vector3[] points = new Vector3[controlPoints.Length];
            for (int i = 0; i < controlPoints.Length; i++)
            {
                if (controlPoints[i] != null)
                    points[i] = controlPoints[i].position;
            }
            return CalculateBezierCurve(points, t);
        }

        return Vector3.zero;
    }
}
