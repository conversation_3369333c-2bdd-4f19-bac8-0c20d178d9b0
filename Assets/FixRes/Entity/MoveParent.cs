using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;

namespace Youda.Move
{
    public class MoveParent:IDisposable
    {
        public Transform transform;
        
        /// <summary>
        /// 移动目标点
        /// </summary>
        public Vector3 targetPos;

        /// <summary>
        /// 将要延迟
        /// </summary>
        protected float delay;

        /// <summary>
        /// 移动速度
        /// </summary>
        protected float time;

        /// <summary>
        /// 播放速度
        /// </summary>
        protected float playSpeed = 1f;

        /// <summary>
        /// 是否正在移动
        /// </summary>
        protected bool isMoving;

        /// <summary>
        /// 移动开始
        /// </summary>
        public Action OnMoveBegin;

        /// <summary>
        /// 移动结束
        /// </summary>
        public Action OnMoveEnded;

        public MoveParent(Transform owner)
        {
            this.transform = owner;
        }
        /// <summary>
        /// 销毁
        /// </summary>
        public void Dispose()
        {
            transform = null;
            targetPos = Vector3.zero;
            OnMoveBegin = null;
            OnMoveEnded = null;
         }

        public void SetSpeed(float speed)
        {
            //moveSpeed = Vector3.Distance(transform.position, targetPos) / speed * 0.9f;

            this.playSpeed = speed;

            /*iTween tween = transform.GetComponent<iTween>();
            if(tween != null) {
                tween.time = this.Time; tween.delay = this.Delay;
            }*/
        }

        /// <summary>
        /// 获取加速后的延迟时间
        /// </summary>
        public float Delay { get { return this.delay / playSpeed; } }

        /// <summary>
        /// 获取加速后的使用时间
        /// </summary>
        public float Time { get { return this.time / playSpeed; } }

        /// <summary>
        /// 停止移动
        /// </summary>
        public void StopMove()
        {
            //iTween.Stop(transform.gameObject);
        }

        /// <summary>
        /// 移动到
        /// </summary>
        /// <param name="targetPos"></param>
        /// <param name="speed"></param>
        public virtual void MoveTo(Vector3 targetPos, float time, float delay = 0)
        {
            this.targetPos = targetPos;
            this.delay = delay;
            this.time = time;

            Hashtable args = new Hashtable();

            //设置类型为线性，线性效果会好一些。
            //args.Add("easeType", iTween.EaseType.linear);
            //设置寻路的速度
            args.Add("time", this.Time);
            args.Add("delay", this.Delay);
            //是否先从原始位置走到路径中第一个点的位置
            args.Add("movetopath", true);
            //是否让模型始终面朝当面目标的方向，拐弯的地方会自动旋转模型
            //如果你发现你的模型在寻路的时候始终都是一个方向那么一定要打开这个
            args.Add("orienttopath", true);
            args.Add("axis", "y");
            args.Add("position", targetPos);

            System.Action<object[]> complete = OnMoveCompleteCall;
            args.Add("oncomplete", complete);
            //args.Add("oncompletetarget", OnMoveEnded);

            //iTween.MoveTo(transform.gameObject, args);
        }
        /// <summary>
        /// MoveTo扩展
        /// </summary>
        public virtual void MoveToEx(float targetX, float targetY, float targetZ, float time, float delay = 0)
        {
            Vector3 _targetPos = new Vector3(targetX, targetY, targetZ);
            MoveTo(_targetPos, time, delay);
        }
        /// <summary>
        /// 移动路径
        /// </summary>
        /// <param name="path"></param>
        /// <param name="time"></param>
        public virtual void MovePath(Transform parent, float time, float delay = 0)
        {
            List<Vector3> movePath = new List<Vector3>();
            for(int i = 0; i < parent.childCount; i++)
            {
                var child = parent.GetChild(i);
                movePath.Add(child.position);
            }

            this.MovePath(movePath.ToArray(), time, delay);
        }

        /// <summary>
        /// 移动
        /// </summary>
        /// <param name="posList"></param>
        /// <param name="time"></param>
        /// <param name="delay"></param>
        public virtual void MovePath(Vector3[] posList, float time, float delay = 0)
        {
            this.delay = delay; this.time = time;

            Hashtable args = new Hashtable();

            //设置类型为线性，线性效果会好一些。
            //args.Add("easeType", iTween.EaseType.linear);
            //设置寻路的速度
            args.Add("time", this.Time);
            args.Add("delay", this.Delay);
            //是否先从原始位置走到路径中第一个点的位置
            args.Add("movetopath", true);
            //是否让模型始终面朝当面目标的方向，拐弯的地方会自动旋转模型
            //如果你发现你的模型在寻路的时候始终都是一个方向那么一定要打开这个
            args.Add("orienttopath", true);
            args.Add("axis", "y");
            args.Add("path", posList);

            //System.Action<object[]> movestep = OnMoveStepCallback;
            //args.Add("onupdate", movestep);

            System.Action<object[]> complete = OnMoveCompleteCall;
            args.Add("oncomplete", complete);
            //args.Add("oncompletetarget", OnMoveEnded);

            //iTween.MoveTo(transform.gameObject, args);
        }

        public void OnMoveStepCallback(params object[] args)
        {

        }

        public void OnMoveCompleteCall(params object[] args)
        {
            if (this.OnMoveEnded != null) this.OnMoveEnded();
        }
    }
}
