using System;
using Sirenix.OdinInspector;
using UnityEngine;

namespace GameHotfix.Monos
{
    /// <summary>
    /// 将当前 RectTransform A Size 适应指定的 RectTransform B Size
    /// 提供给 不在同一个父节点下,但又需要适应某个RectTransform大小的UI使用
    /// 即将 RectTransform B 的Size 赋值到 RectTransform A Size上
    /// </summary>
    [ExecuteAlways]
    public class UIBindRectTransformSize : MonoBehaviour
    {
        public RectTransform bindTrans;

        private RectTransform rectTrans;

        [NonSerialized,ShowInInspector]
        public Vector2 DebugSize;
        private void Awake()
        {
            rectTrans = GetComponent<RectTransform>();
        }

        private void Update()
        {
            if (bindTrans == null)
                return;


            Vector2 size = rectTrans.sizeDelta;
            size = bindTrans.rect.size;
            rectTrans.sizeDelta = size;
            DebugSize = size;
        }
    }
}