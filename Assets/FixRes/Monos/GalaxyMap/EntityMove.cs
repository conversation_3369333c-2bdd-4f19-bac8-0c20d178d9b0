using System;
using Sirenix.OdinInspector;
using UnityEngine;

namespace GameHotfix.GalaxyMap
{
    public class EntityMove : MonoBehaviour
    {
        [ShowInInspector] [NonSerialized] public bool isMoving = false;
        
        [ShowInInspector] [NonSerialized] public float startMoveTimer = 0f;
        [ShowInInspector] [NonSerialized] public float moveTotalTimer = 0f;

        [ShowInInspector]
        [NonSerialized]
        public Vector3 startPoint;
        
        [ShowInInspector]
        [NonSerialized]
        public Vector3 endPoint;
        
        public void SetMoveTarget(
            float sx,float sy,float sz,
            float tx,float ty,float tz,
            float moveTimer,float offsetTimer
            )
        {

            isMoving = true;
            startMoveTimer = Time.realtimeSinceStartup - offsetTimer;
            moveTotalTimer = moveTimer;
            
            startPoint.x = sx;
            startPoint.y = sy;
            startPoint.z = sz;

            endPoint.x = tx;
            endPoint.y = ty;
            endPoint.z = tz;
            this.transform.forward = (endPoint - startPoint).normalized;
        }

        public void Update()
        {
            if (!isMoving)
                return;
            
            float process = (Time.realtimeSinceStartup - startMoveTimer) / moveTotalTimer;
            process = Mathf.Clamp01(process);
            this.transform.position = Vector3.Lerp(startPoint,endPoint,process);
     
            if (process >= 1)
            {
                isMoving = false;
            }
        }
        
    }
}