using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UIFramework
{
    public class UIEvent
    {
		public const int None = 0;
		public const int Click = 1;          //Button单击
		public const int Press = 2;          //Button按下
		public const int PressUp = 3;        //But<PERSON>松开
		public const int OnDrag = 4;         //滑动
        public const int OnAnimation = 5;    //动画
        public const int OnDragDir = 6;      //检测滑动方向
        public const int OnBeginDrag = 7;         //开始
        public const int OnEndDrag = 8;         //结束滑动
        public const int OnInDrag = 9;         //滑动中
        public const int OnTriggerEnter2D = 10;         
        public const int OnTriggerStay2D = 11;         
        public const int OnTriggerExit2D = 12;
        public const int OnCollisionEnter2D = 13;
        public const int OnCollisionStay2D = 14;
        public const int OnCollisionExit2D = 15;

        public const int PushEvent = 1000;   //UI通用事件，不想定义新事件可用

        #region 回调事件
        /// <summary>
        /// 点击回调
        /// </summary>
        /// <param name="go">按钮GameObject</param>
        /// <param name="clickEvent">点击回调</param>
        /// <param name="objects">回调参数</param>
        public static void OnClickCallBack(Delegate clickEvent, object[] objects)
        {
            if (clickEvent == null) return;

            int objCount = objects != null ? objects.Length : 0;
            switch (objCount)
            {
                case 0:
                    clickEvent.DynamicInvoke();
                    break;
                case 1:
                    clickEvent.DynamicInvoke(objects[0]);
                    break;
                case 2:
                    clickEvent.DynamicInvoke(objects[0], objects[1]);
                    break;
                case 3:
                    clickEvent.DynamicInvoke(objects[0], objects[1], objects[2]);
                    break;
                case 4:
                    clickEvent.DynamicInvoke(objects[0], objects[1], objects[2], objects[3]);
                    break;
                case 5:
                    clickEvent.DynamicInvoke(objects[0], objects[1], objects[2], objects[3], objects[4]);
                    break;
                default:
                    Debug.LogError("OnClickCallBack param limited");
                    break;
            }
        }

        /// <summary>
        /// 长按回调
        /// </summary>
        /// <param name="go">按钮GameObject</param>
        /// <param name="pressEvent">长按回调</param>
        /// <param name="objects">回调参数</param>
        public static void OnPressCallBack(bool isPressing, Delegate pressEvent, object[] objects)
        {
            if (pressEvent == null) return;

            int objCount = objects != null ? objects.Length : 0;
            switch (objCount)
            {
                case 0:
                    pressEvent.DynamicInvoke(isPressing);
                    break;
                case 1:
                    pressEvent.DynamicInvoke(isPressing, objects[0]);
                    break;
                case 2:
                    pressEvent.DynamicInvoke(isPressing, objects[0], objects[1]);
                    break;
                case 3:
                    pressEvent.DynamicInvoke(isPressing, objects[0], objects[1], objects[2]);
                    break;
                case 4:
                    pressEvent.DynamicInvoke(isPressing, objects[0], objects[1], objects[2], objects[3]);
                    break;
                case 5:
                    pressEvent.DynamicInvoke(isPressing, objects[0], objects[1], objects[2], objects[3], objects[4]);
                    break;
                default:
                    Debug.LogError("OnPressCallBack param limited");
                    break;
            }
        }

        /// <summary>
        /// 拖拽回调
        /// </summary>
        /// <param name="go">按钮GameObject</param>
        /// <param name="dragEvent">拖拽回调</param>
        /// <param name="objects">回调参数</param>
        public static void OnDragCallBack(Delegate dragEvent, int uiEventType, PointerEventData eventData, object[] objects)
        {
            if (dragEvent == null) return;

            int objCount = objects != null ? objects.Length : 0;
            switch (objCount)
            {
                case 0:
                    dragEvent.DynamicInvoke(uiEventType, eventData);
                    break;
                case 1:
                    dragEvent.DynamicInvoke(uiEventType, eventData, objects[0]);
                    break;
                case 2:
                    dragEvent.DynamicInvoke(uiEventType, eventData, objects[0], objects[1]);
                    break;
                case 3:
                    dragEvent.DynamicInvoke(uiEventType, eventData, objects[0], objects[1], objects[2]);
                    break;
                case 4:
                    dragEvent.DynamicInvoke(uiEventType, eventData, objects[0], objects[1], objects[2], objects[3]);
                    break;
                case 5:
                    dragEvent.DynamicInvoke(uiEventType, eventData, objects[0], objects[1], objects[2], objects[3], objects[4]);
                    break;
                default:
                    Debug.LogError("OnDragCallBack param limited");
                    break;
            }
        }
        #endregion
    }
}
