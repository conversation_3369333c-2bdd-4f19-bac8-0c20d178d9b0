using Core;
using Cysharp.Threading.Tasks;
using Game.Framework;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using UnityEngine;

public partial class Cfg
{
    private static bool _isIniting;
    static bool isInited = false;
    const int loadPerFrame = 15; //每帧读表数量
    static bool isLoadJsonConfig = false;
    public const string IS_LOAD_JSON = "IS_LOAD_JSON";
    public const string IS_OPEN_GUIDE = "IS_OPEN_GUIDE";

    #region 新版配置相关
    public static string DATA_FILE_NAME = "Configs/data_cfg_cab";
    public static string DATA_PATH => string.Format("{0}/AssetsPatch/{1}", Application.dataPath, DATA_FILE_NAME);

    public static async UniTask InitConfigDataAsync()
    {
        _isIniting = true;
        isInited = false;

        TextAsset[] ta = null;
        if (Constants.ResourceMode == (int)Constants.DevelopFlag.Resource)
        {
#if UNITY_EDITOR
            isLoadJsonConfig = UnityEditor.EditorPrefs.GetBool(IS_LOAD_JSON, true);
            System.IO.DirectoryInfo info = new DirectoryInfo(string.Format("{0}/AssetsPatch/{1}", Application.dataPath, DATA_FILE_NAME));
            FileInfo[] files = info.GetFiles();
            int index = 0;
            List<UniTask<TextAsset>> loadTasks = new List<UniTask<TextAsset>>(files.Length);
            foreach (FileInfo file in files)
            {
                if (file.Name.EndsWith(".meta") || file.Name.EndsWith(".json"))
                    continue;
                var task = GameResMgr.Ins.LoadAssetAsync<TextAsset>(string.Format("{0}/{1}", DATA_FILE_NAME, file.Name));
                loadTasks.Add(task);
            }
            ta = await UniTask.WhenAll(loadTasks);
#endif
        }
        else
        {
            ta = await GameResMgr.Ins.LoadAllAssetAsync<TextAsset>(string.Format("{0}/data_cfg_cab", DATA_FILE_NAME));
        }
        int loadCount = 0;
        //分帧加载所有表
        for (int i = 0; i < ta.Length; i++)
        {
            if (ta[i] != null)
            {
#if UNITY_EDITOR
                DLogger.Log("Load TextAsset {0}", ta[i].name);
#endif
                CfgInfo info = new CfgInfo(ta[i]);
                InitCfg(ta[i].name, info);
            }

            loadCount++;

            if (loadCount > loadPerFrame)
            {
                loadCount = 0;
                await UniTask.Yield();
            }
        }

        isInited = true;
        _isIniting = false;
        GameResMgr.Ins.UnloadAsset(DATA_FILE_NAME);    //用完卸载
    }

    static T LoadFromStream<T, U, V>(string fileName, CfgInfo info) where T : ConfigData<U, V>, new() where V : cfg_base<U>, new()
    {
        T ret = new T();
        //Debug.LogFormat("LoadFromStream ** {0}", fileName);
        Dictionary<U, V> dataDic;
        if (isLoadJsonConfig)
        {
            dataDic = GetCfgAsDictionaryByJson<U, V>(fileName);
        }
        else
        {
            dataDic = GetCfgAsDictionary<U, V>(info);
        }

        ret.Init(dataDic);
        info.OnLoaded();
        return ret;
    }

    static Dictionary<TKey, TValue> GetCfgAsDictionaryByJson<TKey, TValue>(string fileName)
        where TValue : cfg_base<TKey>, new()
    {
        string jsonPath = Path.Combine(DATA_PATH, fileName + ".json");
        string jsonStr = File.ReadAllText(jsonPath);
        TValue[] data = JsonConvert.DeserializeObject<TValue[]>(jsonStr);
        Dictionary<TKey, TValue> dir = new Dictionary<TKey, TValue>(data.Length);
        try
        {
            for (int i = 0; i < data.Length; i++)
            {
                TKey key = data[i].GetKeyProperty();
                dir.Add(key, data[i]);
            }
        }
        catch (Exception e)
        {
#if UNITY_EDITOR
            Debug.LogError("Cfg " + jsonPath + " load error!");
#endif
            Debug.LogException(e);
        }

        return dir;
    }

    public static Dictionary<TKey, TValue> GetCfgAsDictionary<TKey, TValue>(CfgInfo info) where TValue : cfg_base<TKey>, new()
    {
        //先读取总长度，一次性申请内存块，避免产生小块内存（不再先做成列表再做字典，减少总内存申请）
        int count = info.ReadInt32();
        //DLogger.Log("GetCfgAsDictionary {0}", count);
        var t = typeof(TValue);
        Dictionary<TKey, TValue> dir = new Dictionary<TKey, TValue>(count);
        TKey key = default;
        try
        {
            for (int i = 0; i < count; i++)
            {
                var temp = (TValue)FormatterServices.GetUninitializedObject(t);
                temp.ReadData(info);
                key = temp.GetKeyProperty();
                dir.Add(key, temp);
            }
        }
        catch (Exception exception)
        {
            Debug.LogError(string.Format("repeated key or changed key pos: {0} {1} {2}", typeof(TValue).Name, key, exception));
        }
        return dir;
    }
    #endregion

    public static bool IsLoadFinished()
    {
        return isInited;
    }

    public static void Clear()
    {

    }
}
