using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Core.FSM;
using GameMain;
using NodeCanvas.Framework;
using UnityEngine;

namespace Game.CityAI
{
    public class ImmigrantWorkState : ImmigrantBaseState
    {
        public ImmigrantWorkState(ImmigrantFSM fsm, string stateName) { 
            this.fsm = fsm;
            this.stateName = stateName;
        }

        
        
        public override void OnStateEnter()
        {
            var entity = fsm.entity;
            fsm.targetBuildingId = entity.WorkingBuildingId;

            var btOwner = entity.btOwner;
            btOwner.graph = ImmigrantMgr.Ins.FSMCtrl.WorkBT;
            var blackboard = btOwner.graph.blackboard;
            blackboard.SetVariableValue("workPlace", entity.WorkPlace);
            blackboard.SetVariableValue("immigrantId", entity.id);
            blackboard.SetVariableValue("entry", entity.WorkPlaceEntry);
            btOwner.StartBehaviour();
            Debug.Log($"immigrant{entity.id} is On state{stateName}");
        }



        public override void OnStateLeave()
        {
            var entity = fsm.entity;
            fsm.leaveBuildingId = entity.WorkingBuildingId;
            entity.animator.Play("Idle");
            entity.btOwner?.StopBehaviour();
        }

        public override void Tick(int deltaMs)
        {
            // 工作状态的更新逻辑
            // 例如：工作进度更新，体力消耗，检查工作是否完成
        }
    }
}
