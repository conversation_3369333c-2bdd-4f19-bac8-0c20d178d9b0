using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Core;
using Game.City;
using GameMain;
using NodeCanvas.BehaviourTrees;
using NodeCanvas.Framework;
using UnityEngine;
using UnityEngine.AI;

namespace Game.CityAI
{

    // 搬运机器人实体
    public class TransportRobotEntity
    {
        public GameObject go;
        public BehaviourTreeOwner btOwner;
        public IBlackboard blackboard;
        public int id; // buildingId
        //private 

        public TransportRobotEntity(int buildingId, GameObject go, Transform buildingTransform)
        {
            this.go = go;
            UtilTool.SetLayerRecursively(go, 16);


            id = buildingId;
            btOwner = go.GetComponent<BehaviourTreeOwner>();
            blackboard = btOwner?.graph.blackboard;
            blackboard.SetVariableValue("resourceBuilding", buildingTransform);
            blackboard.SetVariableValue("buildingId", buildingId);
            InitPos();
        }

        private void InitPos()
        {
            var starShipEntity = CityMgr.Ins.GetBuildingEntity(BuildingId.StarShip);
            if (starShipEntity != null)
            {
                var starShipPos = starShipEntity.buildingObj.transform.position;
                go.transform.position = starShipPos;
                var navAgent = go.GetComponent<NavMeshAgent>();
                navAgent.Warp(starShipPos);
            }
            
        }

        public void Destroy()
        {
            go.DestroyEx();
        }
        
    }
}
