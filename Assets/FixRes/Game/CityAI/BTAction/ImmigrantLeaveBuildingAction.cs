using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GameMain;
using NodeCanvas.Framework;
using ParadoxNotion.Design;
using UnityEngine;
using UnityEngine.AI;

namespace Game.CityAI
{
    [Category("Custom/Immigrant")]
    [Description("Immigrant Leave current inBuilding ")]
    public class ImmigrantLeaveBuildingAction : ActionTask<NavMeshAgent>
    {
        public BBParameter<int> immigrantId;
        [Name("Movement Speed")]
        [Description("Speed of movement during navigation.")]
        public BBParameter<float> movementSpeed = 1f;

        [Name("Arrival Distance")]
        [Description("Distance threshold to consider a waypoint reached.")]
        public BBParameter<float> keepDistance = 0.5f;

        private ImmigrantFSM immigrantFSM;
        private Vector3 targetPosition;
        private Vector3? lastRequest;

        protected override void OnExecute()
        {
            // Validate agent
            if (agent == null)
            {
                EndAction(false);
                return;
            }

            immigrantFSM = ImmigrantMgr.Ins.FSMCtrl.GetImmigrantFSM(immigrantId.value);
            if (immigrantFSM == null ) { 
                Debug.LogError($"ImmigrantLeaveBuildingAction: Could not find ImmigrantFSM for ID {immigrantId.value}");
                EndAction(false);
                return;
            }
            var isinBuilding = immigrantFSM.isInBuilding;
            if (!isinBuilding)
            {
                EndAction(true);
                return;
            }
            if(immigrantFSM.inBuildingId == immigrantFSM.targetBuildingId)
            {
                EndAction(true);
                return;
            }
            targetPosition = CityWayPointMgr.Ins.GetBuilidngEntry(immigrantFSM.inBuildingId);
            agent.speed = movementSpeed.value;
            if (Vector3.Distance(agent.transform.position, targetPosition) < agent.stoppingDistance + keepDistance.value)
            {
                EndAction(true);
                return;
            }
        }

        protected override void OnUpdate()
        {
            if (lastRequest != targetPosition)
            {
                if (!agent.SetDestination(targetPosition))
                {
                    EndAction(false);
                    return;
                }
            }

            lastRequest = targetPosition;

            if (!agent.pathPending && agent.remainingDistance <= agent.stoppingDistance + keepDistance.value)
            {
                EndAction(true);
            }
        }

        protected override void OnPause() { OnStop(); }
        protected override void OnStop()
        {
            if (lastRequest != null && agent.gameObject.activeSelf && agent.enabled && agent.isOnNavMesh)
            {
                agent.ResetPath();
            }
            lastRequest = null;
        }

    }
}
