using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GameBattle
{
    /// <summary>
    /// ����ʹ�õļ�����Ϣ
    /// </summary>
    public class SkillActInfo
    {
        public int skillId;             //����id
        public int skillLv;             //���ܵȼ�
        Dictionary<int, float> attrChg; //���Ա仯

        BattleObject atker;             //����ʹ����

        public SkillActInfo(BattleObject atker, int skillId, int skillLv)
        {
            this.atker = atker;
            this.skillId = skillId;
            this.skillLv = skillLv;
        }

        /// <summary>
        /// ��������ֵ
        /// </summary>
        /// <param name="attrId"></param>
        /// <param name="val"></param>
        public void UpdateAttrVal(int attrId, float val)
        {
            if (attrChg.ContainsKey(attrId))
                attrChg[attrId] += val;
            else
            {
                attrChg.Add(attrId, val);
            }
        }


        public void OnHitTarget(BattleObject target)
        {
            if (target == null) return;

            var skill_arg_cfg = Cfg.SkillArg.GetDataByIdLv(skillId, skillLv);
            if (skill_arg_cfg != null && skill_arg_cfg.Count > 0)
            {
                for (int i = 0; i < skill_arg_cfg.Count; i++)
                {

                }
            }
        }


    }
}