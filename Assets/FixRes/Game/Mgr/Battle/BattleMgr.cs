using Game.Network;
using GameMain;
using System;
using System.Collections.Generic;
using UnityEngine;
using Google.Protobuf;
using Object = UnityEngine.Object;
using IMessage = Google.Protobuf.IMessage;
using Packet;
using System.Linq;
using HeroInfo = GameMain.HeroInfo;
using UnityEngine.Pool;
using Core;

namespace GameBattle
{
    public class BattleMgr : LazySingleton<BattleMgr>, ISubMgr
    {
        public const int MAX_HERO_NUM = 5;

        public int CurStageId => mCurStageId;
        public BattleType CurBattleType => mCurBattleType;
        public object[] BattleParam => mBattleParam;
        int mCurStageId = 0;
        int mCurChapterId = 1;
        private BattleType mCurBattleType;
        BattlePVECtrl mPVECtrl = null;
        System.Random mRandom = new System.Random();
        BattleProcessFSM _processStateMachine;
        object[] mBattleParam;

        public void OnEnterGame()
        {
            Init();
        }

        public void RegisterNetProto()
        {
            BattleNetwork.Ins.RspStartBattleCallBack += OnRspStartBattle;
            BattleNetwork.Ins.RspBattleWinCallBack += OnRspBattleWin;
            BattleNetwork.Ins.RspBattleFailCallBack += OnRspBattleFail;
        }

        public void UnregisterNetProto()
        {
            BattleNetwork.Ins.RspStartBattleCallBack -= OnRspStartBattle;
            BattleNetwork.Ins.RspBattleWinCallBack -= OnRspBattleWin;
            BattleNetwork.Ins.RspBattleFailCallBack -= OnRspBattleFail;
        }

        public void SendReqStartBattle(List<int> heroIds)
        {
            BattleNetwork.Ins.ReqStartBattle(mCurStageId, heroIds);
        }

        void OnRspStartBattle(IMessage imsg)
        {
            EventDispatcher.BroadcastEvent(BattleEvent.OnRspStartBattle);
        }

        public void SendReqBattleWin()
        {
            BattleNetwork.Ins.ReqBattleWin(mCurStageId);
        }

        void OnRspBattleWin(IMessage imsg)
        {
            var rsp = imsg as RspBattleWin;
            List<ItemInfo> rewardItemInfos = rsp.RewardItemInfos.ToList();
            EventDispatcher.BroadcastEvent(BattleEvent.OnRspBattleWin, rewardItemInfos);

            if (Cfg.LevelsInfo.CheckIsPve(rsp.Id))
            {
                if (BattleChapterMgr.Ins.CurLevelId == 0)
                {
                    BattleNetwork.Ins.ReqAFKLevelRewardInfo();
                }
                BattleChapterMgr.Ins.CurLevelId = rsp.Id;

            }
        }

        public void SendReqBattleFail()
        {
            BattleNetwork.Ins.ReqBattleFail(mCurStageId);
        }

        void OnRspBattleFail(IMessage imsg)
        {
            EventDispatcher.BroadcastEvent(BattleEvent.OnRspBattleFail);
        }

        public void Init()
        {
            SkillDisplayMgr.Ins.Init();
            InitStateMechine();
            UserMgr.Ins.OnOneSystemRecvData(ToString());
        }

        void InitStateMechine()
        {
            _processStateMachine = new BattleProcessFSM();

            _processStateMachine.RegisterState(BattleState.BEFORE_ENTER_SCENE, new BattleBeforeEnterSceneState(_processStateMachine));
            _processStateMachine.RegisterState(BattleState.ENTER_SCENE, new BattleEnterSceneState(_processStateMachine));
            _processStateMachine.RegisterState(BattleState.AFTER_ENETR_SCENE, new BattleAfterEnterSceneState(_processStateMachine));
            _processStateMachine.RegisterState(BattleState.SELECT_HERO, new BattleSelectHeroState(_processStateMachine));
            _processStateMachine.RegisterState(BattleState.COUNT_DOWN, new BattleCountDownState(_processStateMachine));
            _processStateMachine.RegisterState(BattleState.IN_COMBAT, new BattleInCombatState(_processStateMachine));
            _processStateMachine.RegisterState(BattleState.END, new BattleEndState(_processStateMachine));
            _processStateMachine.RegisterState(BattleState.INTERRUPT, new BattleInterruptState(_processStateMachine));
            _processStateMachine.RegisterState(BattleState.EXIT, new BattleExitState(_processStateMachine));
        }

        public void EnterBattle(int levelId, BattleType battleType = BattleType.Normal, params object[] args)
        {
            if (mCurStageId != 0)
            {
                DLogger.Warning($"当前已进入关卡{mCurStageId}");
                return;
            }
            mCurStageId = levelId;
            mCurBattleType = battleType;
            mBattleParam = args;
            _processStateMachine.ChangeState(BattleState.BEFORE_ENTER_SCENE);
        }

        public void ChangeState(string stateName)
        {
            _processStateMachine.ChangeState(stateName);
        }

        public void CreatePVEBattle()
        {
            if (!mPVECtrl)
            {
                GameObject go = new GameObject("PVEBattle");
                mPVECtrl = go.AddComponent<BattlePVECtrl>();
                mPVECtrl.SetStateMachine(_processStateMachine);
            }

            mPVECtrl.Init(mCurStageId);

            //mPVECtrl.BattleStart();
        }

        public LevelsInfo GetCurLevelInfo() => Cfg.LevelsInfo.GetData(mCurStageId);

        public void InitPVEBattle()
        {
            mPVECtrl.InitNPCGroup();
        }

        public bool IsCrit(float crit)
        {
            int num = Mathf.CeilToInt(crit * 100);
            int val = mRandom.Next(1, 100);
            return num >= val;
        }

        public BattlePVECtrl GetPVECtrl()
        {
            return mPVECtrl;
        }

        public void NextStageLevel()
        {
        }

        public void OnPVEBattleFinish()
        {
            if (mPVECtrl != null)
            {
                //mPVECtrl.BattleEnd();
                mPVECtrl.ExitBattle();
            }
        }

        public void OnExit()
        {
            BattleEffectMgr.Ins.OnExit();
            OnPVEBattleFinish();
            if (mPVECtrl)
            {
                Object.Destroy(mPVECtrl.gameObject);
                mPVECtrl = null;
            }

            BulletFactory.Ins.OnDestroy();
            mCurStageId = 0;
        }

        public void SaveCurLineup(SortedDictionary<int, HeroInfo> lineUp)
        {
            ulong userId = UserMgr.Ins.Self.role_id;
            for (int i = 1; i <= MAX_HERO_NUM; i++)
            {
                HeroInfo heroInfo = null;
                string key = UserMgr.Ins.GetUserMarkKey($"BattlePos_{userId}_{i}");
                if (lineUp.TryGetValue(i, out heroInfo))
                {
                    PlayerPrefs.SetInt(key, heroInfo.id);
                }
                else
                {
                    PlayerPrefs.SetInt(key, 0);
                }
            }
        }

        public Dictionary<int, int> LoadCurLineup()
        {
            ulong userId = UserMgr.Ins.Self.role_id;

            Dictionary<int, int> result = DictionaryPool<int, int>.Get();
            for (int i = 1; i <= MAX_HERO_NUM; i++)
            {
                string key = UserMgr.Ins.GetUserMarkKey($"BattlePos_{userId}_{i}");

                int heroId = PlayerPrefs.GetInt(key, 0);
                result.Add(i, heroId);
            }

            return result;
        }

        private string AUTO_MODE_KEY => UserMgr.Ins.GetUserMarkKey("AutoModeActive");
        public bool GetAutoModeActive()
        {
            return PlayerPrefs.GetInt(AUTO_MODE_KEY, 0) == 1;
        }

        public void SetAutoModeActive(bool active)
        {
            PlayerPrefs.SetInt(AUTO_MODE_KEY, active ? 1 : 0);
        }
    }

    public static class BattleState
    {
        public const string BEFORE_ENTER_SCENE = "BeforeEnterScene";
        public const string ENTER_SCENE = "EnterScene";
        public const string AFTER_ENETR_SCENE = "AfterEnterScene";
        public const string SELECT_HERO = "SelectHero";
        public const string COUNT_DOWN = "CountDown";
        public const string IN_COMBAT = "InCombat";
        public const string END = "End";
        public const string INTERRUPT = "Interrupt";
        public const string EXIT = "Exit";
    }
}