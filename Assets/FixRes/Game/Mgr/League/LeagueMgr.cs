using Core;
using Game.City;
using Game.Network;
using GameUI;
using Google.Protobuf;
using Packet;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Game;
using UIFramework;
using UnityEngine;
using LeagueBaseInfo = Game.LeagueBaseInfo;


namespace GameMain
{
    public class LeagueMgr : LazySingleton<LeagueMgr>, EventReceiver, ISubMgr
    {
        /// <summary>
        /// 联盟科技数据
        /// </summary>
        private LeagueUnionTechnologyAllInfos allInfos = null;

        LeagueFullInfo mSelf = null;

        private UnionResourceInfo _unionResourceInfo = null;
        private WarDeclarationToken _warDeclarationToken = null;

        public LeagueFullInfo Self
        {
            get { return mSelf; }
        }

        int mPosId = 0;
        int mNeedHelpCount = 0;
        int mTodayHelpCount = 0;
        int mTodayHelpRepairCount = 0;
        int mTodayHelpRepairMaxCount = 0;
        int mHelpRepairPerTime = 0;

        public int PosId
        {
            get { return mPosId; }
        }

        Dictionary<ulong, LeagueMenberInfo> mMemberDic = new Dictionary<ulong, LeagueMenberInfo>();
        Dictionary<int, ulong> mSpecPosDic = new Dictionary<int, ulong>();
        Dictionary<int, LeagueRankInfo> mLeagueRankDic = new Dictionary<int, LeagueRankInfo>();
        Dictionary<ulong, LeagueApplyInfo> mApplyListDic = new Dictionary<ulong, LeagueApplyInfo>();
        Dictionary<ulong, LeagueHelpInfo> mHelpInfoDic = new Dictionary<ulong, LeagueHelpInfo>();
        Dictionary<int, Dictionary<int, bool>> mPermissionDic = new Dictionary<int, Dictionary<int, bool>>();
        Dictionary<int, List<ulong>> mMemberRankDic = new Dictionary<int, List<ulong>>();
        List<ulong> mApplyList = new List<ulong>();
        HashSet<ulong> mSelfHelpGuids = new HashSet<ulong>();
        HashSet<int> mReqedHelpAccIds = new HashSet<int>();
        HashSet<long> mReqedLeagueIds = new HashSet<long>();

        List<LeagueListInfo> mLeagueList = new List<LeagueListInfo>();

        //查看其它联盟
        LeagueFullInfo mOtherInfo;
        Dictionary<ulong, LeagueMenberInfo> mOtherMemberDic;

        public LeagueFullInfo GetWatchingInfo()
        {
            return mOtherInfo;
        }

        public Dictionary<ulong, LeagueMenberInfo> GetWatchingMemberDic()
        {
            return mOtherMemberDic;
        }

        public LeagueMenberInfo GetWatchingMemberInfo(ulong uId)
        {
            if (mOtherMemberDic.ContainsKey(uId))
                return mOtherMemberDic[uId];

            return null;
        }

        public List<LeagueListInfo> GetLeagueList()
        {
            return mLeagueList;
        }

        public Dictionary<ulong, LeagueMenberInfo> GetMemberDic()
        {
            return mMemberDic;
        }

        public LeagueMenberInfo GetRandomMember()
        {
            if (mMemberDic.Count <= 0)
            {
                return null;
            }

            var values = mMemberDic.Values.Where((member) => !UserMgr.Ins.IsMyself(member.UserInfo.RoleId)).ToArray();
            return values[UnityEngine.Random.Range(0, values.Length)];
        }

        public Dictionary<int, List<ulong>> GetLeagueMemberRankDic()
        {
            return mMemberRankDic;
        }

        public List<ulong> GetMemberListByRank(int rank)
        {
            if (mMemberRankDic.ContainsKey(rank))
                return mMemberRankDic[rank];
            return null;
        }

        bool bReqedLeagueList = false;

        public bool IsLeagueMember(ulong uid)
        {
            if (mSelf == null) return false;
            return mMemberDic.ContainsKey(uid);
        }

        public void RegisterNetProto()
        {
            LeagueNetwork.Ins.RetLeagueInfoCallBack = OnRetLeagueInfo;
            LeagueNetwork.Ins.RetCreateLeagueCallBack = OnRetCreateLeague;
            LeagueNetwork.Ins.RetLeagueListCallBack = OnRetLeagueList;
            LeagueNetwork.Ins.RetJoinLeagueCallBack = OnRetJoinLeague;
            LeagueNetwork.Ins.RetLeagueRankListCallBack = OnRetLeagueRankList;
            LeagueNetwork.Ins.RetLeagueMenberListCallBack = OnRetLeagueMenberList;
            LeagueNetwork.Ins.RetChgLeagueInfoCallBack = OnRetChgLeagueInfo;
            LeagueNetwork.Ins.RetExitLeagueCallBack = OnRetExitLeague;
            LeagueNetwork.Ins.RetKickLeagueMemberCallBack = OnRetKickLeagueMember;
            LeagueNetwork.Ins.RetSendLeagueMailCallBack = OnRetSendLeagueMail;
            LeagueNetwork.Ins.RetApplyListCallBack = OnRetApplyList;
            LeagueNetwork.Ins.RetSearchLeagueCallBack = OnRetSearchLeague;
            LeagueNetwork.Ins.RetAutoJoinLeagueCallBack = OnRetAutoJoinLeague;
            LeagueNetwork.Ins.RetLeaguePermitChgCallBack = OnRetLeaguePermitChg;
            LeagueNetwork.Ins.RetOperaLeagueApplyCallBack = OnRetOperaLeagueApply;
            LeagueNetwork.Ins.RetLeaguePassLeaderCallBack = OnRetLeaguePassLeader;
            LeagueNetwork.Ins.RetLeagueChgPosCallBack = OnRetLeagueChgPos;
            LeagueNetwork.Ins.RetLeagueHelpListCallBack = OnRetLeagueHelpList;
            LeagueNetwork.Ins.RetLeagueHelpOtherCallBack = OnRetLeagueHelpOther;
            LeagueNetwork.Ins.RetLeagueHelpMeCallBack = OnRetLeagueHelpMe;
            LeagueNetwork.Ins.RetLeagueDisbandCallBack = OnRetLeagueDisband;
            LeagueNetwork.Ins.SyncLeagueInfoCallBack = OnSyncLeagueInfo;
            LeagueNetwork.Ins.SyncLeagueAddMemberCallBack = OnSyncLeagueAddMember;
            LeagueNetwork.Ins.SyncLeagueDelMemberCallBack = OnSyncLeagueDelMember;
            LeagueNetwork.Ins.SyncSomebodyApplyCallBack = OnSyncSomebodyApply;
            LeagueNetwork.Ins.SyncLeaguePermitChgCallBack = OnSyncLeaguePermitChg;
            LeagueNetwork.Ins.SyncSuccJoinLeagueCallBack = OnSyncSuccJoinLeague;
            LeagueNetwork.Ins.SyncLeagueChgPosCallBack = OnSyncLeagueChgPos;
            LeagueNetwork.Ins.SyncLeagueHelpListCallBack = OnSyncLeagueHelpList;
            LeagueNetwork.Ins.SyncLeagueHelpByOtherCallBack = OnSyncLeagueHelpByOther;
            LeagueNetwork.Ins.SyncOperaLeagueApplyCallBack = OnSyncOperaLeagueApply;
            LeagueNetwork.Ins.SyncHelpRepairPerTimeChgCallBack = OnSyncHelpRepairPerTimeChg;
            LeagueNetwork.Ins.SyncLeagueMemberOnlineStateCallBack = OnSyncLeagueMemberOnlineState;
            LeagueNetwork.Ins.SyncRefusedByLeagueCallBack = OnSyncRefusedByLeague;
            LeagueNetwork.Ins.SyncLeagueHelpChgInfoCallBack = OnSyncLeagueHelpChgInfo;
            LeagueNetwork.Ins.RspChangeLeagueLevelNameCallBack = RspChangeLeagueLevelName;
            LeagueNetwork.Ins.RspOtherLeagueInfoCallBack = OnRspOtherLeagueInfo;
            LeagueNetwork.Ins.RspOtherLeagueMemberInfoCallBack = OnRspOtherLeagueMemberInfo;

            LeagueNetwork.Ins.RspUnionResourceInfoCallBack = OnRspUnionResourceInfo;
            LeagueNetwork.Ins.SyncReqUnionResourceInfoChangeCallBack = OnSyncReqUnionResourceInfoChange;
            LeagueNetwork.Ins.SyncReqUnionResourceConsumeTimeChangeCallBack = OnSyncReqUnionResourceConsumeTimeChange;

            LeagueNetwork.Ins.RspLeagueUnionTechnologyAllInfosCallBack = RspLeagueUnionTechnologyAllInfos;
            LeagueNetwork.Ins.RspLeagueUnionTechnologyStudyCallBack = RspLeagueUnionTechnologyStudy;
            LeagueNetwork.Ins.RspLeagueUTDonateCallBack = RspLeagueUTDonate;
            LeagueNetwork.Ins.RspLeagueUnionTechnologyIsRecommendInfoCallBack = RspLeagueUnionTechnologyIsRecommendInfo;
            LeagueNetwork.Ins.SyncReqUnionTechnologyStudySucessCallBack = SyncReqUnionTechnologyStudySucess;
            LeagueNetwork.Ins.RspLeagueUnionTechnologyUpdataDonateCallBack = RspLeagueUnionTechnologyUpdataDonate;
            LeagueNetwork.Ins.SyncLeagueUnionTechnologyUpdataDonateCallBack = SyncLeagueUnionTechnologyUpdataDonateCallBack;
        }


        public void UnregisterNetProto()
        {
            LeagueNetwork.Ins.ClearAllCallback();
        }

        public void OnEnterGame()
        {
            mTodayHelpRepairMaxCount = Cfg.Const.GetVal1(85);
            ReqLeagueInfo();
        }

        public void OnExitGame()
        {
            mSelf = null;
            mPosId = 0;
            mMemberDic.Clear();
            mLeagueList.Clear();
            mLeagueRankDic.Clear();
            mMemberRankDic.Clear();
            mApplyList.Clear();
            mApplyListDic.Clear();
            mPermissionDic.Clear();
            mHelpInfoDic.Clear();
            mSelfHelpGuids.Clear();
            mReqedHelpAccIds.Clear();
            mSpecPosDic.Clear();
            mReqedLeagueIds.Clear();
            _unionResourceInfo = null;
            allInfos = null;
        }


        public void AfterEnterGame()
        {
        }

        //是否盟主
        public bool IsLeader()
        {
            return mPosId == 1;
        }

        /// <summary>
        /// 请求联盟信息
        /// </summary>
        void ReqLeagueInfo()
        {
            LeagueNetwork.Ins.ReqLeagueInfo();
        }

        void OnRetLeagueInfo(IMessage imsg)
        {
            RetLeagueInfo msg = imsg as RetLeagueInfo;

            if (msg.HasPosId)
            {
                mSelf = msg.Info;
                mPosId = msg.PosId;
                RefreshPermission();
                RefreshHelpAccIds();
                ReqApplyList();
                ReqLeagueMenberList();
                ReqLeagueHelpList();
                ReqLeagueUnionTechnologyAllInfo();
                LeagueNetwork.Ins.ReqUnionResourceInfo();
                mHelpRepairPerTime = mSelf.RepairPerTime;
                if (mHelpRepairPerTime == 0)
                    mHelpRepairPerTime = Cfg.Const.GetVal1(81);
            }
            else
            {
                for (int i = 0; i < msg.ReqLeagueIds.Count; i++)
                {
                    mReqedLeagueIds.Add(msg.ReqLeagueIds[i]);
                }
            }

            UserMgr.Ins.OnOneSystemRecvData(ToString());
            //CreatTest();
        }

        public bool IsRequiredLeague(long id)
        {
            return mReqedLeagueIds.Contains(id);
        }

        /// <summary>
        /// 请求创建联盟
        /// </summary>
        /// <param name="name"></param>
        /// <param name="shortName"></param>
        /// <param name="notice"></param>
        /// <param name="flagBgId"></param>
        /// <param name="flagBgColorId"></param>
        /// <param name="flagIconId"></param>
        public void ReqCreateLeague(string name, string shortName, string notice, int flagBgId, int flagBgColorId,
            int flagIconId)
        {
            LeagueNetwork.Ins.ReqCreateLeague(name, shortName, notice, flagBgId, flagBgColorId, flagIconId);
        }

        void OnRetCreateLeague(IMessage imsg)
        {
            RetCreateLeague msg = imsg as RetCreateLeague;
            mSelf = msg.Info;
            mPosId = 1; //盟主
            UserMgr.Ins.SetLeagueInfo(mSelf.Info);

            ReqLeagueMenberList();
            RefreshPermission();
            OpenUIView();
            ReqLeagueUnionTechnologyAllInfo();
            //加入后更新联盟矿石数量
            LeagueNetwork.Ins.ReqUnionResourceInfo();
        }

        /// <summary>
        /// 请求联盟列表
        /// </summary>
        public void ReqLeagueList(bool force = false, Action callBack = null)
        {
            if (!bReqedLeagueList || force)
            {
                bReqedLeagueList = true;
                mLeagueList.Clear();
                LeagueNetwork.Ins.ReqLeagueList();
            }
            else
            {
                callBack?.Invoke();
            }
        }

        public void ResetReqMark()
        {
            bReqedLeagueList = false;
        }

        void OnRetLeagueList(IMessage imsg)
        {
            RetLeagueList msg = imsg as RetLeagueList;
            for (int i = 0; i < msg.InfoList.Count; i++)
            {
                var info = msg.InfoList[i];

                mLeagueList.Add(info);
            }

            EventManager.Ins.DispatchEvent(UIEventType.OnRetLeagueList);
        }

        /// <summary>
        /// 请求加入联盟
        /// </summary>
        /// <param name="leagueId"></param>
        public void ReqJoinLeague(long leagueId)
        {
            LeagueNetwork.Ins.ReqJoinLeague(leagueId);
            if (!mReqedLeagueIds.Contains(leagueId))
                mReqedLeagueIds.Add(leagueId);
        }

        void OnRetJoinLeague(IMessage imsg)
        {
            TipsMgr.Ins.ShowTips(LanguageCode.ApplySuccess);
            EventManager.Ins.DispatchEvent(UIEventType.OnLeagueApplied);
        }

        /// <summary>
        /// 请求联盟排行榜
        /// </summary>
        /// <param name="startIndex"></param>
        /// <param name="count"></param>
        public void ReqLeagueRankList(int startIndex, int count)
        {
            LeagueNetwork.Ins.ReqLeagueRankList(startIndex, count);
        }

        void OnRetLeagueRankList(IMessage imsg)
        {
        }

        /// <summary>
        /// 请求成员列表
        /// </summary>
        public void ReqLeagueMenberList()
        {
            LeagueNetwork.Ins.ReqLeagueMenberList();
        }

        void OnRetLeagueMenberList(IMessage imsg)
        {
            ulong lastPower = mSelf.Info.Power;
            mSelf.Info.Power = 0;
            mMemberRankDic.Clear();
            mSpecPosDic.Clear();
            RetLeagueMenberList msg = imsg as RetLeagueMenberList;
            for (int i = 0; i < msg.MemberList.Count; i++)
            {
                UpdateMemberInfo(msg.MemberList[i]);
            }

            EventManager.Ins.DispatchEvent(UIEventType.OnLeagueMemberRefresh);
            if (lastPower != mSelf.Info.Power)
                EventManager.Ins.DispatchEvent(UIEventType.OnLeagueInfoRefresh);
        }

        public LeagueMenberInfo GetMemberInfoBySpecPos(int posId)
        {
            if (mSpecPosDic.ContainsKey(posId))
            {
                ulong uId = mSpecPosDic[posId];
                return GetMemberInfo(uId);
            }

            return null;
        }

        public LeagueMenberInfo GetMemberInfo(ulong uId)
        {
            if (mMemberDic.ContainsKey(uId))
                return mMemberDic[uId];

            return null;
        }

        void UpdateMemberInfo(LeagueMenberInfo info)
        {
            ulong uid = info.UserInfo.RoleId;
            if (!mMemberDic.ContainsKey(uid))
            {
                mMemberDic.Add(uid, info);
            }
            else
            {
                mMemberDic[uid] = info;
            }

            mSelf.Info.Power += info.UserInfo.Power;
            var cfg = Cfg.Permit.GetData(info.PosId);
            if (cfg == null) return;
            if (info.PosId > 5 || info.PosId == 1)
            {
                if (!mSpecPosDic.ContainsKey(info.PosId))
                    mSpecPosDic.Add(info.PosId, uid);
            }

            if (!mMemberRankDic.ContainsKey(cfg.permit_level))
            {
                List<ulong> list = new List<ulong>();
                list.Add(uid);
                mMemberRankDic.Add(cfg.permit_level, list);
            }
            else
            {
                mMemberRankDic[cfg.permit_level].Add(uid);
            }
        }


        public void ReqChgLeagueInfo(string lName, string lsName, string lNotice, int flagBgId, int flagBgColorId,
            int flagIconId, int limitLv, long limitPower, int reqType)
        {
            LeagueNetwork.Ins.ReqChgLeagueInfo(lName, lsName, lNotice, flagBgId, flagBgColorId, flagIconId, limitLv,
                limitPower, reqType);
        }

        void OnRetChgLeagueInfo(IMessage imsg)
        {
            TipsMgr.Ins.ShowTips(LanguageCode.SettingSuccess);
            EventManager.Ins.DispatchEvent(UIEventType.OnLeagueInfoRefresh);
        }

        void OnSyncLeagueInfo(IMessage imsg)
        {
            if (Self == null) return;

            SyncLeagueInfo msg = imsg as SyncLeagueInfo;
            Self.Info = msg.Info;
            Self.LeagueNotice = msg.LeagueNotice;
            Self.LimitLv = msg.LimitLv;
            Self.LimitPower = msg.LimitPower;
            Self.ReqType = msg.ReqType;
            if (!msg.LeagueLeaderName.IsNullOrEmptyEx())
                Self.LeagueLeaderName = msg.LeagueLeaderName;

            EventManager.Ins.DispatchEvent(UIEventType.OnLeagueInfoRefresh);
        }

        void OnSyncLeagueAddMember(IMessage imsg)
        {
            SyncLeagueAddMember msg = imsg as SyncLeagueAddMember;
            for (int i = 0; i < msg.InfoList.Count; i++)
            {
                UpdateMemberInfo(msg.InfoList[i]);
                OnChgApplyMember(msg.InfoList[i].UserInfo.RoleId, false);
            }

            EventManager.Ins.DispatchEvent(UIEventType.OnOperatedApply);
            EventManager.Ins.DispatchEvent(UIEventType.OnLeagueMemberRefresh);
        }

        void OnSyncLeagueDelMember(IMessage imsg)
        {
            SyncLeagueDelMember msg = imsg as SyncLeagueDelMember;
            bool haveMe = false;
            for (int i = 0; i < msg.UserId.Count; i++)
            {
                ulong uid = msg.UserId[i];
                if (!haveMe)
                    haveMe = UserMgr.Ins.IsMyself(uid);
                if (mMemberDic.TryGetValue(uid, out LeagueMenberInfo info))
                {
                    var cfg = Cfg.Permit.GetData(info.PosId);
                    if (cfg != null)
                    {
                        if (mMemberRankDic.ContainsKey(cfg.permit_level))
                        {
                            mMemberRankDic[cfg.permit_level].Remove(uid);
                        }
                    }

                    mSelf.Info.Power -= info.UserInfo.Power;
                }

                mMemberDic.Remove(msg.UserId[i]);
            }

            if (haveMe)
            {
                TipsMgr.Ins.ShowTips(Localization.ToFormat(240, mSelf.Info.LeagueName));
                mSelf = null;
                mMemberDic.Clear();
                EventManager.Ins.DispatchEvent(UIEventType.OnExitLeague);
                LeagueTeamMgr.Ins.OnExitLeague();
                CloseAllLeaguePanel();
            }
            else
            {
                EventManager.Ins.DispatchEvent(UIEventType.OnLeagueMemberRefresh);
            }
        }

        void CloseAllLeaguePanel()
        {
            for (int i = UIDefine.LeagueMainPanel; i < UIDefine.LeaguePanelMax; i++)
            {
                UIMgr.Ins.ClosePanel(i, true);
            }
        }

        public void ReqLeagueDisband()
        {
            LeagueNetwork.Ins.ReqLeagueDisband();
        }

        void OnRetLeagueDisband(IMessage imsg)
        {
            mSelf = null;
            mMemberDic.Clear();
            UIMgr.Ins.ClosePanel(UIDefine.LeagueMainPanel);
            EventManager.Ins.DispatchEvent(UIEventType.OnExitLeague);
            LeagueTeamMgr.Ins.OnExitLeague();
            TipsMgr.Ins.ShowTips(LanguageCode.DisbandLeagueSuccess);
        }

        public void ReqExitLeague()
        {
            LeagueNetwork.Ins.ReqExitLeague();
        }

        void OnRetExitLeague(IMessage imsg)
        {
            TipsMgr.Ins.ShowTips(Localization.ToFormat(434, mSelf.Info.LeagueName));
            mSelf = null;
            mMemberDic.Clear();
            UserMgr.Ins.SetLeagueInfo(null);
            EventManager.Ins.DispatchEvent(UIEventType.OnExitLeague);
            LeagueTeamMgr.Ins.OnExitLeague();
            CloseAllLeaguePanel();
            _unionResourceInfo = null;
        }

        public void ReqKickLeagueMember(ulong uid, int reason)
        {
            LeagueNetwork.Ins.ReqKickLeagueMember(uid, reason);
        }

        void OnRetKickLeagueMember(IMessage imsg)
        {
            TipsMgr.Ins.ShowTips(LanguageCode.OperaSuccess);
            if (UIMgr.Ins.IsOpened(UIDefine.LeagueKickMemberPanel))
                UIMgr.Ins.ClosePanel(UIDefine.LeagueKickMemberPanel);

            if (UIMgr.Ins.IsOpened(UIDefine.LeagueOperaFloatPanel))
                UIMgr.Ins.ClosePanel(UIDefine.LeagueOperaFloatPanel);
        }

        public void ReqSendLeagueMail(string title, string content)
        {
            LeagueNetwork.Ins.ReqSendLeagueMail(title, content);
        }

        void OnRetSendLeagueMail(IMessage imsg)
        {
            TipsMgr.Ins.ShowTips(LanguageCode.SendSuccess);
            if (UIMgr.Ins.IsOpened(UIDefine.LeagueMailPanel))
                UIMgr.Ins.ClosePanel(UIDefine.LeagueMailPanel);
        }

        public void ReqApplyList()
        {
            LeagueNetwork.Ins.ReqApplyList();
        }

        void OnRetApplyList(IMessage imsg)
        {
            RetApplyList msg = imsg as RetApplyList;


            for (int i = 0; i < msg.UserList.Count; i++)
            {
                var user = msg.UserList[i];
                UpdateApplyInfo(user);
            }

            RefreshApplyReddot();
            EventManager.Ins.DispatchEvent(UIEventType.OnRetLeagueApplyList);
        }

        void UpdateApplyInfo(LeagueApplyInfo info)
        {
            ulong uid = info.UserInfo.RoleId;
            if (!mApplyListDic.ContainsKey(uid))
            {
                mApplyListDic.Add(uid, info);
            }
            else
            {
                mApplyListDic[uid] = info;
            }

            mApplyList.Add(uid);
        }

        public List<ulong> GetApplyList()
        {
            return mApplyList;
        }

        public LeagueApplyInfo GetApplyPlayer(ulong id)
        {
            if (mApplyListDic.ContainsKey(id))
                return mApplyListDic[id];

            return null;
        }

        /// <summary>
        /// 通知有人申请加入联盟（用于红点显示）
        /// </summary>
        /// <param name="imsg"></param>
        void OnSyncSomebodyApply(IMessage imsg)
        {
            SyncSomebodyApply msg = imsg as SyncSomebodyApply;
            UpdateApplyInfo(msg.Info);

            RefreshApplyReddot();
            EventManager.Ins.DispatchEvent(UIEventType.OnRetLeagueApplyList);
        }

        void RefreshApplyReddot()
        {
            var cfg = Cfg.Permit.GetData(PosId);
            if (cfg != null && GetPermitState(1, cfg.permit_level))
            {
                ReddotMgr.Ins.UpdateReddotState(602, mApplyList.Count > 0, mApplyList.Count);
            }
        }

        public void ReqSearchLeague(string searchName)
        {
            LeagueNetwork.Ins.ReqSearchLeague(searchName);
        }

        void OnRetSearchLeague(IMessage imsg)
        {
            mLeagueList.Clear();
            RetSearchLeague msg = imsg as RetSearchLeague;
            for (int i = 0; i < msg.InfoList.Count; i++)
            {
                var info = msg.InfoList[i];

                mLeagueList.Add(info);
            }

            EventManager.Ins.DispatchEvent(UIEventType.OnRetLeagueList);

            if (msg.InfoList.Count == 0)
            {
                TipsMgr.Ins.ShowTips(LanguageCode.CannotFindLeague);
            }
        }

        public void ReqAutoJoinLeague()
        {
            LeagueNetwork.Ins.ReqAutoJoinLeague();
        }

        void OnRetAutoJoinLeague(IMessage imsg)
        {
            RetAutoJoinLeague msg = imsg as RetAutoJoinLeague;
            mSelf = msg.Info;
            RefreshPermission();
            OnNewJoinLeague();
        }

        void OnNewJoinLeague()
        {
            mReqedLeagueIds.Clear();
            mPosId = 5; //普通成员
            ReqApplyList();
            ReqLeagueMenberList();
            ReqLeagueHelpList();
            LeagueTeamMgr.Ins.ReqLeagueTeamList();
            EventManager.Ins.DispatchEvent(UIEventType.OnSucessJoinLeague);

            UIMgr.Ins.ClosePanel(UIDefine.LeagueDetailPanel);
        }

        void RefreshPermission()
        {
            if (mSelf == null) return;
            mPermissionDic.Clear();
            for (int i = 0; i < mSelf.PermitFlags.Count; i++)
            {
                int flag = mSelf.PermitFlags[i];
                Dictionary<int, bool> dic = new Dictionary<int, bool>();
                for (int j = 0; j < 5; j++)
                {
                    dic.Add(j, UtilTool.CheckBit(flag, j));
                }

                mPermissionDic.Add(i, dic);
            }

            EventManager.Ins.DispatchEvent(UIEventType.OnSyncLeaguePermitChg);
        }

        void RefreshHelpAccIds()
        {
            if (mSelf == null) return;
            mReqedHelpAccIds.Clear();

            for (int i = 0; i < mSelf.HelpAccIds.Count; i++)
            {
                int id = mSelf.HelpAccIds[i];
                if (!mReqedHelpAccIds.Contains(id))
                    mReqedHelpAccIds.Add(id);
            }
        }

        public bool GetPermitState(int type, int rNum)
        {
            rNum--;
            type--;
            if (rNum == 4) return true;

            if (mPermissionDic.ContainsKey(rNum))
            {
                if (mPermissionDic[rNum].ContainsKey(type))
                {
                    return mPermissionDic[rNum][type];
                }
            }

            return false;
        }

        public bool IsMyLeague(LeagueBaseInfo baseinfo)
        {
            return baseinfo != null && IsMyLeague(baseinfo.LeagueId);
        }
        public bool IsMyLeague(long leagueId)
        {
            if (mSelf == null) return false;
            return mSelf.Info.LeagueId == leagueId;
        }

        public void ReqLeaguePermitChg(List<int> permitFlags)
        {
            LeagueNetwork.Ins.ReqLeaguePermitChg(permitFlags);
        }

        void OnRetLeaguePermitChg(IMessage imsg)
        {
            RetLeaguePermitChg msg = imsg as RetLeaguePermitChg;

            for (int i = 0; i < mSelf.PermitFlags.Count; i++)
            {
                mSelf.PermitFlags[i] = msg.PermitFlags[i];
            }

            RefreshPermission();
            TipsMgr.Ins.ShowTips(LanguageCode.OperaSuccess);
        }

        void OnSyncLeaguePermitChg(IMessage imsg)
        {
            SyncLeaguePermitChg msg = imsg as SyncLeaguePermitChg;

            for (int i = 0; i < mSelf.PermitFlags.Count; i++)
            {
                mSelf.PermitFlags[i] = msg.PermitFlags[i];
            }

            RefreshPermission();
        }

        void OnSyncSuccJoinLeague(IMessage imsg)
        {
            SyncSuccJoinLeague msg = imsg as SyncSuccJoinLeague;

            mSelf = msg.Info;
            OnNewJoinLeague();
            RefreshPermission();
            OpenUIView(false);
            UserMgr.Ins.SetLeagueInfo(mSelf.Info);
            ReqLeagueUnionTechnologyAllInfo();
            //加入后更新联盟矿石数量
            LeagueNetwork.Ins.ReqUnionResourceInfo();
        }

        public void ReqOperaLeagueApply(ulong userId, bool isAgree)
        {
            LeagueNetwork.Ins.ReqOperaLeagueApply(userId, isAgree);
            //操作完，不管结果，直接删掉
            OnChgApplyMember(userId);
        }

        void OnRetOperaLeagueApply(IMessage imsg)
        {
            RetOperaLeagueApply msg = imsg as RetOperaLeagueApply;

            OnChgApplyMember(msg.UserId);
        }

        void OnSyncOperaLeagueApply(IMessage imsg)
        {
            SyncOperaLeagueApply msg = imsg as SyncOperaLeagueApply;

            OnChgApplyMember(msg.UserId);
        }

        void OnSyncHelpRepairPerTimeChg(IMessage imsg)
        {
            SyncHelpRepairPerTimeChg msg = imsg as SyncHelpRepairPerTimeChg;
            mHelpRepairPerTime = msg.RepairPerTime;
        }

        void OnSyncLeagueMemberOnlineState(IMessage imsg)
        {
            SyncLeagueMemberOnlineState msg = imsg as SyncLeagueMemberOnlineState;
            for (int i = 0; i < msg.StateList.Count; i++)
            {
                var info = GetMemberInfo(msg.StateList[i].Uid);
                if (info != null)
                {
                    info.IsOnline = msg.StateList[i].IsOnline;
                }
            }

            EventManager.Ins.DispatchEvent(UIEventType.OnlineStateChg);
        }

        void OnSyncRefusedByLeague(IMessage imsg)
        {
            SyncRefusedByLeague msg = imsg as SyncRefusedByLeague;
            if (mReqedLeagueIds.Contains(msg.LeagueId))
                mReqedLeagueIds.Remove(msg.LeagueId);

            EventManager.Ins.DispatchEvent(UIEventType.OnLeagueApplied);
        }

        void OnSyncLeagueHelpChgInfo(IMessage imsg)
        {
            SyncLeagueHelpChgInfo msg = imsg as SyncLeagueHelpChgInfo;
            for (int i = 0; i < msg.ChgList.Count; i++)
            {
                var data = msg.ChgList[i];
                ulong uid = data.Guid;
                if (mHelpInfoDic.TryGetValue(uid, out var info))
                {
                    info.CurVal = data.CurVal;
                    info.MaxVal = data.MaxVal;

                    if (info.CurVal == info.MaxVal)
                        mHelpInfoDic.Remove(uid);
                }
            }

            EventManager.Ins.DispatchEvent(UIEventType.OnSyncLeagueHelpList);
        }

        void OnChgApplyMember(ulong userId, bool send = true)
        {
            if (mApplyListDic.ContainsKey(userId))
            {
                mApplyListDic.Remove(userId);
                mApplyList.Remove(userId);

                RefreshApplyReddot();
                if (send)
                    EventManager.Ins.DispatchEvent(UIEventType.OnOperatedApply);
            }
        }

        public void ReqLeaguePassLeader(ulong userId)
        {
            LeagueNetwork.Ins.ReqLeaguePassLeader(userId);
        }

        void OnRetLeaguePassLeader(IMessage imsg)
        {
            mPosId = 5; //普通成员

            TipsMgr.Ins.ShowTips(LanguageCode.PassLeaderSuccess);
        }

        public void ReqLeagueChgPos(ulong userId, int posId)
        {
            LeagueNetwork.Ins.ReqLeagueChgPos(userId, posId);
        }

        void OnRetLeagueChgPos(IMessage imsg)
        {
            RetLeagueChgPos msg = imsg as RetLeagueChgPos;

            TipsMgr.Ins.ShowTips(LanguageCode.LeagueChgPosSuccess);
            OnMemberChgPos(msg.Guid, msg.PosId);

            if (UIMgr.Ins.IsOpened(UIDefine.LeagueChgPosPanel))
                UIMgr.Ins.ClosePanel(UIDefine.LeagueChgPosPanel);

            if (UIMgr.Ins.IsOpened(UIDefine.LeagueSetPosPanel))
                UIMgr.Ins.ClosePanel(UIDefine.LeagueSetPosPanel);
        }

        void OnSyncLeagueChgPos(IMessage imsg)
        {
            SyncLeagueChgPos msg = imsg as SyncLeagueChgPos;
            OnMemberChgPos(msg.Guid, msg.PosId);
        }

        #region 联盟帮助

        public int GetNeedHelpCount()
        {
            //DLogger.Log("mNeedHelpCount {0}", mNeedHelpCount);
            return mNeedHelpCount;
        }

        public int GetTodayHelpCount()
        {
            return mTodayHelpCount;
        }

        public int GetTodayHelpRepairCount()
        {
            return mTodayHelpRepairCount;
        }

        public int GetHelpRepairPerTime()
        {
            return mHelpRepairPerTime;
        }

        public bool IsReqedHelp(int accId, bool isRepair = false)
        {
            if (isRepair)
            {
                //如果大于当天可帮助次数上限，就不能再帮助了
                if (mTodayHelpRepairMaxCount <= mTodayHelpRepairCount)
                    return true;
            }

            if (mSelf == null) return true;

            return mReqedHelpAccIds.Contains(accId);
        }

        public void ReqLeagueHelpList()
        {
            LeagueNetwork.Ins.ReqLeagueHelpList();
        }

        void OnRetLeagueHelpList(IMessage imsg)
        {
            RetLeagueHelpList msg = imsg as RetLeagueHelpList;
            for (int i = 0; i < msg.HelpList.Count; i++)
            {
                mNeedHelpCount += UpdateHelpInfo(msg.HelpList[i]);
            }

            mTodayHelpCount = msg.TodayVal;
            EventManager.Ins.DispatchEvent(UIEventType.OnSyncLeagueHelpList);
        }

        int UpdateHelpInfo(LeagueHelpInfo info)
        {
            int ret = 0;
            if (!mHelpInfoDic.ContainsKey(info.Guid))
            {
                mHelpInfoDic.Add(info.Guid, info);
                if (!info.IsHelped)
                    ret = 1;
            }
            else
            {
                mHelpInfoDic[info.Guid] = info;
            }

            return ret;
        }

        void OnSyncLeagueHelpList(IMessage imsg)
        {
            SyncLeagueHelpList msg = imsg as SyncLeagueHelpList;
            for (int i = 0; i < msg.HelpList.Count; i++)
            {
                mNeedHelpCount += UpdateHelpInfo(msg.HelpList[i]);
            }


            EventManager.Ins.DispatchEvent(UIEventType.OnSyncLeagueHelpList);
        }

        public void AutoHelpOther()
        {
            List<ulong> list = new List<ulong>();
            foreach (var item in mHelpInfoDic)
            {
                var data = item.Value;
                if (!data.IsHelped)
                    list.Add(data.Guid);
            }

            if (list.Count > 0)
                ReqLeagueHelpOther(list);
            else
                TipsMgr.Ins.ShowTips(1000302);
        }

        public void ReqLeagueHelpOther(List<ulong> guids)
        {
            LeagueNetwork.Ins.ReqLeagueHelpOther(guids);

            TipsMgr.Ins.ShowTips(1000301);
        }

        void OnRetLeagueHelpOther(IMessage imsg)
        {
            RetLeagueHelpOther msg = imsg as RetLeagueHelpOther;
            for (int i = 0; i < msg.Guid.Count; i++)
            {
                if (mHelpInfoDic.ContainsKey(msg.Guid[i]))
                {
                    var info = mHelpInfoDic[msg.Guid[i]];
                    info.CurVal += 1;
                    info.IsHelped = true;
                    mNeedHelpCount--;
                }
            }

            mTodayHelpCount = msg.TodayVal;
            EventManager.Ins.DispatchEvent(UIEventType.OnSyncLeagueHelpList);
        }

        public void ReqLeagueHelpMe(int helpType, int buildingId, int param, int accId)
        {
            LeagueNetwork.Ins.ReqLeagueHelpMe(helpType, buildingId, param, accId);
        }

        void OnRetLeagueHelpMe(IMessage imsg)
        {
            RetLeagueHelpMe msg = imsg as RetLeagueHelpMe;

            if (!mSelfHelpGuids.Contains(msg.Guid))
                mSelfHelpGuids.Add(msg.Guid);

            if (!mReqedHelpAccIds.Contains(msg.AccelerateId))
                mReqedHelpAccIds.Add(msg.AccelerateId);

            EventManager.Ins.DispatchEvent(UIEventType.OnRetLeagueHelpMe);
            TipsMgr.Ins.ShowTips(LanguageCode.ReqLeagueHelpSucc);
        }

        void OnSyncLeagueHelpByOther(IMessage imsg)
        {
            SyncLeagueHelpByOther msg = imsg as SyncLeagueHelpByOther;

            for (int i = 0; i < msg.HelpList.Count; i++)
            {
                var hInfo = msg.HelpList[i];
                var userInfo = GetMemberInfo(hInfo.HelperUserId);

                switch (hInfo.HelpType)
                {
                    case 1: //建造建筑
                    case 2: //升级建筑
                        {
                            int minusTime = Cfg.Const.GetVal1(71);
                            BuildingConstructionInfo info = SpeedUpMgr.Ins.GetBuildingConstructionInfos(hInfo.BuildingId);
                            if (info != null)
                            {
                                info.realEndTime -= minusTime;
                            }

                            var bCfg = Cfg.Building.GetData(hInfo.BuildingId);
                            if (bCfg != null)
                            {
                                TipsMgr.Ins.ShowTips(Localization.ToFormat(LanguageCode.HelpMeBuildNotice,
                                    userInfo.UserInfo.Name, hInfo.Param, Localization.ToFormat(bCfg.name), hInfo.CurVal,
                                    hInfo.MaxVal));
                            }
                        }
                        break;
                    case 3: //治疗伤兵
                        {
                            int minusTime = Cfg.Const.GetVal1(70);
                            var info = SoldierRepairMgr.Ins.GetRepairInfo();
                            if (info != null)
                            {
                                info.endTime -= minusTime;
                            }

                            TipsMgr.Ins.ShowTips(Localization.ToFormat(LanguageCode.HelpMeFixNotice,
                                userInfo.UserInfo.Name, hInfo.CurVal, hInfo.MaxVal));

                            mTodayHelpRepairCount++;
                        }
                        break;
                    case 4: //科技升级
                        {
                            int minusTime = Cfg.Const.GetVal1(72);
                            var technology= Cfg.Technology.GetData(hInfo.Param);
                            if (technology != null)
                            {
                                var info = TechnologyMgr.Ins.GetTechnologyProjectInfo(technology.node);
                                if (info != null)
                                {
                                    info.FinishTime -= minusTime;
                                    if (info.FinishTime < 0)
                                        info.FinishTime = 0;
                                }
                            }

                            var tCfg = Cfg.Technology.GetData(hInfo.Param);
                            if (tCfg != null)
                            {
                                TipsMgr.Ins.ShowTips(Localization.ToFormat(LanguageCode.HelpMeResearchNotice,
                                    userInfo.UserInfo.Name, Localization.ToFormat(tCfg.name_id), hInfo.CurVal,
                                    hInfo.MaxVal));
                            }
                        }
                        break;
                }
            }
            
            EventManager.Ins.DispatchEvent(UIEventType.LeagueHelpByOtherFinish);
        }

        public Dictionary<ulong, LeagueHelpInfo> GetHelpInfoDic()
        {
            return mHelpInfoDic;
        }

        #endregion

        void OnMemberChgPos(ulong guid, int posId)
        {
            if (mMemberDic.ContainsKey(guid))
            {
                int orgPosId = mMemberDic[guid].PosId;
                mMemberDic[guid].PosId = posId;
                var cfg = Cfg.Permit.GetData(orgPosId);
                if (cfg != null)
                {
                    if (mMemberRankDic.TryGetValue(cfg.permit_level, out List<ulong> orgList))
                    {
                        orgList.Remove(guid);
                    }

                    if (orgPosId > 5 || orgPosId == 1)
                    {
                        if (!mSpecPosDic.ContainsKey(orgPosId))
                            mSpecPosDic.Remove(orgPosId);
                    }
                }

                cfg = Cfg.Permit.GetData(posId);
                if (cfg != null)
                {
                    if (mMemberRankDic.TryGetValue(cfg.permit_level, out List<ulong> list))
                    {
                        list.Add(guid);
                    }
                    else
                    {
                        list = new List<ulong>();
                        list.Add(guid);
                        mMemberRankDic.Add(cfg.permit_level, list);
                    }

                    if (posId > 5 || posId == 1)
                    {
                        if (!mSpecPosDic.ContainsKey(posId))
                            mSpecPosDic.Add(posId, guid);

                        if (posId == 1)
                        {
                            mSelf.LeagueLeaderName = mMemberDic[guid].UserInfo.Name;
                        }
                    }
                }

                if (guid == UserMgr.Ins.Self.role_id)
                {
                    mPosId = posId;
                }

                EventManager.Ins.DispatchEvent(UIEventType.OnLeaguePosChged);
            }
        }

        public void OpenUIView(bool forceOpen = true)
        {
            if (mSelf != null) //有联盟
            {
                if (UIMgr.Ins.IsOpened(UIDefine.LeaguePanel) || forceOpen)
                {
                    UIMgr.Ins.ClosePanel(UIDefine.LeaguePanel);
                    UIMgr.Ins.Open(UIDefine.LeagueMainPanel);
                }
            }
            else //无联盟
            {
                if (UIMgr.Ins.IsOpened(UIDefine.LeagueMainPanel) || forceOpen)
                {
                    UIMgr.Ins.ClosePanel(UIDefine.LeagueMainPanel);
                    UIMgr.Ins.Open(UIDefine.LeaguePanel);
                }
            }
        }

        public void OnMidNight()
        {
            mTodayHelpCount = 0;
            mTodayHelpRepairCount = 0;
        }

        public void ReqOtherLeagueInfo(long leagueId)
        {
            LeagueNetwork.Ins.ReqOtherLeagueInfo(leagueId);
        }

        void OnRspOtherLeagueInfo(IMessage imsg)
        {
            RspOtherLeagueInfo msg = imsg as RspOtherLeagueInfo;
            mOtherInfo = msg.Info;
            //UIMgr.Ins.ClosePanel(UIDefine.WaitingPanel);
            UIMgr.Ins.Open(UIDefine.LeagueDetailPanel);
        }

        public void ReqOtherLeagueMemberInfo(long leagueId)
        {
            LeagueNetwork.Ins.ReqOtherLeagueMemberInfo(leagueId);
        }

        void OnRspOtherLeagueMemberInfo(IMessage imsg)
        {
            if (mOtherMemberDic == null)
                mOtherMemberDic = new Dictionary<ulong, LeagueMenberInfo>();

            mOtherMemberDic.Clear();
            RspOtherLeagueMemberInfo msg = imsg as RspOtherLeagueMemberInfo;
            for (int i = 0; i < msg.InfoList.Count; i++)
            {
                mOtherMemberDic.Add(msg.InfoList[i].UserInfo.RoleId, msg.InfoList[i]);
            }

            //UIMgr.Ins.ClosePanel(UIDefine.WaitingPanel);
            UIMgr.Ins.Open(UIDefine.LeagueMemberShowPanel);
        }

        private void RspChangeLeagueLevelName(IMessage imsg)
        {
            RspChangeLeagueLevelName msg = imsg as RspChangeLeagueLevelName;
            if (mSelf == null)
            {
                return;
            }

            for (int i = 0; i < msg.PermitFlagsName.Count; i++)
            {
                mSelf.PermitFlagsName[i] = msg.PermitFlagsName[i];
            }

            EventManager.Ins.DispatchEvent(UIEventType.OnChangeLeagueLevelName);
        }

        /// <summary>
        /// 修改联盟阶级name
        /// 从1开始
        /// </summary>
        /// <param name="levelId"></param>
        /// <param name="name"></param>
        public void ReqChangeLeagueLevelName(int levelId, string name)
        {
            LeagueNetwork.Ins.ReqChangeLeagueLevelName(levelId, name);
        }

        /// <summary>
        /// 查找阶级对应的 名称
        /// 从0 开始
        /// </summary>
        /// <param name="levelId"></param>
        /// <returns></returns>
        public string GetLeagueLevelName(int levelId)
        {
            string result = string.Empty;
            if (mSelf != null && mSelf.PermitFlagsName != null)
            {
                if (!string.IsNullOrEmpty(mSelf.PermitFlagsName[levelId]))
                {
                    result = mSelf.PermitFlagsName[levelId];
                }
            }

            return result;
        }


        public string GetWatchingLeagueLevelName(int levelId)
        {
            string result = string.Empty;
            if (mOtherInfo != null && mOtherInfo.PermitFlagsName != null)
            {
                if (!string.IsNullOrEmpty(mOtherInfo.PermitFlagsName[levelId]))
                {
                    result = mOtherInfo.PermitFlagsName[levelId];
                }
            }

            return result;
        }

        /// <summary>
        /// 判断 ran标签对应的权限 
        /// </summary>
        public bool CheckChangeNameLevel(int rankLevel, int type = 1)
        {
            if (PosId <= 0) return false;
            var level = LeagueUtils.GetPermitLv(LeagueMgr.Ins.PosId);
            if (GetPermitState(type, level))
            {
                return true;
            }

            return false;
        }


        private void OnSyncReqUnionResourceConsumeTimeChange(IMessage imsg)
        {
            SyncReqUnionResourceConsumeTimeChange msg = imsg as SyncReqUnionResourceConsumeTimeChange;
            if (msg == null) return;
            if (_unionResourceInfo == null) return;
            _unionResourceInfo.ProducePerHour = msg.ProducePerHour;
            _unionResourceInfo.CurrentAmount = msg.CurrentAmount;
            EventManager.Ins.DispatchEvent(UIEventType.OnSyncReqUnionResourceConsumeTimeChange);
        }

        private void OnSyncReqUnionResourceInfoChange(IMessage imsg)
        {
            SyncReqUnionResourceInfoChange msg = imsg as SyncReqUnionResourceInfoChange;
            if (msg == null) return;
            if (_unionResourceInfo == null) return;
            _unionResourceInfo = msg.UnionResourceInfo;
            _warDeclarationToken = msg.WarDeclarationTokenInfo;
            EventManager.Ins.DispatchEvent(UIEventType.OnRspUnionResourceInfo);
        }

        private void OnRspUnionResourceInfo(IMessage imsg)
        {
            RspUnionResourceInfo msg = imsg as RspUnionResourceInfo;
            if (msg == null) return;
            _unionResourceInfo = msg.UnionResourceInfo;
            _warDeclarationToken = msg.WarDeclarationTokenInfo;
            EventManager.Ins.DispatchEvent(UIEventType.OnRspUnionResourceInfo);
        }

        public UnionResourceInfo GetUnionResourceInfo()
        {
            if (Self == null) return null;
            if (_unionResourceInfo == null) return null;
            return _unionResourceInfo;
        }

        public WarDeclarationToken GetWarDeclarationTokenInfo()
        {
            if (Self == null) return null;
            if (_warDeclarationToken == null) return null;
            return _warDeclarationToken;
        }

        # region 测试数据

        void CreatTest()
        {
            var random = new System.Random();
            _unionResourceInfo = new UnionResourceInfo
            {
                ProducePerHour = random.Next(1000, 5000),
                CurrentAmount = random.Next(10000, 50000),
            };
            _unionResourceInfo.ConsumeDetails.Clear();

            for (int i = 0; i < 50; i++)
            {
                int consumeType = random.Next(1, 3); // 1, 2, 3
                int days = 30;
                int msInDay = 24 * 60 * 60 * 1000;
                long timeRange = i * msInDay;
                var detail = new UnionResourceConsumeDetail
                {
                    ConsumeTime = (long)TimeUtils.GetServerTimeMs() - timeRange,
                    UserInfo = new User
                    {
                        IconId = random.Next(Cfg.Head.GetAllData().First().Value.id,
                            Cfg.Head.GetAllData().Last().Value.id),
                        Name = $"玩家{random.Next(1, 1000)}"
                    },
                    ConsumeType = consumeType,
                    ConsumeAmount = random.Next(100, 1000)
                };

                if (consumeType == 1)
                {
                    // detail.TechName = $"科技{random.Next(1, 10)}";
                    //detail.TechLevel = random.Next(1, 10);
                }
                else if (consumeType == 2)
                {
                    detail.SkillTargetName = $"目标玩家{random.Next(1, 100)}";
                    detail.SkillName = $"技能{random.Next(1, 5)}";
                }
                // consumeType == 3 不需要额外字段

                _unionResourceInfo.ConsumeDetails.Add(detail);
            }
        }


        public static LeagueUnionTechnologyAllInfos CreateTestData()
        {
            var allInfos = new LeagueUnionTechnologyAllInfos();

            // 经济类
            var type1 = new LeagueUnionTechnologyTypeInfo { Type = 1 };
            type1.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10001, Progress = 3000 });
            type1.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10002, Progress = 6000 });
            type1.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10003, Progress = 3000 });
            type1.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10004, Progress = 6000 });
            type1.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10005, Progress = 3000 });
            type1.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10010, Progress = 6000 });
            type1.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10011, Progress = 20 });
            type1.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10031, Progress = 3000 });
            type1.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10016, Progress = 20 });
            // 战斗类
            var type2 = new LeagueUnionTechnologyTypeInfo { Type = 2 };
            type2.Infos.Add(new LeagueUnionTechnologyInfo { Id = 10011, Progress = 80 });

            allInfos.Infos.Add(type1);
            allInfos.Infos.Add(type2);

            // 捐献配置
            allInfos.Config = new LeagueUnionTechDonateConfig
            {
                DiamondCostCount = 3,
                NormalCount = 10,
                NormalCountRecoverTime = 1751870559,
            };

            // 当前进行中的科技
            allInfos.CurrentInfo = new LeagueUnionTechnologyOyCureentInfo
            {
                Info = new LeagueUnionTechnologyInfo { Id = 10016, Progress = 0 },
                Starttime = TimeUtils.GetLocalTimeMsLong(),
            };

            // 推荐科技ID
            allInfos.RecommendID = 10010;

            return allInfos;
        }

        public static LeagueUnionTechDonateReward CreateRewardTestData()
        {
            return new LeagueUnionTechDonateReward
            {
                Info = new LeagueUnionTechnologyInfo { Id = 10001, Progress = 50 },
                Crits = 2 // 2倍暴击
            };
        }

        #endregion


        #region 联盟科技

        /// <summary>
        /// 判断是否 经验最大
        /// </summary>
        /// <param name="maxNodeID"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public bool CheckUnionNodeISExpMax(int maxNodeID)
        {
            var nodeData = GetUnionNodeData(maxNodeID);
            if (nodeData == null) return false;
            var data = Cfg.UnionTechnology.GetData(maxNodeID);
            return nodeData.Progress >= data.upgradeEXP;
        }

        public LeagueUnionTechnologyAllInfos GetLeagueUnionTechnologyTreeInfo()
        {
            return allInfos;
        }

        /// <summary>
        /// 是否激活这个节点
        /// </summary>
        /// <param name="dataID"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public bool CheckUnionNodeISActive(int dataID)
        {
            return GetUnionNodeData(dataID) != null;
        }

        /// <summary>
        /// 这个节点是否可以激活 
        /// </summary>
        /// <param name="dataID"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public bool CheckUnionNodeActive(int dataID)
        {
            var data = Cfg.UnionTechnology.GetData(dataID);
            if (data == null) return false;
            var dataUnlcok = data.unlock_id;

            bool result = true;
            for (int i = 0; i < dataUnlcok.Length; i++)
            {
                var unlockData = dataUnlcok[i];
                var unlockCfg = Cfg.UnionTechnology.GetNodeLevel(data.type_large, unlockData[0], unlockData[1] + 1);
                if (unlockCfg == null) return false;
                result &= CheckUnionNodeISActive(unlockCfg.id);
            }

            return result;
        }

        /// <summary>
        /// 获取存储的节点数据
        /// </summary>
        /// <param name="dataID"></param>
        /// <returns></returns>
        public LeagueUnionTechnologyInfo GetUnionNodeData(int dataID)
        {
            if (allInfos == null) return null;
            var data = Cfg.UnionTechnology.GetData(dataID);
            if (data == null) return null;
            var typeInfo = allInfos.Infos.FirstOrDefault(x => x.Type == data.type_large);
            if (typeInfo == null) return null;
            // 在该大类下查找id匹配的节点
            return typeInfo.Infos.FirstOrDefault(x => x.Id == dataID);
        }

        /// <summary>
        /// 是否是推荐 
        /// </summary>
        /// <param name="dataID"></param>
        /// <returns></returns>
        public bool CheckRecommend(int dataID)
        {
            if (allInfos == null) return false;
            if (allInfos.RecommendID <= 0) return false;
            //  var recomData= Cfg.UnionTechnology.GetData(allInfos.RecommendID); 
            //f (recomData == null) return false;
            return allInfos.RecommendID == dataID;
        }

        /// <summary>
        /// 请求 联盟科技研究
        /// </summary>
        /// <param name="dataID"></param>
        public void ReqLeagueUnionTechnologyStudy(int dataID)
        {
            LeagueNetwork.Ins.ReqLeagueUnionTechnologyStudy(dataID);
        }

        /// <summary>
        /// 请求 联盟科技主数据
        /// </summary>
        public void ReqLeagueUnionTechnologyAllInfo()
        {
            LeagueNetwork.Ins.ReqLeagueUnionTechnologyAllInfo();
        }

        /// <summary>
        /// 请求 联盟科技捐献
        /// </summary>
        /// <param name="dataID"></param>
        /// <param name="isVip"></param>
        public void ReqLeagueUTDonate(int dataID, bool isVip)
        {
            LeagueNetwork.Ins.ReqLeagueUTDonate(dataID, isVip);
        }

        /// <summary>
        /// 请求 设置推荐
        /// </summary>
        /// <param name="id"></param>
        /// <param name="isRecommend"></param>
        public void ReqLeagueUnionTechnologyIsRecommendInfo(int id, bool isRecommend)
        {
            LeagueNetwork.Ins.ReqLeagueUnionTechnologyIsRecommendInfo(id, isRecommend);
        }

        public void ReqLeagueUnionTechnologyUpdataDonate()
        {
            LeagueNetwork.Ins.ReqLeagueUnionTechnologyUpdataDonate();
        }

        /// <summary>
        ///  返回 联盟科技研究
        /// </summary>
        /// <param name="imsg"></param>
        private void RspLeagueUnionTechnologyStudy(IMessage imsg)
        {
            RspLeagueUnionTechnologyStudy rspLeague = imsg as RspLeagueUnionTechnologyStudy;
            if (rspLeague == null) return;
            if (allInfos == null) return;
            allInfos.CurrentInfo = rspLeague.CurrentInfo;
            EventManager.Ins.DispatchEvent(UIEventType.RspLeagueUnionTechnologyAllUpdata);
        }

        /// <summary>
        /// 返回 联盟科技主数据
        /// </summary>
        /// <param name="imsg"></param>
        private void RspLeagueUnionTechnologyAllInfos(IMessage imsg)
        {
            var rsp = imsg as RspLeagueUnionTechnologyAllInfos;
            if (rsp == null) return;
            allInfos = rsp.Info;
            EventManager.Ins.DispatchEvent(UIEventType.RspLeagueUnionTechnologyAllUpdata);
        }

        /// <summary>
        /// 返回 联盟科技捐献
        /// </summary>
        /// <param name="imsg"></param>
        private void RspLeagueUTDonate(IMessage imsg)
        {
            var rsp = imsg as RspLeagueUTDonate;
            if (rsp == null || allInfos == null || allInfos.Infos == null) return;

            // 获取科技ID
            int infoId = rsp.Info.Info.Id;

            // 获取科技配置（如有需要）
            var cfgInfo = Cfg.UnionTechnology.GetData(infoId);

            // 获取科技大类索引
            int typeLarge = cfgInfo.type_large;
            if (typeLarge < 0 || typeLarge > allInfos.Infos.Count) return;

            var typeInfo = allInfos.Infos[typeLarge - 1];
            if (typeInfo == null || typeInfo.Infos == null) return;

            // 查找并更新科技节点
            var nodeList = typeInfo.Infos;
            var node = nodeList.FirstOrDefault(x => x.Id == infoId);
            if (node == null)
            {
                nodeList.Add(rsp.Info.Info);
            }
            else
            {
                int nodeIndex = nodeList.IndexOf(node);
                if (nodeIndex >= 0)
                {
                    nodeList[nodeIndex] = rsp.Info.Info;
                }
            }

            allInfos.Config = rsp.Config;
            var crits = rsp.Info.Crits;
            EventManager.Ins.DispatchEvent(UIEventType.RspLeagueUTDonate, crits);
            CloseRecommend(rsp, cfgInfo);
        }

        private void CloseRecommend(RspLeagueUTDonate rsp, UnionTechnology cfgInfo)
        {
            if (rsp.Info.Info.Progress >= cfgInfo.upgradeEXP)
            {
                if (allInfos.RecommendID == rsp.Info.Info.Id)
                {
                    ReqLeagueUnionTechnologyIsRecommendInfo(rsp.Info.Info.Id, false);
                }
            }
        }

        /// <summary>
        ///  返回设置推荐
        /// </summary>
        /// <param name="imsg"></param>
        private void RspLeagueUnionTechnologyIsRecommendInfo(IMessage imsg)
        {
            var rsp = imsg as RspLeagueUnionTechnologyIsRecommendInfo;
            if (rsp == null) return;
            if (allInfos == null) return;
            allInfos.RecommendID = rsp.Id;
            EventManager.Ins.DispatchEvent(UIEventType.RspLeagueUnionTechnologyIsRecommendInfo);
        }


        /// <summary>
        /// 同步 联盟科技结果
        /// </summary>
        /// <param name="imsg"></param>
        private void SyncReqUnionTechnologyStudySucess(IMessage imsg)
        {
            var rsp = imsg as SyncReqUnionTechnologyStudySucess;
            if (rsp == null) return;
            if (allInfos == null) return;

            allInfos.Infos.Clear();
            allInfos.Infos.AddRange(rsp.Infos);
            allInfos.CurrentInfo = rsp.CurrentInfo;
            allInfos.RecommendID = rsp.RecommendID;
            EventManager.Ins.DispatchEvent(UIEventType.RspLeagueUnionTechnologyAllUpdata);
        }

        /// <summary>
        /// 返回 更新捐献数据
        /// </summary>
        /// <param name="imsg"></param>
        private void RspLeagueUnionTechnologyUpdataDonate(IMessage imsg)
        {
            var rsp = imsg as RspLeagueUnionTechnologyUpdataDonate;
            if (rsp == null) return;
            if (allInfos == null) return;
            allInfos.Config = rsp.Config;
            EventManager.Ins.DispatchEvent(UIEventType.RspLeagueUTDonateUpdate);
        }

        private void SyncLeagueUnionTechnologyUpdataDonateCallBack(IMessage imsg)
        {
            var rsp = imsg as SyncLeagueUnionTechnologyUpdataDonate;
            if (rsp == null) return;
            if (allInfos == null) return;
            allInfos.Config = rsp.Config;
            EventManager.Ins.DispatchEvent(UIEventType.RspLeagueUTDonateUpdate);
        }

        /// <summary>
        /// 获取当前节点 的 Level
        /// </summary>
        /// <returns>返回 level 最低1 </returns>
        public UnionTechnology GetNodeLevel(UnionTechnology data)
        {
            var nodelist = Cfg.UnionTechnology.GetNodeDict(data.type_large, data.row_idx, data.node);
            if (nodelist == null) return null;
            nodelist.Sort((a, b) => a.level.CompareTo(b.level));
            int index = 0;
            for (int i = 0; i < nodelist.Count; i++)
            {
                var id = nodelist[i].id;
                if (GetUnionNodeData(id) != null)
                {
                    index = i;
                }
            }

            return nodelist[index];
        }

        public LeagueUnionTechnologyOyCureentInfo GetCurrentInfo()
        {
            return allInfos?.CurrentInfo;
        }

        public bool CheckISCurrent(int dataID)
        {
            var current = GetCurrentInfo();
            if (current == null) return false;
            if (current.Info.Id == 0) return false;
            return current.Info.Id == dataID;
        }

        /// <summary>
        /// 获取推荐的 row
        /// </summary>
        /// <param name="large"></param>
        /// <returns></returns>
        public int GetCommredRow(int large)
        {
            if (allInfos == null) return 0;
            if (allInfos.RecommendID != 0)
            {
                var data = Cfg.UnionTechnology.GetData(allInfos.RecommendID);
                if (data == null) return 0;
                if (data.type_large == large)
                {
                    return data.row_idx;
                }
            }

            var datas = Cfg.UnionTechnology.GetLargeDict(large);
            if (datas == null) return 0;
            foreach (var rowKey in datas.Keys)
            {
                if (datas.TryGetValue(rowKey, out var rowDict))
                {
                    var nodeList = rowDict.Values.ToList();
                    for (int i = 0; i < nodeList.Count; i++)
                    {
                        var levels = nodeList[i];
                        if (levels.Count <= 0) continue;
                        var levelnode = GetNodeLevel(levels.FirstOrDefault());
                        if (CheckUnionNodeISExpMax(levelnode.id) &&
                            !CheckisMax(levelnode.id))
                        {
                            return rowKey;
                        }
                    }
                }
            }

            return 0;
        }

        /// <summary>
        /// 是否有修改权限
        /// </summary>
        /// <returns></returns>
        public bool CheckChangeCommon()
        {
            return CheckChangeNameLevel(Cfg.UnionTechnology.RandLevel);
        }

        /// <summary>
        /// 是否在研究
        /// </summary>
        /// <returns>true == 正在研究中</returns>
        public bool IsCurrentStudy()
        {
            var cureentInfo = GetCurrentInfo();
            if (cureentInfo?.Info == null) return false;
            if (cureentInfo?.Info?.Id == 0) return false;
            return true;
        }
        /// <summary>
        /// 是否是满级
        /// </summary>
        /// <param name="dataID"></param>
        /// <returns></returns>
        public bool CheckisMax(int dataID)
        {
            var data = GetUnionNodeData(dataID);
            if (data != null)
            {
                return data.IsMax;
            }

            return false;
        }


        /// <summary>
        /// 获取推荐 
        /// </summary>
        /// <param name="large"></param>
        /// <returns></returns>
        public UnionTechnology GetCommredUnion()
        {
            if (allInfos != null)
            {
                if (allInfos.RecommendID != 0)
                {
                    return Cfg.UnionTechnology.GetData(allInfos.RecommendID);
                }
            }

            return null;
        }
        
        /// <summary>
        /// 获取升级需要的条件
        /// </summary>
        /// <param name="data"></param>
        /// <returns>
        /// <类型，id ，是否通过>
        /// </returns>
        public List<(int,int,bool)> GetTitleAndValueList(UnionTechnology data)
        {
            List<(int, int, bool)> result = new();
            if (data == null) return null;
            var unlockData = Cfg.UnionTechnology.GetUnLock(data.id);
            foreach (var unlock in unlockData)
            {
                var id= unlock.Item1+ unlock.Item2;
                result.Add((0,id,GetUnionNodeData(id)!=null));
            }

            var costConfig = data.cost;
            var count= Cfg.UnionTb.GetData(Cfg.UnionTb.DonateCount).value2 - GetLeagueUnionTechnologyTreeInfo().Config.NormalCount;
           // result.Add(2,costConfig[0],BagMgr.Ins.GetItemCount(Cfg.UnionTechnology.itemID)>);
            return result;
        }
        #endregion
        
    }
}