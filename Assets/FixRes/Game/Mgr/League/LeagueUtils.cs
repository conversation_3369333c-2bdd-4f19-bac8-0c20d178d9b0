using Core;
using GameMain;
using GameUI;
using System.Collections;
using System.Collections.Generic;
using Packet;
using UIFramework;
using UnityEngine;

namespace Game
{
    public static class LeagueUtils
    {
        static List<int> mTmpList = new List<int>();
        public class PermitType
        {
            public const int Manager = 0;               //管理           
            public const int Command = 1;               //指挥
            public const int Build = 2;                 //建设            
            public const int City = 3;                  //城池
            public const int Schedule = 4;              //日程
        }

        public class OptBtnType
        {
            public const int Setting = 0;               //设置
            public const int LeagueList = 1;            //联盟列表
            public const int ApplyList = 2;             //申请列表
            public const int ExitLeague = 3;            //退出联盟 or 解散联盟
            public const int VoteNotice = 4;            //投票公告
            public const int MoveCity = 5;              //迁城邀请
            public const int PermitSetting = 6;         //权限设置
        }

        public class KickReasonType
        {
            public const int LongTimeOffline = 1;       //长期离线
            public const int Nuisance = 2;              //骚扰行为
            public const int PowerLow = 3;              //战力太低
            public const int ContributionLow = 4;          //贡献过低
            public const int SpyAction = 5;             //间谍行为
            public const int IllegalInformation = 6;       //非法资讯
            public const int OtherReason = 7;           //其它
            public const int Count = 7;                 //总数
        }

        static Dictionary<int, int> KickReasonToLangId = new Dictionary<int, int>
        {
            { KickReasonType.LongTimeOffline, LanguageCode.LongTimeOffline },
            { KickReasonType.Nuisance, LanguageCode.Nuisance },
            { KickReasonType.PowerLow, LanguageCode.PowerLow },
            { KickReasonType.ContributionLow, LanguageCode.ContributionLow },
            { KickReasonType.SpyAction, LanguageCode.SpyAction },
            { KickReasonType.IllegalInformation, LanguageCode.IllegalInformation },
            { KickReasonType.OtherReason, LanguageCode.OtherReason },
        };

        public static int GetLangIdByReasonType(int reasonType)
        {
            if (KickReasonToLangId.ContainsKey(reasonType))
                return KickReasonToLangId[reasonType];
            return 0;
        }

        static Dictionary<int, int> mPermitTypeToLangIdDic = new Dictionary<int, int>
        {
            { PermitType.Manager, LanguageCode.Manager},
            { PermitType.Build, LanguageCode.Build},
            { PermitType.Command, LanguageCode.Command},
            { PermitType.Schedule, LanguageCode.Schedule},
            { PermitType.City, LanguageCode.City},
        };

        public static int GetLangIdByPType(int type)
        {
            if (mPermitTypeToLangIdDic.ContainsKey(type))
                return mPermitTypeToLangIdDic[type];
            return 0;
        }

        static List<BtnUIInfo> mLeagueOptBtnList = new List<BtnUIInfo>
        {
            new BtnUIInfo(LanguageCode.Setting, "img_lm_icon_glyq_01shezhi"),
            new BtnUIInfo(LanguageCode.LeagueList, "img_lm_icon_glyq_02liebiao"),
            new BtnUIInfo(LanguageCode.ApplyList, "img_lm_icon_glyq_03shenqing", 602),
            new BtnUIInfo(LanguageCode.ExitLeague, "img_lm_icon_glyq_07jiesan"),
            new BtnUIInfo(LanguageCode.VoteNotice, "img_lm_icon_glyq_05toupiao"),
            new BtnUIInfo(LanguageCode.MoveCityInvite, "img_lm_icon_glyq_04qiancheng"),
            new BtnUIInfo(LanguageCode.PermitSetting, "img_lm_icon_glyq_06quanxian"),
        };
        public static BtnUIInfo GetOptBtnInfo(int index)
        {
            if (index >= mLeagueOptBtnList.Count)
                return null;

            return mLeagueOptBtnList[index];
        }

        static List<int> mTmpOptBtnList = new List<int>();

        public static List<int> GetOptBtnList(bool isLeader)
        {
            mTmpOptBtnList.Clear();

            for (int i = 0; i < mLeagueOptBtnList.Count; i++)
            {
                if (isLeader || mLeagueOptBtnList[i].langId != LanguageCode.MoveCity)
                    mTmpOptBtnList.Add(i);
            }

            return mTmpOptBtnList;
        }

        public static void DoOnClickOptBtn(int index)
        {
            switch (index)
            {
                case OptBtnType.Setting:        //设置
                    {
                        var cfg = Cfg.Permit.GetData(LeagueMgr.Ins.PosId);
                        if (cfg != null)
                        {
                            if (LeagueMgr.Ins.GetPermitState(1, cfg.permit_level))
                                UIMgr.Ins.Open(UIDefine.LeagueSettingPanel);
                            else
                                TipsMgr.Ins.ShowTips(LanguageCode.RankLevelLimited);
                        }


                    }
                    break;
                case OptBtnType.LeagueList:        //联盟列表
                    {
                        UIMgr.Ins.Open(UIDefine.LeagueListPanel);
                    }
                    break;
                case OptBtnType.ApplyList:        //申请列表
                    {
                        UIMgr.Ins.Open(UIDefine.LeagueApplyListPanel);
                    }
                    break;
                case OptBtnType.ExitLeague:        //退出联盟
                    {
                        if (LeagueMgr.Ins.IsLeader())
                        {
                            MsgBoxMgr.Ins.ShowMsgDefault(Localization.ToFormat(LanguageCode.IsDisbandLeague), () =>
                            {
                                LeagueMgr.Ins.ReqLeagueDisband();
                            });
                        }
                        else
                        {
                            MsgBoxMgr.Ins.ShowMsgDefault(Localization.ToFormat(LanguageCode.YouAreExittingLeague), () =>
                            {
                                LeagueMgr.Ins.ReqExitLeague();
                            });
                        }
                    }
                    break;
                case OptBtnType.VoteNotice:        //投票公告
                    {
                        TipsMgr.Ins.ShowTips(LanguageCode.TipsFunctionLocked);
                    }
                    break;
                case OptBtnType.MoveCity:        //迁城邀请
                    {
                        TipsMgr.Ins.ShowTips(LanguageCode.TipsFunctionLocked);
                    }
                    break;
                case OptBtnType.PermitSetting:        //权限设置
                    {
                        UIMgr.Ins.Open(UIDefine.LeaguePermitSettingPanel);
                    }
                    break;
            }
        }

        public static void DoOnClickMainBtn(int index)
        {
            switch (index)
            {
                case GameDefine.SysType.LeagueHelp:
                    UIMgr.Ins.Open(UIDefine.LeagueHelpPanel);
                    break;
                case GameDefine.SysType.LeagueTech:
                    UIMgr.Ins.Open(UIDefine.LeagueTechnologyPanel);
                    break;
                case GameDefine.SysType.LeagueGift:
                    UIMgr.Ins.Open(UIDefine.LeagueGiftPanel);
                    break;
                case GameDefine.SysType.LeagueShop:
                    TipsMgr.Ins.ShowTips(LanguageCode.TipsFunctionLocked);
                    break;
                case GameDefine.SysType.LeagueLand:
                    UIMgr.Ins.Open(UIDefine.WorldCitySiegeBattleDetailPanel);
                    break;
                case GameDefine.SysType.LeagueRank:
                    TipsMgr.Ins.ShowTips(LanguageCode.TipsFunctionLocked);
                    break;
                case GameDefine.SysType.LeagueLog:
                    TipsMgr.Ins.ShowTips(LanguageCode.TipsFunctionLocked);
                    break;
                case GameDefine.SysType.LeagueIntelligence:
                    //TipsMgr.Ins.ShowTips(LanguageCode.TipsFunctionLocked);
                    UIMgr.Ins.Open(UIDefine.LeagueInfoPanel);
                    break;
            }
        }

        static Dictionary<int, BtnUIInfo> mMemberOperaBtnDic = new Dictionary<int, BtnUIInfo>
        {
            {GameDefine.LeagueMemOptBtnType.Detail, new BtnUIInfo(LanguageCode.Detail, "ty_btn_xq") },
            {GameDefine.LeagueMemOptBtnType.Chat, new BtnUIInfo(LanguageCode.Chat, "ty_btn_sj") },
            {GameDefine.LeagueMemOptBtnType.PassLeader, new BtnUIInfo(LanguageCode.PassLeader, "ty_btn_js") },
            {GameDefine.LeagueMemOptBtnType.ChgClass, new BtnUIInfo(LanguageCode.ChgClass, "ty_btn_ljwc") },
            {GameDefine.LeagueMemOptBtnType.RemovePos, new BtnUIInfo(LanguageCode.RemovePos, "ty_btn_sc") },
            {GameDefine.LeagueMemOptBtnType.Kick, new BtnUIInfo(LanguageCode.KickOutLeague, "ty_btn_sc") },
        };

        public static BtnUIInfo GetMemberOperaBtn(int id)
        {
            if (mMemberOperaBtnDic.ContainsKey(id))
                return mMemberOperaBtnDic[id];
            return null;
        }

        public static List<int> GetOperaBtnListByPos(int posId, ulong guid, bool isFromMiddle)
        {
            mTmpList.Clear();

            mTmpList.Add(GameDefine.LeagueMemOptBtnType.Detail);
            mTmpList.Add(GameDefine.LeagueMemOptBtnType.Chat);
            var info = LeagueMgr.Ins.GetMemberInfo(guid);
            int targetPosId = info.PosId;

            if (posId == 1)
            {
                mTmpList.Add(GameDefine.LeagueMemOptBtnType.PassLeader);

                if (isFromMiddle && targetPosId > 5)
                    mTmpList.Add(GameDefine.LeagueMemOptBtnType.RemovePos);
                else
                    mTmpList.Add(GameDefine.LeagueMemOptBtnType.ChgClass);

                mTmpList.Add(GameDefine.LeagueMemOptBtnType.Kick);
            }
            else
            {
                var cfg = Cfg.Permit.GetData(posId);
                var tCfg = Cfg.Permit.GetData(targetPosId);
                if (cfg != null && tCfg != null)
                {
                    //DLogger.Log("GetOperaBtnListByPos {0} {1} {2}", cfg.permit_level, tCfg.permit_level, LeagueMgr.Ins.GetPermitState(1, cfg.permit_level));
                    if (cfg.permit_level > tCfg.permit_level && LeagueMgr.Ins.GetPermitState(1, cfg.permit_level))
                    {
                        mTmpList.Add(GameDefine.LeagueMemOptBtnType.ChgClass);
                        mTmpList.Add(GameDefine.LeagueMemOptBtnType.Kick);
                    }
                }
            }

            return mTmpList;
        }

        public static void DoOnClickMemOptBtn(int index, ulong guid)
        {
            switch (index)
            {
                case GameDefine.LeagueMemOptBtnType.Detail:
                    UIMgr.Ins.Open(UIDefine.UserInfoPanel, guid, true);
                    break;
                case GameDefine.LeagueMemOptBtnType.Chat:
                    TipsMgr.Ins.ShowTips(LanguageCode.TipsFunctionLocked);
                    break;
                case GameDefine.LeagueMemOptBtnType.PassLeader:
                    var member = LeagueMgr.Ins.GetMemberInfo(guid);
                    if (member == null) return;
                    MsgBoxMgr.Ins.ShowMsg(Localization.ToFormat(LanguageCode.PassLeader), Localization.ToFormat(LanguageCode.IsPassLeaderTo, member.UserInfo.Name),
                        Localization.GetValue(LocalLanCode.Confirm), "", () =>
                    {
                        UIMgr.Ins.ClosePanel(UIDefine.LeagueOperaFloatPanel);
                        LeagueMgr.Ins.ReqLeaguePassLeader(guid);
                    });
                    break;
                case GameDefine.LeagueMemOptBtnType.ChgClass:
                    UIMgr.Ins.ClosePanel(UIDefine.LeagueOperaFloatPanel);
                    UIMgr.Ins.Open(UIDefine.LeagueChgPosPanel, guid);
                    break;
                case GameDefine.LeagueMemOptBtnType.RemovePos:
                    UIMgr.Ins.ClosePanel(UIDefine.LeagueOperaFloatPanel);
                    LeagueMgr.Ins.ReqLeagueChgPos(guid, 2);
                    break;
                case GameDefine.LeagueMemOptBtnType.Kick:
                    UIMgr.Ins.Open(UIDefine.LeagueKickMemberPanel, guid);
                    break;
            }
        }

        public static int GetPermitLv(int posId)
        {
            var cfg = Cfg.Permit.GetData(posId);
            if (cfg == null) return 1;

            return cfg.permit_level;
        }

        public enum ConsumeType
        {
            ResearchAllianceTech = 1,   // 研究联盟科技
            UseAllianceSkill = 2,       // 使用联盟技能
            BuildAllianceBuilding = 3   // 建造联盟建筑
        }

        public static string GetUnionResourceConsumeDes(UnionResourceConsumeDetail data, int researchLanId = LanguageCode.__CODE__1000566, int useSkillLanId = LanguageCode.__CODE__1000567, int buildLanId = 0)
        {
            string result = "";
            ConsumeType type = (ConsumeType)data.ConsumeType;
            switch (type)
            {
                case ConsumeType.ResearchAllianceTech:
                    var unionTechnology = Cfg.UnionTechnology.GetData(data.TechId);
                    result = Localization.ToFormat(researchLanId, data.UserInfo.Name, Localization.GetValue(unionTechnology.name_id),
                        unionTechnology.level);
                    break;
                case ConsumeType.UseAllianceSkill:
                    result = Localization.ToFormat(useSkillLanId, data.UserInfo.Name, data.SkillName,
                        data.SkillTargetName);
                    break;
            }

            return result;
        }
    }

    public class LeagueBaseInfo
    {
        public LeagueBaseInfo() { }
        public LeagueBaseInfo(Packet.LeagueBaseInfo netLeagueBaseInfo)
        {
            Clone(netLeagueBaseInfo);
        }

        public void Clone(Packet.LeagueBaseInfo netLeagueBaseInfo)
        {
            LeagueId = (long)netLeagueBaseInfo.LeagueId;
            LeagueName = netLeagueBaseInfo.LeagueName;
            LeagueShortName = netLeagueBaseInfo.LeagueShortName;
            FlagBgId = netLeagueBaseInfo.FlagBgId;
            FlagBgColorId = netLeagueBaseInfo.FlagBgColorId;
            FlagIconId = netLeagueBaseInfo.FlagIconId;
            if (netLeagueBaseInfo.Leader != null)
            {
                LeaderInfo = new UserInfo(netLeagueBaseInfo.Leader);
            }
        }

        public long LeagueId { get; private set; } //联盟ID
        public string LeagueName { get; private set; } //联盟名称
        public string LeagueShortName { get; private set; } //联盟简称
        public UserInfo LeaderInfo { get; private set; } //盟主信息

        public int FlagBgId { get; private set; }//联盟标记背景id
        public int FlagBgColorId { get; private set; }//联盟标记背景颜色id
        public int FlagIconId { get; private set; }//联盟标记id
        public string LeagueFullName => $"[{LeagueShortName}]{LeagueName}"; //联盟全称
    }
}

