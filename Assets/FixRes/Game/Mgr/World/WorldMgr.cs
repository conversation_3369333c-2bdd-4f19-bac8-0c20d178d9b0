using Core;
using Cysharp.Threading.Tasks;
using Game.Network;
using Packet;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using Game;
using Game.Framework;
using UIFramework;
using UnityEngine;
using UnityEngine.Pool;
using Debug = UnityEngine.Debug;

namespace GameMain
{
    public class WorldMgr : LazySingleton<WorldMgr>, ISubMgr, EventReceiver
    {
        private GridDataMgr _gridDataMgr = new();
        private WorldMapBattleMgr _battleMgr = new();

        public Dictionary<ulong, TroopInfo> MapTroopInfos => _mapTroopInfos;
        Dictionary<ulong, TroopInfo> _mapTroopInfos = new Dictionary<ulong, TroopInfo>();

        public List<TroopInfo> RemoveInofs => _removeInfos;
        List<TroopInfo> _removeInfos = new List<TroopInfo>();

        private short[] _mapDatas;

        public WorldMapBehaviour WorldMapBehaviour { get; private set; }

        public ulong SelectedTroopId { get; set; }
        public Vector2Int MyCityPos { get; private set; }

        List<int> _openStateList = new();

        WorldMapLocalPathfinder _worldMapPathFinder = new();

        int lv;

        //普通野怪解锁等级
        public int MonsterUnlockLv { get; set; }

        //集结野怪解锁等级
        public int AssembleMonsterUnlockLv { get; set; }

        public void RegisterNetProto()
        {
        }

        public void UnregisterNetProto()
        {
        }

        public void OnEnterGame()
        {
            WorldNetWorkMgr.Ins.SendReqBigMapSub();
            _battleMgr.OnEnterGame();
            EventManager.Ins.RegisterEvent(UIEventType.OnSyncMainCityChangePos, this, OnSyncMainCityChangePos);
        }

        public void OnExitGame()
        {
            _mapTroopInfos?.Clear();
            _removeInfos?.Clear();
            _gridDataMgr?.Clear();

            EventManager.Ins.RemoveEvent(UIEventType.OnSyncMainCityChangePos, this);
        }

        public void AfterEnterGame()
        {
            if (WorldMapBehaviour)
            {
                WorldMapBehaviour.ShowRangeDirty = true;
            }
        }

        public async UniTask Init()
        {
            //string walkableDataPath = "Configs/WorldMapData/WorldMapWalkableData.bytes";
            string mapDataPath = "Configs/WorldMapData/WorldMapData.bytes";
            string attachGridsPath = "Configs/WorldMapData/WorldMapElementAttachGrids.txt";
            //TextAsset taWalkable = await GameResMgr.Ins.LoadAssetAsync<TextAsset>(walkableDataPath);
            TextAsset taMap = await GameResMgr.Ins.LoadAssetAsync<TextAsset>(mapDataPath);
            TextAsset taAttachGrids = await GameResMgr.Ins.LoadAssetAsync<TextAsset>(attachGridsPath);
            _gridDataMgr.SetElementAttachGrid(taAttachGrids.text);
            //_worldMapPathFinder.Init(taWalkable.bytes);

            InitMapData(taMap.bytes);
            _worldMapPathFinder.Init(IsWalkableByClientPos);
            //GameResMgr.Ins.UnloadAsset(walkableDataPath);
            GameResMgr.Ins.UnloadAsset(mapDataPath);
            GameResMgr.Ins.UnloadAsset(attachGridsPath);
        }

        public short GetClientTileIdByServerPos(Vector2Int serverPos)
        {
            return GetClientTileIdByClientPos(serverPos - Vector2Int.one);
        }

        public short GetClientTileId(int index)
        {
            if (index >= 0 && index < _mapDatas.Length)
                return _mapDatas[index];
            else
                return 0;
        }

        public short GetClientTileIdByClientPos(Vector2Int clientPos)
        {
            int index = clientPos.x + WorldMapUtils.MAP_WIDTH * clientPos.y;
            return GetClientTileId(index);
        }

        private void InitMapData(byte[] mapData)
        {
            _mapDatas = new short[mapData.Length / sizeof(short)];
            Buffer.BlockCopy(mapData, 0, _mapDatas, 0, mapData.Length);
        }

        public bool IsWalkableByClientPos(Vector2Int clientPos)
        {
            short tileId = GetClientTileIdByClientPos(clientPos);

            if (tileId <= 0)
            {
                Debug.LogError("Data index out of range!");
                return false;
            }

            return WorldMapUtils.ClientTileIdToWalkable(tileId);
        }

        public void OnEnterWorldScene(WorldMapBehaviour worldMapBehaviour)
        {
            WorldMapBehaviour = worldMapBehaviour;

            EventManager.Ins.RegisterEvent(UIEventType.UnSelectWorldMapTroop, this, UnSelectWorldMapTroop);
        }

        public void UnInit()
        {
            WorldMapBehaviour = null;
            EventManager.Ins.RemoveEvent(UIEventType.UnSelectWorldMapTroop, this);

        }

        public int[] GetAttachGridIndexs(int index) => _gridDataMgr.GetAttachGridIndexs(index);

        public void TryReqGridData(HashSet<Vector2Int> gridIndexList)
        {
            _gridDataMgr?.TryReqGridData(gridIndexList);
        }

        public void OnEnterWorldMap(RspBigMapEnter msg)
        {
            MyCityPos = new Vector2Int((int)msg.Pos.X, (int)msg.Pos.Y);
            lv = (int)msg.Lv;
            MonsterUnlockLv = (int)msg.MonsterUnlockLv;
            AssembleMonsterUnlockLv = (int)msg.AssembleMonsterUnlockLv;
            foreach (var state in msg.StateList)
            {
                _openStateList.Add((int)state);
            }
        }

        public void OnRspBigMapSub()
        {
            UserMgr.Ins.OnOneSystemRecvData(ToString());
        }

        public void OnGridDataUpdate(List<GridData> gridDataList, RspBigMapInfo msg)
        {
            _gridDataMgr.OnGridDataUpdate(gridDataList, msg);
            List<TroopInfo> troopInfos = new List<TroopInfo>();
            foreach (var troopInfo in msg.Troops)
            {
                var info = new TroopInfo();
                info.UpdateData(troopInfo);
                troopInfos.Add(info);
            }

            UpdateMapTroop(troopInfos);
            EventManager.Ins.DispatchEvent(UIEventType.OnGridDataUpdate);
        }

        public void UpdateMapTroop(List<TroopInfo> troopInfo)
        {
            List<TroopInfo> myTroopInfos = new List<TroopInfo>();
            for (int i = 0; i < troopInfo.Count; i++)
            {
                var info = troopInfo[i];
                if (info.state == TroopState.IDLE)
                {
                    _removeInfos.Add(info);
                    _mapTroopInfos.Remove(info.TroopId);
                }
                else if (info.state == TroopState.MINE)
                {
                    _removeInfos.Add(info);
                    _mapTroopInfos[info.TroopId] = info;
                }
                else
                {
                    _mapTroopInfos[info.TroopId] = info;
                }

                if (info.roleId == UserMgr.Ins.Self.role_id)
                {
                    myTroopInfos.Add(info);
                }
            }

            if (myTroopInfos.Count > 0)
            {
                TroopMgr.Ins.UpdateTroopInfo(myTroopInfos);
            }

            EventManager.Ins.DispatchEvent(UIEventType.OnBigMapTroopInfoUpdate);
        }

        public TroopInfo GetTroopInfo(ulong troopId)
        {
            if (_mapTroopInfos.TryGetValue(troopId, out var troopInfo))
            {
                return troopInfo;
            }

            return null;
        }

        public TroopInfo GetTroopInfoByPos(Vector2Int pos)
        {
            foreach (var kv in _mapTroopInfos)
            {
                if (kv.Value.pos == pos)
                {
                    return kv.Value;
                }
            }

            return null;
        }

        public GridData GetGridData(Vector2Int pos)
        {
            if (WorldMapUtils.IsValidatePos(pos))
            {
                return GetGridData(WorldMapUtils.GetGridIndex(pos));
            }

            return null;
        }

        public GridData GetGridData(int index) => _gridDataMgr.GetGridData(index);

        public BigMapMainCity GetMainCityData(int index) => _gridDataMgr.GetMainCityData(index);

        public MineInfo GetMineInfo(int index) => _gridDataMgr.GetMineInfo(index);

        public MonsterInfo GetMonsterInfo(int index) => _gridDataMgr.GetMonsterInfo(index);
        public RadarEventWorldInfo GetRadarEventWorldInfo(int index) => _gridDataMgr.GetRadarEventWorldInfo(index);
        public BigMapDispatchInfo GetDispatchInfo(int index) => _gridDataMgr.GetDispatchInfo(index);

        private void OnSyncMainCityChangePos(object imsg)
        {
            var msg = (SyncMainCityChangePos)imsg;
            _gridDataMgr?.OnSyncMainCityChangePos(msg);
            if (UserMgr.Ins.IsMyself(msg.MainCity.Info.RoleId))
            {
                MyCityPos = new Vector2Int((int)msg.MainCity.Pos.X, (int)msg.MainCity.Pos.Y);
                lv = (int)msg.MainCity.Info.Lv;
            }
        }

        void UnSelectWorldMapTroop()
        {
            SelectedTroopId = 0;
            WorldMapBehaviour?.UnSelectTroop();
        }

        public bool CheckStateIsOpened(int id)
        {
            var cityAreaCfg = Cfg.CityArea.GetData(id);
            return _openStateList.Contains(cityAreaCfg.belong_state);
        }

        public async UniTask<bool> FindPath(Vector2Int start, Vector2Int goal)
        {
            start = WorldMapUtils.PosServerToClient(start);
            goal = WorldMapUtils.PosServerToClient(goal);
            Stopwatch sw = Stopwatch.StartNew();
            DLogger.Log($"开始寻路:{start}->{goal}");
            bool isFound = await _worldMapPathFinder.FindPath(start, goal);
            if (isFound)
            {
                DebugLogPos(WorldMapUtils.PosClientToServer(start), WorldMapUtils.PosClientToServer(goal), GetPathResultServer());
            }
            else
            {
                TipsMgr.Ins.ShowTips(Localization.ToFormat(LanguageCode.__CODE__1000192, start, goal));
            }

            sw.Stop();
            DLogger.Log($"寻路结束，耗时:{sw.Elapsed}");
            return isFound;
        }

        void DebugLogPos(Vector2Int start, Vector2Int goal, List<Vector2Int> result)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"start:{start} goal:{goal}");
            for (int i = 0; i < result.Count; i++)
            {
                sb.Append(result[i]);
            }

            DLogger.Log(sb.ToString());
        }

        public async UniTask<bool> JoinLeagueTeam(int troopIndex, ulong teamId, Pos founderPos, List<KeyValuePair<int, int>> selectSoldier)
        {
            if (IsPathFinding())
            {
                await UniTask.WaitUntil(() => !IsPathFinding());
            }

            Vector2Int goal = Vector2Int.zero;
            goal.Set((int)founderPos.X, (int)founderPos.Y);

            Vector2Int start;
            var troopInfo = TroopMgr.Ins.GetTroopInfo(troopIndex);
            //如果troopinfo不为空，说明是野外部队，如果为空，说明是从城里出发
            if (troopInfo != null)
            {
                if (troopInfo.state == TroopState.GOBACK)
                {
                    return false;
                }

                start = troopInfo.GetCurPos();
            }
            else
            {
                start = MyCityPos;
            }

            bool isFound = await FindPath(start, goal);
            if (isFound)
            {
                LeagueTeamMgr.Ins.ReqJoinLeagueTeam(troopIndex, teamId, GetPathResultServer(), selectSoldier);
            }

            return isFound;
        }

        /// <summary>
        /// 执行部队行动（移动、攻击、采集等）
        /// </summary>
        /// <param name="troopIndex">部队索引</param>
        /// <param name="targetGrid">目标格子数据</param>
        /// <param name="selectSoldier">选择的士兵</param>
        /// <param name="autoReturn">是否自动返回</param>
        /// <param name="isTeam">是否为队伍行动</param>
        /// <returns>是否执行成功</returns>
        public async UniTask<bool> DoTroopAction(int troopIndex, GridData targetGrid, List<KeyValuePair<int, int>> selectSoldier = null, bool autoReturn = true, bool isTeam = false)
        {
            // 等待寻路完成
            if (IsPathFinding())
            {
                await UniTask.WaitUntil(() => !IsPathFinding());
            }

            // 确定起始位置
            Vector2Int startPos = GetTroopStartPosition(troopIndex, out TroopInfo troopInfo);
            if (startPos == Vector2Int.zero)
            {
                return false;
            }

            // 检查是否已在目标位置
            if (startPos == targetGrid.pos)
            {
                return false;
            }

            // 计算目标位置（考虑战斗点）
            Vector2Int goalPos = CalculateGoalPosition(startPos, targetGrid);

            // 执行寻路
            bool pathFound = await FindPath(startPos, goalPos);
            if (!pathFound)
            {
                return false;
            }

            // 处理部队采集状态
            if (!await HandleTroopMiningState(troopIndex, troopInfo, targetGrid))
            {
                return false;
            }

            // 刷新部队信息和移动状态
            troopInfo = TroopMgr.Ins.GetTroopInfo(troopIndex);
            bool isMoving = troopInfo?.state == TroopState.MOVE;

            // 根据目标类型执行相应行动
            return ExecuteTroopAction(troopIndex, targetGrid, selectSoldier, autoReturn, isTeam, startPos, goalPos, isMoving);
        }

        /// <summary>
        /// 获取部队起始位置
        /// </summary>
        private Vector2Int GetTroopStartPosition(int troopIndex, out TroopInfo troopInfo)
        {
            troopInfo = TroopMgr.Ins.GetTroopInfo(troopIndex);

            // 如果部队信息不为空，说明是野外部队
            if (troopInfo != null)
            {
                // 如果部队正在返回，不能执行新行动
                if (troopInfo.state == TroopState.GOBACK)
                {
                    return Vector2Int.zero;
                }
                return troopInfo.GetCurPos();
            }

            // 部队信息为空，说明是从主城出发
            return MyCityPos;
        }

        /// <summary>
        /// 计算目标位置（考虑是否需要战斗点）
        /// </summary>
        private Vector2Int CalculateGoalPosition(Vector2Int startPos, GridData targetGrid)
        {
            Vector2Int goalPos = targetGrid.pos;
            if (CheckNeedBattle(targetGrid))
            {
                goalPos = WorldMapUtils.GetTargetBattlePoint(startPos, targetGrid.pos);
            }
            return goalPos;
        }

        /// <summary>
        /// 处理部队采集状态
        /// </summary>
        private async UniTask<bool> HandleTroopMiningState(int troopIndex, TroopInfo troopInfo, GridData targetGrid)
        {
            // 如果部队正在采集，需要先取消采集
            if (troopInfo?.state == TroopState.MINE)
            {
                WorldNetWorkMgr.Ins.SendReqMineCancel(troopIndex);

                // 如果目的地是主城，取消协议后服务器会自动推送部队返回主城
                if (targetGrid.pos == MyCityPos)
                {
                    return false;
                }

                // 等待部队状态不为采集中
                await UniTask.WaitUntil(() =>
                {
                    var currentTroopInfo = TroopMgr.Ins.GetTroopInfo(troopIndex);
                    return currentTroopInfo?.state != TroopState.MINE;
                });
            }

            return true;
        }

        /// <summary>
        /// 根据目标类型执行相应的部队行动
        /// </summary>
        private bool ExecuteTroopAction(int troopIndex, GridData targetGrid, List<KeyValuePair<int, int>> selectSoldier,
            bool autoReturn, bool isTeam, Vector2Int startPos, Vector2Int goalPos, bool isMoving)
        {
            // 空地或自己的主城 - 直接移动
            if (IsMovableTarget(targetGrid))
            {
                return HandleTroopMovement(troopIndex, targetGrid, selectSoldier);
            }

            // 敌方城市 - 攻城或协防
            if (targetGrid.CityData != null)
            {
                return HandleCityAction(troopIndex, targetGrid, selectSoldier, isTeam, startPos, goalPos, isMoving);
            }

            // 采集点 - 采集资源
            if (targetGrid.MineInfo != null)
            {
                return HandleMineAction(troopIndex, targetGrid, selectSoldier, isMoving);
            }

            // 野怪 - 攻击野怪
            if (targetGrid.MonsterInfo != null)
            {
                return HandleMonsterAction(troopIndex, targetGrid, selectSoldier, autoReturn, isTeam, startPos, goalPos, isMoving);
            }

            // 要塞城市 - 攻击或协防要塞
            if (targetGrid.CityAreaId != 0)
            {
                return HandleFortressAction(troopIndex, targetGrid, selectSoldier, isTeam, isMoving);
            }

            return true;
        }

        /// <summary>
        /// 检查是否为可移动目标（空地或自己的主城）
        /// </summary>
        private bool IsMovableTarget(GridData targetGrid)
        {
            return targetGrid.IsOpenSpace() ||
                   (targetGrid.CityData != null && UserMgr.Ins.IsMyself(targetGrid.CityData.roleId));
        }

        /// <summary>
        /// 处理部队移动
        /// </summary>
        private bool HandleTroopMovement(int troopIndex, GridData targetGrid, List<KeyValuePair<int, int>> selectSoldier)
        {
            var troopInfo = TroopMgr.Ins.GetTroopInfo(troopIndex);
            string moveType = GetMoveType(troopInfo);

            WorldNetWorkMgr.Ins.SendReqTroopMoveTo(troopIndex, targetGrid.pos, moveType, GetPathResultServer(), selectSoldier);
            return true;
        }

        /// <summary>
        /// 获取移动类型
        /// </summary>
        private string GetMoveType(TroopInfo troopInfo)
        {
            if (troopInfo == null)
            {
                return MoveType.MOVE;
            }

            // 根据部队状态确定移动类型
            switch (troopInfo.state)
            {
                case TroopState.STANDBY:
                case TroopState.HELP_DEF:
                case TroopState.UNION_CITY_DEF:
                case TroopState.TEAM:
                    return MoveType.MOVE;
                default:
                    return MoveType.MOVE_CHANGE;
            }
        }

        /// <summary>
        /// 处理城市相关行动（攻击或协防）
        /// </summary>
        private bool HandleCityAction(int troopIndex, GridData targetGrid, List<KeyValuePair<int, int>> selectSoldier,
            bool isTeam, Vector2Int startPos, Vector2Int goalPos, bool isMoving)
        {
            var cityData = targetGrid.CityData;

            if (isTeam)
            {
                // 创建联盟队伍攻城
                LeagueTeamMgr.Ins.ReqCreateLeagueTeam(troopIndex, LeagueTeamMgr.Ins.GetTimeType(),
                    GameDefine.TeamTarType.PlayerCity, targetGrid.pos, selectSoldier);
            }
            else if (LeagueMgr.Ins.IsLeagueMember(cityData.roleId))
            {
                // 协防联盟成员城市
                WorldNetWorkMgr.Ins.SendReqCityHelpDef(troopIndex, targetGrid.pos,
                    startPos == goalPos ? null : GetPathResultServer(), selectSoldier, isMoving);
            }
            else
            {
                // 攻击敌方城市
                WorldNetWorkMgr.Ins.SendReqCityAttack(troopIndex, targetGrid.pos,
                    startPos == goalPos ? null : GetPathResultServer(), selectSoldier, isMoving);
            }

            return true;
        }

        /// <summary>
        /// 处理采集行动
        /// </summary>
        private bool HandleMineAction(int troopIndex, GridData targetGrid, List<KeyValuePair<int, int>> selectSoldier, bool isMoving)
        {
            var mineInfo = targetGrid.MineInfo;

            // 检查是否为自己的采集点
            if (UserMgr.Ins.IsMyself(mineInfo.ownerId))
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000251);
                return false;
            }

            // 检查部队负载是否已满
            var troopInfo = TroopMgr.Ins.GetTroopInfo(troopIndex);
            if (troopInfo?.curLoad >= troopInfo?.maxLoad)
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000263);
                return false;
            }

            WorldNetWorkMgr.Ins.SendReqMineCollect(troopIndex, mineInfo.mineId, GetPathResultServer(), selectSoldier, isMoving);
            return true;
        }

        /// <summary>
        /// 处理野怪攻击行动
        /// </summary>
        private bool HandleMonsterAction(int troopIndex, GridData targetGrid, List<KeyValuePair<int, int>> selectSoldier,
            bool autoReturn, bool isTeam, Vector2Int startPos, Vector2Int goalPos, bool isMoving)
        {
            var monsterInfo = targetGrid.MonsterInfo;

            // 检查体力消耗
            if (!CheckMonsterStaminaCost(monsterInfo))
            {
                return false;
            }

            // 检查是否可以攻击野怪
            if (!CheckMonsterCanAttack(monsterInfo))
            {
                return false;
            }

            var monsterType = (MonsterType)monsterInfo.MonsterCfg.monster_type;
            switch (monsterType)
            {
                case MonsterType.Normal:
                case MonsterType.RadarMonster:
                case MonsterType.RadarBoss:
                    WorldNetWorkMgr.Ins.SendReqAttackMonster(troopIndex,
                        startPos == goalPos ? null : GetPathResultServer(),
                        monsterInfo.id, selectSoldier, isMoving, autoReturn);
                    break;

                case MonsterType.Boss:
                    LeagueTeamMgr.Ins.ReqCreateLeagueTeam(troopIndex, LeagueTeamMgr.Ins.GetTimeType(),
                        GameDefine.TeamTarType.NPCMonster, targetGrid.pos, selectSoldier);
                    break;
            }

            return true;
        }

        /// <summary>
        /// 检查野怪攻击的体力消耗
        /// </summary>
        private bool CheckMonsterStaminaCost(MonsterInfo monsterInfo)
        {
            int costStamina = monsterInfo.MonsterCfg.strength_cost;

            // 雷达事件野怪的体力消耗计算
            if (monsterInfo.radarEventRoldId != 0)
            {
                if (!UserMgr.Ins.IsMyself(monsterInfo.radarEventRoldId))
                {
                    TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000492);
                    return false;
                }

                var radarInfo = RadarMgr.Ins.GetRadarInfoById(monsterInfo.radarEventId);
                costStamina = radarInfo.EventCfg.strength_cost;
            }

            if (!StaminaMgr.Ins.CheckStaminaEnough(costStamina))
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000306);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 处理要塞城市行动
        /// </summary>
        private bool HandleFortressAction(int troopIndex, GridData targetGrid, List<KeyValuePair<int, int>> selectSoldier, bool isTeam, bool isMoving)
        {
            var cityInfo = WorldCitySiegeMgr.Ins.GetFortressCityInfo(targetGrid.CityAreaId);

            // 如果临时占领的是自己联盟，进行协防
            if (LeagueMgr.Ins.IsMyLeague(cityInfo.tempOwnerLeagueInfo))
            {
                WorldCitySiegeMgr.Ins.SendReqDefendCity(targetGrid.CityAreaId, troopIndex,
                    GetPathResultServer(), selectSoldier, isMoving);
                return true;
            }

            // 根据城市状态决定行动
            if (cityInfo.state == WorldCitySiegeState.Peace)
            {
                // 和平期间，如果是自己联盟的城市则协防
                if (LeagueMgr.Ins.IsMyLeague(cityInfo.ownerLeague))
                {
                    WorldCitySiegeMgr.Ins.SendReqDefendCity(targetGrid.CityAreaId, troopIndex,
                        GetPathResultServer(), selectSoldier, isMoving);
                }
            }
            else
            {
                // 非和平期间，检查免战时间
                int curTime = TimeUtils.GetServerTimeInt();
                if (curTime > cityInfo.tempPeaceEndTime)
                {
                    if (isTeam)
                    {
                        LeagueTeamMgr.Ins.ReqCreateLeagueTeam(troopIndex, LeagueTeamMgr.Ins.GetTimeType(),
                            GameDefine.TeamTarType.UnionCity, targetGrid.pos, selectSoldier);
                    }
                    else
                    {
                        WorldCitySiegeMgr.Ins.SendReqAttackCity(targetGrid.CityAreaId, troopIndex,
                            GetPathResultServer(), selectSoldier, isMoving);
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// 检查目标格子是否需要战斗
        /// </summary>
        /// <param name="gridData">目标格子数据</param>
        /// <returns>是否需要战斗</returns>
        private bool CheckNeedBattle(GridData gridData)
        {
            // 检查城市目标
            if (gridData.CityData != null)
            {
                return CheckCityNeedBattle(gridData.CityData);
            }

            // 检查野怪目标
            if (gridData.MonsterInfo != null)
            {
                return true; // 野怪总是需要战斗
            }

            // 检查采集点目标
            if (gridData.MineInfo != null)
            {
                return CheckMineNeedBattle(gridData.MineInfo);
            }

            // 检查要塞城市目标
            if (gridData.CityAreaId != 0)
            {
                return CheckFortressCityNeedBattle(gridData.CityAreaId);
            }

            return false;
        }

        /// <summary>
        /// 检查城市是否需要战斗
        /// </summary>
        private bool CheckCityNeedBattle(BigMapMainCity cityData)
        {
            ulong roleId = cityData.roleId;

            // 如果是自己或联盟成员的城市，不需要战斗
            if (UserMgr.Ins.IsMyself(roleId) || LeagueMgr.Ins.IsLeagueMember(roleId))
            {
                return false;
            }

            return true; // 敌方城市需要战斗
        }

        /// <summary>
        /// 检查采集点是否需要战斗
        /// </summary>
        private bool CheckMineNeedBattle(MineInfo mineInfo)
        {
            ulong roleId = mineInfo.ownerId;

            // 如果是自己、联盟成员的采集点或无主采集点，不需要战斗
            if (UserMgr.Ins.IsMyself(roleId) || LeagueMgr.Ins.IsLeagueMember(roleId) || roleId == 0)
            {
                return false;
            }

            return true; // 敌方占领的采集点需要战斗
        }

        /// <summary>
        /// 检查要塞城市是否需要战斗
        /// </summary>
        private bool CheckFortressCityNeedBattle(int cityAreaId)
        {
            var cityInfo = WorldCitySiegeMgr.Ins.GetFortressCityInfo(cityAreaId);

            // 如果是免战期，不需要战斗
            if (cityInfo.IsInPeace())
            {
                return false;
            }

            // 如果当前临时占领的是自己的联盟，不需要战斗
            if (LeagueMgr.Ins.IsMyLeague(cityInfo.tempOwnerLeagueInfo))
            {
                return false;
            }

            // 如果没有临时占领联盟，但当前占领的是自己的联盟，不需要战斗
            if (LeagueMgr.Ins.IsMyLeague(cityInfo.ownerLeague))
            {
                return false;
            }

            return true; // 敌方控制的要塞需要战斗
        }

        public bool CheckMonsterCanAttack(MonsterInfo monsterInfo)
        {
            MonsterType monsterType = (MonsterType)monsterInfo.MonsterCfg.monster_type;
            int unlockLevel = monsterType == MonsterType.Boss ? AssembleMonsterUnlockLv : MonsterUnlockLv;
            if (monsterInfo.level > unlockLevel)
            {
                TipsMgr.Ins.ShowTips(Localization.ToFormat(LanguageCode.__CODE__1000264, unlockLevel));
                return false;
            }

            if (monsterInfo.isFighting)
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000266);
                return false;
            }

            if (_gridDataMgr.CheckMonsterHasLocked(monsterInfo.pos))
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000265);
                return false;
            }

            return true;
        }

        public void OnSyncMines(IList<BigMapMineInfo> netMineInfoList)
        {
            _gridDataMgr?.OnSyncMines(netMineInfoList);
        }

        public void OnSyncDelMines(IList<ulong> delMineIds)
        {
            EventManager.Ins.DispatchEvent(UIEventType.OnSyncDelMines, delMineIds);
            _gridDataMgr?.OnSyncDelMines(delMineIds.ToHashSet());
        }

        public void OnSyncMonster(IList<BigMapMonsterInfo> netMonsterInfoList)
        {
            _gridDataMgr?.OnSyncMonster(netMonsterInfoList);
        }

        public void OnSyncDelMonster(IList<ulong> delMonsterIds)
        {
            EventManager.Ins.DispatchEvent(UIEventType.OnSyncDelMonster, delMonsterIds);
            _gridDataMgr?.OnSyncDelMonster(delMonsterIds.ToHashSet());
        }

        public void OnSyncCitys(IList<Packet.BigMapMainCity> netCityInfoList)
        {
            _gridDataMgr?.OnSyncCitys(netCityInfoList);
        }

        public void OnSyncDispatch(IList<BigMapDispatchInfo> netDispatchInfoList)
        {
            _gridDataMgr?.OnSyncDispatch(netDispatchInfoList);
        }

        public void OnSyncDelDispatch(IList<ulong> delDispatchIds)
        {
            _gridDataMgr?.OnSyncDelDispatch(delDispatchIds.ToHashSet());
        }

        public List<Vector2Int> GetPathResult() => _worldMapPathFinder.PathResult;
        public List<Vector2Int> GetPathResultServer() => _worldMapPathFinder.PathResultServer;
        public bool IsPathFinding() => _worldMapPathFinder.Finding;

        public void SelectTroop(TroopInfo troopInfo)
        {
            if (WorldMapBehaviour != null)
                WorldMapBehaviour.SelectTroop(troopInfo);
        }

        public Dictionary<ulong, TroopBattleInfo> GetBattleInfos() => _battleMgr.BattleInfos;

        public List<TroopBattleInfo> GetBattleInfoByDefId(ulong roleId) => _battleMgr.GetBattleInfoByDefId(roleId);

        public void GetRemoveBattleIds(List<ulong> list, long curTime) => _battleMgr.GetRemoveBattleIds(list, curTime);

        public List<Vector2Int> GetTargetBattlePos(Vector2Int pos) => _battleMgr.GetTargetBattlePos(pos);

        public RoleBattleInfo GetBattleAtkInfo(ulong troopId)
        {
            var battleInfo = _battleMgr.GetBattleInfo(troopId);
            return battleInfo?.atkInfo;
        }

        /// <summary>
        /// 检查是否可以在指定位置迁移城市
        /// </summary>
        /// <param name="CityPos">目标城市位置坐标</param>
        /// <param name="checkPos">需要检查的周围格子位置列表（通常包含城市周围的9格）</param>
        /// <returns>如果可以迁移返回true，否则返回false</returns>
        public bool CheckCanMoveCity(Vector2Int CityPos, IList<Vector2Int> checkPos)
        {
            // 检查世界地图行为控制器是否可用
            if (WorldMapBehaviour == null)
            {
                return false;
            }

            //StringBuilder sb = new StringBuilder();

            //for (int i = 0; i < checkPos.Count; i++)
            //{
            //    sb.Append($"{checkPos[i]} ");
            //}

            //Debug.LogError($"{CityPos} {sb.ToString()}");

            // 验证目标城市位置是否在有效范围内（1-1000）
            if (!WorldMapUtils.IsValidatePos(CityPos))
            {
                return false;
            }

            // 获取目标位置的格子数据
            GridData centerGridData = GetGridData(CityPos);
            if (centerGridData == null)
            {
                return false;
            }

            // 检查目标位置的格子类型
            GridType centerGridType = centerGridData.Type;

            // 如果目标位置是矿点，需要额外检查
            if (centerGridType == GridType.Mine)
            {
                var mineInfo = centerGridData.MineInfo;
                // 矿点已被占领，不能迁移
                if (mineInfo.ownerId != 0)
                {
                    return false;
                }
                // 矿点有雷达事件，不能迁移
                if (mineInfo.radarEventRoldId != 0)
                {
                    return false;
                }
            }
            // 如果目标位置是野怪，需要额外检查
            else if (centerGridType == GridType.Monster)
            {
                var monsterInfo = centerGridData.MonsterInfo;
                // 野怪正在战斗中，不能迁移
                if (monsterInfo.isFighting)
                {
                    return false;
                }
                // 野怪有雷达事件，不能迁移
                if (monsterInfo.radarEventRoldId != 0)
                {
                    return false;
                }
            }

            // 检查城市周围的所有格子
            foreach (var pos in checkPos)
            {
                // 验证周围格子位置是否在有效范围内
                if (!WorldMapUtils.IsValidatePos(pos))
                {
                    return false;
                }

                int index = WorldMapUtils.GetGridIndex(pos);
                GridData gridData = GetGridData(index);
                if (gridData == null)
                {
                    return false;
                }

                // 格子不可行走，不能迁移
                if (!gridData.Walkable)
                {
                    return false;
                }

                // 如果是空地或装饰格子，需要检查周围是否有其他玩家的城市
                if (gridData.IsOpenSpace())
                {
                    // 检查该空地周围所有8个方向的格子
                    foreach (var dir in WorldMapUtils.allDirections)
                    {
                        Vector2Int dirPos = pos + dir;
                        GridData dirGridData = GetGridData(dirPos);
                        if (dirGridData == null)
                        {
                            return false;
                        }

                        GridType dirType = dirGridData.Type;
                        // 如果周围有城市
                        if (dirType == GridType.City)
                        {
                            var cityData = dirGridData.CityData;
                            ulong roleId = cityData.roleId;
                            // 周围有其他玩家的城市，不能迁移（避免城市贴脸）
                            if (!UserMgr.Ins.IsMyself(roleId))
                            {
                                return false;
                            }
                        }
                    }
                }
            }

            // 所有检查都通过，可以迁移
            return true;
        }
    }
}