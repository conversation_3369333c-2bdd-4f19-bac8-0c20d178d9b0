using Cysharp.Threading.Tasks;
using GameMain;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GuideStepDialogue : GuideStepBase
{
    public override async UniTask DoGuideStep()
    {
        if (cfg == null) return;
        await base.DoGuideStep();
        DialogueMgr.Ins.PlayDialogue(cfg.param[0], OnDialogueFinishCallBack);
    }

    void OnDialogueFinishCallBack()
    {
        FinishCurStep();
    }

    public override async UniTask OnFinishStep()
    {
        await base.OnFinishStep();

    }
}
