using System.IO;
//From reddot.xlsx
public class Reddot : cfg_base<int>
{
	public int id {get;set;}
	public int rootId {get;set;}
	public int[] place {get;set;}
	public int reddot_type {get;set;}
	public string reddot_res {get;set;}
	public float size {get;set;}
	public int transparency {get;set;}
	public int disappear {get;set;}

	public override void ReadData(CfgInfo br)
	{
		id = br.ReadInt32();
		rootId = br.ReadInt32();
		place = br.GetInts();
		reddot_type = br.ReadInt32();
		reddot_res = br.ReadString();
		size = br.ReadSingle();
		transparency = br.ReadInt32();
		disappear = br.ReadInt32();
	}
	public override int GetKeyProperty()
	{
		return id;
	}
}
