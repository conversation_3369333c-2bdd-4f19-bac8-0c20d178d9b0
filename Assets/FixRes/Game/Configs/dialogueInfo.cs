using System.IO;
//From dialogue.xlsx
public class DialogueInfo : cfg_base<int>
{
	public int Id {get;set;}
	public int ContentId {get;set;}
	public int NameId {get;set;}
	public int NextId {get;set;}
	public string Spine {get;set;}
	public int[] SpineOffset {get;set;}
	public int SpineReversal {get;set;}
	public int[] SpineScale {get;set;}
	public int Audio {get;set;}

	public override void ReadData(CfgInfo br)
	{
		Id = br.ReadInt32();
		ContentId = br.ReadInt32();
		NameId = br.ReadInt32();
		NextId = br.ReadInt32();
		Spine = br.ReadString();
		SpineOffset = br.GetInts();
		SpineReversal = br.ReadInt32();
		SpineScale = br.GetInts();
		Audio = br.ReadInt32();
	}
	public override int GetKeyProperty()
	{
		return Id;
	}
}
