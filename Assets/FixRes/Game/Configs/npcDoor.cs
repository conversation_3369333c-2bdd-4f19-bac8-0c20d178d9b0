using System.IO;
//From npc.xlsx
public class NpcDoor : cfg_base<int>
{
	public int id {get;set;}
	public int move_speed {get;set;}
	public int[] percentage {get;set;}
	public int[] buffs {get;set;}
	public int[] triggerEffect {get;set;}
	public int[] buff_Colour {get;set;}

	public override void ReadData(CfgInfo br)
	{
		id = br.ReadInt32();
		move_speed = br.ReadInt32();
		percentage = br.GetInts();
		buffs = br.GetInts();
		triggerEffect = br.GetInts();
		buff_Colour = br.GetInts();
	}
	public override int GetKeyProperty()
	{
		return id;
	}
}
