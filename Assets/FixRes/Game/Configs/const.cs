using System.IO;
//From const.xlsx
public class Const : cfg_base<int>
{
	public int id {get;set;}
	public int value1 {get;set;}
	public int[] value2 {get;set;}
	public int[][] value3 {get;set;}
	public float value4 {get;set;}

	public override void ReadData(CfgInfo br)
	{
		id = br.ReadInt32();
		value1 = br.ReadInt32();
		value2 = br.GetInts();
		value3 = br.GetInts2();
		value4 = br.ReadSingle();
	}
	public override int GetKeyProperty()
	{
		return id;
	}
}
