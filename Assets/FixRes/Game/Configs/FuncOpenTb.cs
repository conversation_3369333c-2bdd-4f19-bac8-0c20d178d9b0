using System.IO;
public class FuncOpenTb : cfg_base<int>
{
	public int id {get;set;}
	public int fun_name {get;set;}
	public string c_com_res {get;set;}
	public int c_btns_type {get;set;}
	public int c_btns_module {get;set;}
	public int c_btns_sort {get;set;}
	public int show_open {get;set;}
	public string c_an_res_atlas {get;set;}
	public int[] visible_type {get;set;}
	public string c_showPanel {get;set;}
	public int[] c_CurrencyShow {get;set;}
	public int[] activity_type {get;set;}
	public int unlock_day {get;set;}
	public int unlock_mainCity_lv {get;set;}
	public int unlock_mainMiss_id {get;set;}
	public int visible_day {get;set;}
	public int visible_mainCity_lv {get;set;}
	public int visible_mainMiss_id {get;set;}
	public string c_panel {get;set;}
	public string c_activation_panel {get;set;}
	public int red_point {get;set;}

	public override void ReadData(CfgInfo br)
	{
		id = br.ReadInt32();
		fun_name = br.ReadInt32();
		c_com_res = br.ReadString();
		c_btns_type = br.ReadInt32();
		c_btns_module = br.ReadInt32();
		c_btns_sort = br.ReadInt32();
		show_open = br.ReadInt32();
		c_an_res_atlas = br.ReadString();
		visible_type = br.GetInts();
		c_showPanel = br.ReadString();
		c_CurrencyShow = br.GetInts();
		activity_type = br.GetInts();
		unlock_day = br.ReadInt32();
		unlock_mainCity_lv = br.ReadInt32();
		unlock_mainMiss_id = br.ReadInt32();
		visible_day = br.ReadInt32();
		visible_mainCity_lv = br.ReadInt32();
		visible_mainMiss_id = br.ReadInt32();
		c_panel = br.ReadString();
		c_activation_panel = br.ReadString();
		red_point = br.ReadInt32();
	}
	public override int GetKeyProperty()
	{
		return id;
	}
}
