using System.IO;
//From radar.xlsx
public class Radar : cfg_base<int>
{
	public int level {get;set;}
	public int[][] refesh_num {get;set;}
	public int[][] event_quality {get;set;}
	public int storage_num {get;set;}
	public int display_num {get;set;}
	public int experience {get;set;}
	public int mine_level {get;set;}
	public float gather_speed {get;set;}
	public int[][] reward {get;set;}

	public override void ReadData(CfgInfo br)
	{
		level = br.ReadInt32();
		refesh_num = br.GetInts2();
		event_quality = br.GetInts2();
		storage_num = br.ReadInt32();
		display_num = br.ReadInt32();
		experience = br.ReadInt32();
		mine_level = br.ReadInt32();
		gather_speed = br.ReadSingle();
		reward = br.GetInts2();
	}
	public override int GetKeyProperty()
	{
		return level;
	}
}
