using System.IO;
//From building.xlsx
public class Building : cfg_base<int>
{
	public int building_id {get;set;}
	public int type {get;set;}
	public int sub_type {get;set;}
	public int cityRegion {get;set;}
	public string model_picture {get;set;}
	public float effect_scale {get;set;}
	public int name {get;set;}
	public int dec {get;set;}
	public int build_time {get;set;}
	public int max_level {get;set;}
	public int[][] build_cost {get;set;}
	public int building_foundation {get;set;}
	public int[] name_offset {get;set;}
	public int[] move_num {get;set;}
	public int[] ellipse_data {get;set;}
	public int[] ellipse_data_offset {get;set;}
	public string icon {get;set;}
	public int model {get;set;}
	public int unlock_type {get;set;}
	public int[] unlock_param {get;set;}
	public int[] building_level {get;set;}
	public int default_building {get;set;}
	public bool send_citizen {get;set;}
	public int parallel {get;set;}
	public string model_point {get;set;}
	public int show_float {get;set;}
	public int bubble_height {get;set;}

	public override void ReadData(CfgInfo br)
	{
		building_id = br.ReadInt32();
		type = br.ReadInt32();
		sub_type = br.ReadInt32();
		cityRegion = br.ReadInt32();
		model_picture = br.ReadString();
		effect_scale = br.ReadSingle();
		name = br.ReadInt32();
		dec = br.ReadInt32();
		build_time = br.ReadInt32();
		max_level = br.ReadInt32();
		build_cost = br.GetInts2();
		building_foundation = br.ReadInt32();
		name_offset = br.GetInts();
		move_num = br.GetInts();
		ellipse_data = br.GetInts();
		ellipse_data_offset = br.GetInts();
		icon = br.ReadString();
		model = br.ReadInt32();
		unlock_type = br.ReadInt32();
		unlock_param = br.GetInts();
		building_level = br.GetInts();
		default_building = br.ReadInt32();
		send_citizen = br.ReadBool();
		parallel = br.ReadInt32();
		model_point = br.ReadString();
		show_float = br.ReadInt32();
		bubble_height = br.ReadInt32();
	}
	public override int GetKeyProperty()
	{
		return building_id;
	}
}
