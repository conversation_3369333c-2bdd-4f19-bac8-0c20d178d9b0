using System.IO;
//From monument.xlsx
public class MonumentTask : cfg_base<int>
{
	public int id {get;set;}
	public int task_name {get;set;}
	public int task_content {get;set;}
	public int[] event_id {get;set;}
	public int stage {get;set;}
	public int unlock_func {get;set;}
	public int start_time {get;set;}
	public int last_time {get;set;}
	public int[] reward {get;set;}
	public string task_banner {get;set;}

	public override void ReadData(CfgInfo br)
	{
		id = br.ReadInt32();
		task_name = br.ReadInt32();
		task_content = br.ReadInt32();
		event_id = br.GetInts();
		stage = br.ReadInt32();
		unlock_func = br.ReadInt32();
		start_time = br.ReadInt32();
		last_time = br.ReadInt32();
		reward = br.GetInts();
		task_banner = br.ReadString();
	}
	public override int GetKeyProperty()
	{
		return id;
	}
}
