using System.Collections.Generic;
using UnityEngine;

public class DataWild_monsterTb : ConfigData<int, Wild_monsterTb>
{
    private Dictionary<int, int> _monsterMaxLv = new Dictionary<int, int>();
    
    public override void Init(Dictionary<int, Wild_monsterTb> dataDic)
    {
        base.Init(dataDic);
        foreach (var item in dataDic)
        {
            if (!_monsterMaxLv.ContainsKey(item.Value.monster_type))
            {
                _monsterMaxLv[item.Value.monster_type] = item.Value.level;
            }
            else
            {
                _monsterMaxLv[item.Value.monster_type] = Mathf.Max(_monsterMaxLv[item.Value.monster_type], item.Value.level);
            }
        }
    }
    
    public int GetMonsterMaxLv(int monsterType)
    {
        return _monsterMaxLv.ContainsKey(monsterType) ? _monsterMaxLv[monsterType] : 0;
    }
}