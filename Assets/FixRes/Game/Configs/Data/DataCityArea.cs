using GameMain;
using System.Collections.Generic;

public class DataCityArea : ConfigData<int, CityArea>
{
    public Dictionary<int, int> CityPosIndex { get; private set; }

    public override void Init(Dictionary<int, CityArea> dataDic)
    {
        base.Init(dataDic);
        CityPosIndex = new Dictionary<int, int>(dataDic.Count);
        foreach (var kv in dataDic)
        {
            int x = kv.Value.city_coordinate[0];
            int y = kv.Value.city_coordinate[1];
            int index = WorldMapUtils.GetGridIndex(x, y);
            CityPosIndex.Add(index, kv.Key);
        }
    }

    public int GetCityIdByIndex(int index)
    {
        CityPosIndex.TryGetValue(index, out int cityId);
        return cityId;
    }

    public CityArea GetCityAreaByIndex(int index)
    {
        int cityId = GetCityIdByIndex(index);
        if (cityId == 0) return null;
        return GetData(cityId);
    }
}
