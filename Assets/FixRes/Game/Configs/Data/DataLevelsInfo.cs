using System;
using System.Collections.Generic;
using System.Linq;

public class DataLevelsInfo : ConfigData<int, LevelsInfo>
{
    /// <summary>
    /// 仅pve 战斗使用
    /// </summary>
    public LevelsInfo[] SortedLevelsInfoList { get; set; }
    
    public enum  LevelType
    {
        Pve= 1,
        Radar=2,
    }
    /// <summary>
    /// 各个类型对应的 type 的LevelInfo
    /// </summary>
    public Dictionary<int,List<LevelsInfo>> SortedLevelsDictList =new Dictionary<int, List<LevelsInfo>>();
    public override void Init(Dictionary<int, LevelsInfo> dataDic)
    {
        base.Init(dataDic);
        InitDict();
        SortedLevelsInfoList = GetTypeData(LevelType.Pve).ToArray();
        Array.Sort(SortedLevelsInfoList, (a, b) => a.index - b.index);
    }

    private void InitDict()
    {
        var allData =  GetAllData().Values.ToArray();
        SortedLevelsDictList.Clear();
        foreach (var item in allData)
        {
            if (SortedLevelsDictList.TryGetValue(item.type,out var list))
            {
                if (list == null)
                {
                    return;
                }
                list.Add(item);
            }
            else
            { 
                var  levelInfoList=new List<LevelsInfo>();
                levelInfoList.Add(item);
                SortedLevelsDictList.Add(item.type, levelInfoList);
            }
        }
    }

    public List<LevelsInfo> GetRewardLevels()
    {
        List<LevelsInfo> result = new List<LevelsInfo>();
        var allData = GetAllData();
        foreach (var kv in allData)
        {
            if (kv.Value.chest_reward != 0)
            {
                result.Add(kv.Value);
            }
        }
        
        return result;
    }
    
    public LevelsInfo GetFirstLevel()
    {
        var allData = GetAllData();
        foreach (var kv in allData)
        {
            if (kv.Value.index == 1)
            {
                return kv.Value;
            }
        }
        
        return null;
    }

    public List<LevelsInfo> GetTypeData(LevelType levelType)
    {
        return SortedLevelsDictList.GetValueOrDefault((int)levelType);
    }

    /// <summary>
    /// 判断关卡 id 是否是Pve
    /// </summary>
    /// <param name="levelID"></param>
    /// <param name="levelType"></param>
    /// <returns></returns>
    public bool CheckIsPve(int levelID, LevelType levelType = LevelType.Pve)
    {
        var levleInfo = Cfg.LevelsInfo.GetData(levelID);
        if (levleInfo == null) return false;
        return levleInfo.type==(int)levelType;
    }
}