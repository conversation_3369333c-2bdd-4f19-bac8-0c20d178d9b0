using System.Collections.Generic;

public class DataImmigrationTb : ConfigData<int, ImmigrationTb>
{

    private List<int> m_Ids;

    private Dictionary<int, int> m_BuildingSkill = new Dictionary<int, int>(8);
    public override void Init(Dictionary<int, ImmigrationTb> dataDic)
    {
        base.Init(dataDic);
        m_Ids = new List<int>(dataDic.Keys);
        
        foreach(var data in dataDic)
        {
            var id = data.Key;
            var cfg = data.Value;
            var buildings = cfg.dispatch_building;
            if(buildings != null && buildings.Length > 0)
            {
                foreach (var buildingId in buildings) { 
                    if(!m_BuildingSkill.TryGetValue(buildingId, out var skillId)){
                        var skill = cfg.skill;
                        if (skill != null && skill.Length > 0) { 
                            m_BuildingSkill.Add(buildingId, cfg.skill[0]);
                        }
                    }
                }
            }
        }
    }

    public List<int> GetIds()
    {
        return m_Ids;
    }

    public int GetCountByQualitry(int quality)
    {
        if(quality == -1)
        {
            return _data.Count;
        }
        var sum = 0;
        foreach(var data in _data.Values)
        {
            if(data.quality == quality)
            {
                sum++;
            }
        }
        return sum;
    }

    public int GetBuildingBuff(int buildingId)
    {
        if(m_BuildingSkill.TryGetValue(buildingId, out var skill))
        {
            var skillCfg = Cfg.ImmigrationSkillPower.GetData(skill);
            if(skillCfg != null)
            {
                return skillCfg.skill_buff;
            }
        }
        return 0;
    }
    
}
