using Unity.Mathematics;
using UnityEngine;

public class DataUnionTb : ConfigData<int, UnionTb>
{
    /// <summary>
    /// 捐献次数
    /// </summary>
    public int DonateCount = 6;

    /// <summary>
    /// 捐献钻石
    /// </summary>
    public int DonateDiamond = 7;

    /// <summary>
    /// 捐献钻石上限
    /// </summary>
    public int DonateDiamondMax = 8;

    /// <summary>
    /// 捐献恢复时间
    /// </summary>
    public int DonateRecoverTime = 9;

    /// <summary>
    /// 推荐科技增益
    /// </summary>
    public int RecommendTechBonus = 10;

    /// <summary>
    /// 捐献暴击效果
    /// </summary>
    public int DonateCritEffect = 11;

    /// <summary>
    /// 联盟矿石初始值
    /// </summary>
    public int LeagueMineralInit = 12;

    /// <summary>
    /// 联盟矿石初始每分钟恢复值
    /// </summary>
    public int LeagueMineralInitRecoverPerMinute = 13;

    /// <summary>
    /// 联盟宣战令上限
    /// </summary>
    public int LeagueWarOrderMax = 14;

    /// <summary>
    /// 联盟宣战令恢复时间
    /// </summary>
    public int LeagueWarOrderRecoverTime = 15;

    /// <summary>
    /// 联盟矿石上限
    /// </summary>
    public int LeagueMineralMax = 16;

    /// <summary>
    /// 获取消耗钻石 
    /// </summary>
    /// <param name="diamondDonation"></param>
    /// <returns></returns>
    public int GetDiamondDonation(int diamondDonation)
    {
        int result = GetData(DonateDiamond).value2;
        int max = GetData(DonateDiamondMax).value2;
        result += result * diamondDonation;
        result = Mathf.Clamp(result, 0, max);
        return result;
    }
}