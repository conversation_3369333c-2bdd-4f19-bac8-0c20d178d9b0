using System.Collections.Generic;

public class DataResourcesPrice : ConfigData<int, ResourcesPrice>
{
    Dictionary<int, List<ResourcesPrice>> mTypeDic= null;
    public override void Init(Dictionary<int, ResourcesPrice> dataDic)
    {
        base.Init(dataDic);
        mTypeDic = new Dictionary<int, List<ResourcesPrice>>();
        foreach (var item in dataDic)
        {
            int type = item.Value.type;
            if (!mTypeDic.ContainsKey(type))
            {
                mTypeDic.Add(type, new List<ResourcesPrice>());
            }
            mTypeDic[type].Add(item.Value);
        }

        foreach (var item in mTypeDic)
        {
            item.Value.Sort((a, b) => a.num.CompareTo(b.num));
        }
    }

    public ResourcesPrice GetInfoByType(int type, int val)
    {
        if (!mTypeDic.ContainsKey(type)) return null;

        List<ResourcesPrice> list = mTypeDic[type];
        if (list.Count > 0 && list[0].num >= val)
            return list[0];

        if (list[list.Count - 1].num <= val) 
            return list[list.Count - 1];

        for (int i = 0; i < list.Count; i++)
        {
            if (list[i].num >= val && list[i - 1].num < val) 
                return list[i];
        }

        return null;
    }
}
