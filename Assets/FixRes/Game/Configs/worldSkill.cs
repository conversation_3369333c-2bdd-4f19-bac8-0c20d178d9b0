using System.IO;
//From worldSkill.xlsx
public class WorldSkill : cfg_base<int>
{
	public int id {get;set;}
	public int type {get;set;}
	public int bulletID {get;set;}
	public int effectID {get;set;}
	public int skillTarget {get;set;}
	public float skill_Time {get;set;}
	public float active_Time {get;set;}
	public int self_effectID {get;set;}

	public override void ReadData(CfgInfo br)
	{
		id = br.ReadInt32();
		type = br.ReadInt32();
		bulletID = br.ReadInt32();
		effectID = br.ReadInt32();
		skillTarget = br.ReadInt32();
		skill_Time = br.ReadSingle();
		active_Time = br.ReadSingle();
		self_effectID = br.ReadInt32();
	}
	public override int GetKeyProperty()
	{
		return id;
	}
}
