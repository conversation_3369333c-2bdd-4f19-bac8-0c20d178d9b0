using System.Collections;
using System.Collections.Generic;
using Core;
using Unity.AI.Navigation;
using UnityEngine;

namespace GameBattle
{
    public class BattlePVEPos : MonoBehaviour
    {
        public int pos { get; set; }
        GameObject[] qualityGo = new GameObject[3];

        Transform _posSpriteTransform;
        NavMeshModifierVolume _navMeshModifierVolume;
        NavMeshModifierVolume _navMeshModifierVolumeAdditive;

        // Start is called before the first frame update
        void Start()
        {
            _posSpriteTransform = transform.Find("PosSprite");
            for (int i = 1; i <= 3; i++)
            {
                Transform qualityTransform = _posSpriteTransform.Find("QualitySprite" + i);
                if (qualityTransform)
                {
                    qualityGo[i - 1] = qualityTransform.gameObject;
                }
            }

            Transform navMeshAgent = transform.Find("NavMeshAgent");
            if (navMeshAgent)
            {
                _navMeshModifierVolume = navMeshAgent.GetComponent<NavMeshModifierVolume>();
            }

            Transform navMeshAgentAdditive = transform.Find("NavMeshAgentAdditive");
            if (navMeshAgentAdditive)
            {
                _navMeshModifierVolumeAdditive = navMeshAgentAdditive.GetComponent<NavMeshModifierVolume>();
            }
        }

        public void SetPosSpriteActive(bool value)
        {
            _posSpriteTransform.gameObject.SetActiveEx(value);
        }

        public void SetQuality(int quality)
        {
            for (int i = 1; i <= qualityGo.Length; i++)
            {
                if (qualityGo[i - 1] != null)
                {
                    qualityGo[i - 1].SetActiveEx(i == quality);
                }
            }
        }

        public void SetNavMeshModifierEnabled(bool value)
        {
            _navMeshModifierVolume.enabled = value;
            if (_navMeshModifierVolumeAdditive)
            {
                _navMeshModifierVolumeAdditive.enabled = value;
            }
        }
    }
}