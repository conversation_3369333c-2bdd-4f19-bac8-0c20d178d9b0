using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BattleHitTestHelper : MonoBehaviour
{
#if UNITY_EDITOR
    public bool inRange = false;
    private List<Color> colors = new List<Color>();
    MeshRenderer[] renderers;
    // Start is called before the first frame update
    void Start()
    {
        renderers = GetComponentsInChildren<MeshRenderer>();
        for (int i = 0; i < renderers.Length; i++)
        {
            colors.Add(renderers[i].material.color);
        }
    }

    // Update is called once per frame
    void Update()
    {
        if (inRange)
        {
            for (int i = 0; i < renderers.Length; i++)
            {
                renderers[i].material.color = Color.yellow;
            }
        }
        else
        {
            for (int i = 0; i < renderers.Length; i++)
            {
                renderers[i].material.color = colors[i];
            }
        }
    }
#endif
}
