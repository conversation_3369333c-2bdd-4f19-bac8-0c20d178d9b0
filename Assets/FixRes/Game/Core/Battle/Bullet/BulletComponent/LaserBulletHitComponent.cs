using System.Collections;
using System.Collections.Generic;
using Core;
using UnityEngine;
#if UNITY_EDITOR
using Physics = Vertx.Debugging.DrawPhysics;
#endif

namespace GameBattle
{
    public class LaserBulletHitComponent : BulletHitComponentBase
    {
        private LaserBullet laserBullet => (LaserBullet)bulletBase;
        public LaserBulletHitComponent(BulletBase bullet) : base(bullet)
        {
        }



        protected override void CheckHitSphere(int[] collosionArea)
        {
            DLogger.Error("不存在圆形的激光！");
        }
    }
}