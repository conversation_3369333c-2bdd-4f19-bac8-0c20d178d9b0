using Core;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace GameBattle
{
    /// <summary>
    /// PVE战斗计算工具类
    /// </summary>
    public class BattleUtils
    {
        //计算技能效果
        public static void CalcSkill(BattleObject atk, BattleObject target, int skillArgId)
        {
            var cfg = Cfg.SkillArg.GetData(skillArgId);
            if (cfg != null)
            {
                int[] cfg_param = cfg.param;
                //int effectId = cfg.effect_id;

                int state = cfg_param[0];

                switch (state)
                {
                    case BattleDefine.SkillState.Harm:
                        {
                            float param = UtilTool.GetVal(cfg_param[2], cfg_param[3]);
                            float crit = atk.fightAttr(BattleDefine.AttrType.Crit);
                            bool isCrit = BattleMgr.Ins.IsCrit(crit);
                            float dmg = param * Cfg.Formula.GetValType18(atk, target, isCrit); //计算伤害
                            target.OnDamage(dmg, isCrit);
                            if (atk is BattleHero && target is BattleNPCUnit)
                            {
                                (atk as BattleHero).OnTakeDamage(dmg, FightInfotype.Damage);
                            }
                            if (target is BattleHero)
                            {
                                (target as BattleHero).OnTakeDamage(dmg, FightInfotype.Beharmed);
                            }

                            if (atk.guid > 10)
                            {
                                DLogger.Log("CalcSkill {0} {1} {2} {3}", atk.guid, target.guid, skillArgId, dmg);
                            }
                        }
                        break;
                    case BattleDefine.SkillState.Attr:
                        {
                            int attrId = cfg_param[1];
                            float param = UtilTool.GetVal(cfg_param[2], cfg_param[3]);
                            var targets = GetSkillStateAttrTarget(atk, target, skillArgId);
                            if (targets != null)
                            {
                                foreach (var t in targets)
                                {
                                    t.AddAttr(attrId, param);
                                }
                            }
                        }
                        break;
                    case BattleDefine.SkillState.State:
                        {
                        }
                        break;
                }
            }
        }

        public static void DelSkill(BattleObject atk, BattleObject target, int skillArgId)
        {
            var cfg = Cfg.SkillArg.GetData(skillArgId);
            if (cfg != null)
            {
                int[] cfg_param = cfg.param;
                int state = cfg_param[0];
                if (state == BattleDefine.SkillState.Attr)
                {
                    int attrId = cfg_param[1];
                    float param = UtilTool.GetVal(cfg_param[2], cfg_param[3]);
                    target.AddAttr(attrId, -param);
                }
            }
        }

        private static List<BattleObject> GetSkillStateAttrTarget(BattleObject atk, BattleObject target, int skillArgId)
        {
            var battlePveCtrl = BattleMgr.Ins.GetPVECtrl();
            if (battlePveCtrl)
            {
                return battlePveCtrl.GetSkillStateAttrTarget(atk, target, skillArgId);
            }

            return null;
        }

        public static string GetBattleUIItemPath(string name) => $"Prefabs/UI/Battle/{name}.prefab";

        public static string GetTargetPrefix(BattleUnitType battleUnitType)
        {
            if (battleUnitType == BattleUnitType.None)
            {
                Debug.LogError("is none");
            }
            return (battleUnitType) switch
            {
                BattleUnitType.Hero => "Hero",
                BattleUnitType.Drone => "Drone",
                BattleUnitType.Npc => "NpcUnit",
            };
        }

        public static LayerMask GetHitLayerMask(BattleFaction battleFaction)
        {
            string layerName = battleFaction == BattleFaction.Player ? "Enemy" : "Hero";
            return LayerMask.GetMask(layerName);
        }

        public static int GetBattleGuidByGo(GameObject go)
        {
            string prefix = null;
            BattleUnitType battleUnitType = BattleUnitType.None;
            if (go.CompareTag(GameTag.PVEHero))
            {
                battleUnitType = BattleUnitType.Hero;
            }
            else if (go.CompareTag(GameTag.PVEDrone))
            {
                battleUnitType = BattleUnitType.Drone;
            }
            else if (go.CompareTag(GameTag.PVENpc))
            {
                battleUnitType = BattleUnitType.Npc;
            }
            if (battleUnitType == BattleUnitType.None)
            {
                return 0;
            }
            prefix = GetTargetPrefix(battleUnitType);
            if (string.IsNullOrEmpty(prefix))
            {
                return 0;
            }
            string numStr = go.name.Substring(prefix.Length);
            int.TryParse(numStr, out int guid);
            return guid;
        }
    }

    //战斗类型
    public enum BattleType
    {
        //普通战斗
        Normal,

        //城内PVE
        CityPVE,
        //雷达事件
        RadarPVE,
    }
}