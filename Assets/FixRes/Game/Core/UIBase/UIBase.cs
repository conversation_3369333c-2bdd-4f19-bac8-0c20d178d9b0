using Cysharp.Threading.Tasks;
using Game.Framework;
using GameUI;
using SuperScrollView;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Timers;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Core;
using TMPro;
using System.Threading;
using System.IO;
using GameMain;

namespace UIFramework
{
    public class UIBase : MonoBehaviour, EventReceiver
    {
        List<int> timerIds = null;
        HashSet<UIItem> subItems = null;
        protected Dictionary<string, int> resDic = null;
        List<WLoopGridView> mGridList = null;
        //List<UniTask> mTasks = new List<UniTask>();

        CancellationTokenSource cancellationTokenSource;
        CancellationToken cancellationToken;

        private void Awake()
        {
        }

        private void OnDestroy()
        {
        }

        public virtual void OnInit()
        {
            timerIds = new List<int>();
        }

        public virtual void OnUnInit()
        {
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Cancel();
                cancellationTokenSource.Dispose();
                cancellationTokenSource = null;
            }

            if (timerIds != null)
            {
                foreach (var timeId in timerIds)
                {
                    SimpleTimer.DeleteTimer(timeId);
                }

                timerIds.Clear();
                timerIds = null;
            }

            if (subItems != null)
            {
                foreach (var item in subItems)
                {
                    item.OnUnInit();
                }

                subItems.Clear();
            }

            if (mGridList != null)
            {
                for (int i = 0; i < mGridList.Count; i++)
                {
                    mGridList[i].Dispose();
                }
            }

            if (resDic != null)
            {
                foreach (var item in resDic)
                {
                    GameResMgr.Ins.UnloadAsset(item.Key, item.Value);
                }

                resDic.Clear();
            }

            _reddotUiBoundedDict?.Clear();
        }

        public int DelayCall(float delayTime, Action action)
        {
            if (timerIds == null)
                return 0;

            int timeId = SimpleTimer.AddTimer(delayTime, 0, action);
            timerIds.Add(timeId);

            return timeId;
        }

        public int RepeatCall(float interval, int count, Action action)
        {
            if (timerIds == null)
                return 0;

            int timeId = SimpleTimer.AddTimer(0, interval, count, action);
            timerIds.Add(timeId);

            return timeId;
        }

        public void RemoveTimerByIndex(int timeId)
        {
            timerIds.Remove(timeId);
            SimpleTimer.DeleteTimer(timeId);
        }

        protected async UniTask<WLoopGridView> CreateLoopGrid<T>(WScrollRect scrollRect, int itemTotalCount
            , Action<WLoopGridView, int, WLoopItem> refreshItemByIndex
            , LoopGridViewSettingParam settingParam = null) where T : WLoopItem => await CreateLoopGrid<T>(scrollRect, scrollRect.itemName, itemTotalCount, refreshItemByIndex, settingParam);

        protected async UniTask<WLoopGridView> CreateLoopGrid<T>(WScrollRect scrollRect, string itemName, int itemTotalCount
            , Action<WLoopGridView, int, WLoopItem> refreshItemByIndex
            , LoopGridViewSettingParam settingParam = null) where T : WLoopItem
        {
            WLoopGridView gridView = scrollRect.GetComponent<WLoopGridView>();
            if (gridView == null)
                gridView = scrollRect.gameObject.AddComponent<WLoopGridView>();

            if (settingParam == null && scrollRect.content)
            {
                GridLayoutGroup gridItemGroup = scrollRect.content.GetComponent<GridLayoutGroup>();
                if (gridItemGroup)
                {
                    settingParam = new LoopGridViewSettingParam()
                    {
                        mItemSize = gridItemGroup.cellSize,
                        mItemPadding = gridItemGroup.spacing,
                        mPadding = gridItemGroup.padding,
                    };
                    gridItemGroup.enabled = false;
                }

                HorizontalLayoutGroup horizontalLayoutGroup = scrollRect.content.GetComponent<HorizontalLayoutGroup>();
                if (horizontalLayoutGroup)
                {
                    settingParam = new LoopGridViewSettingParam()
                    {
                        mItemPadding = new Vector2(horizontalLayoutGroup.spacing, 0),
                        mPadding = horizontalLayoutGroup.padding,
                    };
                    horizontalLayoutGroup.enabled = false;
                }

                VerticalLayoutGroup verticalLayoutGroup = scrollRect.content.GetComponent<VerticalLayoutGroup>();
                if (verticalLayoutGroup)
                {
                    settingParam = new LoopGridViewSettingParam()
                    {
                        mItemPadding = new Vector2(0, verticalLayoutGroup.spacing),
                        mPadding = verticalLayoutGroup.padding,
                    };
                    verticalLayoutGroup.enabled = false;
                }
            }

            await gridView.InitAsync<T>(scrollRect, itemName, itemTotalCount, refreshItemByIndex, this, settingParam);

            if (mGridList == null)
                mGridList = new List<WLoopGridView>();

            mGridList.Add(gridView);

            return gridView;
        }

        public async UniTask<T> AddSubItem<T>(string itemName, Transform root, params object[] param) where T : UIItem
        {
            var go = await GameResMgr.Ins.LoadAndCreateUIItem(itemName, root, 1, this);
            if (go == null) return null;
            GameObject orgGo = go[0];

            //if (GuideMgr.Ins.IsGuiding())
            orgGo.name = Path.GetFileNameWithoutExtension(itemName);

            return await AddSubItem<T>(orgGo, param);
        }

        public async UniTask<T[]> AddSubItems<T>(string itemName, Transform root, int count, params object[] param) where T : UIItem
        {
            var go = await GameResMgr.Ins.LoadAndCreateUIItem(itemName, root, count, this);
            T[] items = new T[count];
            //mTasks.Clear();
            for (int i = 0; i < go.Length; i++)
            {
                items[i] = await AddSubItem<T>(go[i], param);
            }
            //await UniTask.WhenAll(mTasks);
            return items;
        }

        public async UniTask<T> AddSubItemWithoutPos<T>(GameObject go, params object[] param) where T : UIItem
        {
            if (subItems == null) subItems = new HashSet<UIItem>();
            var item = await UIHelper.InitItem<T>(go, true, param);
            item.SetParent(this);
            subItems.Add(item);
            return item;
        }
        public async UniTask<T> AddSubItem<T>(GameObject go, params object[] param) where T : UIItem
        {
            if (subItems == null) subItems = new HashSet<UIItem>();
            var item = await UIHelper.InitItem<T>(go, false, param);
            item.SetParent(this);
            subItems.Add(item);
            return item;
        }

        public async UniTask<T[]> AddSubItems<T>(GameObject go, Transform root, int count, params object[] param) where T : UIItem
        {
            if (subItems == null) subItems = new HashSet<UIItem>();
            var waitOpt = GameObject.InstantiateAsync<GameObject>(go, count, root, Vector3.zero, Quaternion.identity);
            await waitOpt;
            T[] items = new T[count];
            for (int i = 0; i < waitOpt.Result.Length; ++i)
            {
                var item = await UIHelper.InitItem<T>(go, false, param);
                item.SetParent(this);
                subItems.Add(item);
                items[i] = item;
            }

            return items;
        }

        public void RemoveSubItem(UIItem item)
        {
            subItems.Remove(item);
            item.OnUnInit();
            item.gameObject.DestroyEx();
        }

        protected virtual void RefreshAsync(Func<UniTaskVoid> task)
        {
            UniTask.Void(task);
        }

        public void CreateTask(Func<UniTask> task)
        {
            if (cancellationTokenSource == null)
            {
                cancellationTokenSource = new CancellationTokenSource();
                cancellationToken = cancellationTokenSource.Token;
            }

            UniTask.Create(task).AttachExternalCancellation(cancellationToken);
        }

        public virtual void RecvMsg(params object[] args)
        {
        }

        public void AddUseRes(string path, int count = 1)
        {
            if (resDic == null)
                resDic = new Dictionary<string, int>();

            if (!resDic.ContainsKey(path))
                resDic.Add(path, count);
            else
                resDic[path] += count;
        }

        public virtual void OnLinkEvent(GameObject go, TMP_LinkInfo linkInfo)
        {
        }

        public void RegisterEvent(UIEventType eventType, Action handler)
        {
            EventManager.Ins.RegisterEvent(eventType, this, handler);
        }

        public void RegisterEvent(UIEventType eventType, Action<object> handler)
        {
            EventManager.Ins.RegisterEvent(eventType, this, handler);
        }

        #region 红点系统

        private Dictionary<int, UIReddotBaseItem> _reddotUiBoundedDict;


        /// <summary>
        /// 这里是动态加载红点资源，仅限于挂置在panel的静态展示节点下（不会回对象池的）
        /// 切记不要挂到对象池管理的GameObject上
        /// </summary>
        /// <param name="reddotId"></param>
        /// <param name="root"></param>
        public async UniTask BindReddot(int reddotId, Transform root)
        {
            if (_reddotUiBoundedDict == null)
            {
                _reddotUiBoundedDict = new Dictionary<int, UIReddotBaseItem>();
            }

            if (_reddotUiBoundedDict.ContainsKey(reddotId))
            {
                DLogger.Error("[Reddot]此节点已经绑定过，不可重复绑定");
                return;
            }

            var cfg = Cfg.Reddot.GetData(reddotId);
            //_reddotUiLoadingDict.Add(reddotId, task);

            string reddotUiPath = $"Reddot/{cfg.reddot_res}";
            UIReddotBaseItem newItem = await AddSubItem<UIReddotBaseItem>(reddotUiPath, root, reddotId);
            newItem.RefreshView();
            _reddotUiBoundedDict.Add(reddotId, newItem);
        }

        //目前应该不需要运行时解绑红点吧，绑上去就完事儿了？？？
        //注意解绑会触发一个subitem的遍历，性能不是很好
        /*protected void UnBindReddot(int reddotId)
        {
        
            if (_reddotUiLoadingDict.ContainsKey(reddotId))
            {
                DLogger.Error("[Reddot]此节点资源加载中，无法解除绑定");
                return;
            }
        
            if (_reddotUiBoundedDict.ContainsKey(reddotId))
            {
                var reddotUi = _reddotUiBoundedDict[reddotId];
                reddotUi.OnUnInit();
                this.subItems.Remove(reddotUi);
                reddotUi.DestroyEx();
            }
        
        }*/

        #endregion

    }
}