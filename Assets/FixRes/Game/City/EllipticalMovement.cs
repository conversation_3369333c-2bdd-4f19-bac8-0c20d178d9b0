using UnityEngine;

public class EllipticalMovement : MonoBehaviour
{
    [SerializeField]
    private EllipseData ellipseData; // 椭圆数据引用（可以为null）

    [Header("移动参数")]
    [SerializeField, Range(0.1f, 10f)]
    private float speed = 1f; // 目标移动速度

    [SerializeField, Range(0f, 360f)]
    private float startAngle = 0f; // 起始角度（度）

    [SerializeField]
    private bool clockwise = true; // 是否顺时针移动

    [SerializeField]
    private bool autoStart = true; // 是否自动开始移动

    [Header("加减速参数")]
    [SerializeField, Range(0.1f, 10f)]
    private float transitionTime = 2f; // 加速/减速时间

    [SerializeField]
    private AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1); // 过渡曲线

    [Header("朝向控制")]
    [SerializeField]
    private RotationMode rotationMode = RotationMode.FaceMovementDirection; // 朝向模式

    [SerializeField, Range(0f, 10f)]
    private float rotationSpeed = 5f; // 旋转速度

    [SerializeField]
    private Vector3 customDirection = Vector3.forward; // 自定义朝向

    [SerializeField, Range(-180f, 180f)]
    private float rotationOffset = 0f; // 朝向偏移角度

    [SerializeField]
    private bool smoothRotation = true; // 是否平滑旋转

    [SerializeField]
    private Transform lookAtTarget; // 看向目标

    [Header("停止后朝向设置")]
    [SerializeField]
    private bool faceTowardsCenterOnStop = false; // 停止后是否朝向圆心

    [SerializeField, Range(0.1f, 10f)]
    private float stopRotationSpeed = 3f; // 停止后朝向圆心的旋转速度

    // 移动状态枚举
    public enum MovementState
    {
        Stopped,        // 完全停止
        Accelerating,   // 加速中
        Running,        // 正常运行
        Decelerating,   // 减速中
        FacingCenter    // 停止后朝向圆心
    }

    // 朝向模式枚举
    public enum RotationMode
    {
        None,                    // 不改变朝向
        FaceMovementDirection,   // 面向移动方向
        FaceCenter,             // 面向椭圆中心
        FaceAwayFromCenter,     // 背离椭圆中心
        CustomDirection,        // 自定义方向
        LookAtTarget,           // 看向指定目标
        FaceTangent,            // 面向切线方向
        FaceRadial              // 面向径向（从中心指向当前位置）
    }

    // 私有变量
    private float currentAngle;
    private MovementState movementState = MovementState.Stopped;
    private Vector3 lastPosition;
    private Quaternion targetRotation;
    private float currentSpeed = 0f;
    private float transitionTimer = 0f;
    private bool isTransitioning = false;
    private bool isRotatingToCenter = false;
    private RotationMode originalRotationMode;
    private bool isInitialized = false; // 标记是否已初始化

    // 属性
    public float CurrentSpeed => currentSpeed;
    public MovementState State => movementState;
    public bool IsMoving => movementState != MovementState.Stopped && movementState != MovementState.FacingCenter;
    public float CurrentAngle => currentAngle;
    public bool IsRotatingToCenter => isRotatingToCenter;
    public EllipseData EllipseData => ellipseData;
    public bool IsInitialized => isInitialized;
    public bool HasEllipseData => ellipseData != null;

    // 事件
    public System.Action OnCompleteStopped;
    public System.Action OnCompletedCircle;
    public System.Action OnStartedFacingCenter;
    public System.Action OnFinishedFacingCenter;
    public System.Action OnEllipseDataSet; // 椭圆数据设置完成事件

    private void Start()
    {
        // 尝试初始化，如果椭圆数据为null则等待外部设置
        TryInitialize();
    }

    private void Update()
    {
        // 只有在有椭圆数据且已初始化时才更新
        if (HasEllipseData && isInitialized)
        {
            UpdateMovement();
            UpdateRotation();
        }
    }

    /// <summary>
    /// 尝试初始化组件（如果椭圆数据可用）
    /// </summary>
    private void TryInitialize()
    {
        if (HasEllipseData && !isInitialized)
        {
            Initialize();
        }
    }

    /// <summary>
    /// 初始化组件
    /// </summary>
    private void Initialize()
    {
        if (!HasEllipseData)
        {
            Debug.LogWarning("Cannot initialize EllipticalMovement: EllipseData is null", this);
            return;
        }

        currentAngle = startAngle;
        UpdatePosition();
        lastPosition = transform.position;
        originalRotationMode = rotationMode;
        CalculateTargetRotation();

        if (!smoothRotation)
        {
            transform.rotation = targetRotation;
        }

        movementState = MovementState.Stopped;
        currentSpeed = 0f;
        isInitialized = true;

        if (autoStart)
        {
            Run();
        }
    }

    /// <summary>
    /// 开始移动（带加速过程）
    /// </summary>
    public void Run()
    {
        if (!HasEllipseData)
        {
            Debug.LogWarning("Cannot run: EllipseData is not set", this);
            return;
        }

        if (!isInitialized)
        {
            TryInitialize();
        }

        if (movementState == MovementState.Running || movementState == MovementState.Accelerating)
        {
            return;
        }

        // 强制停止朝向圆心的过程并恢复原始朝向模式
        if (isRotatingToCenter || movementState == MovementState.FacingCenter || rotationMode == RotationMode.FaceCenter)
        {
            isRotatingToCenter = false;
            rotationMode = originalRotationMode;
        }

        if (movementState == MovementState.Decelerating)
        {
            float currentSpeedRatio = currentSpeed / speed;
            transitionTimer = currentSpeedRatio * transitionTime;
        }
        else
        {
            transitionTimer = 0f;
            currentSpeed = 0f;
        }

        movementState = MovementState.Accelerating;
        isTransitioning = true;
    }

    /// <summary>
    /// 停止移动（带减速过程）
    /// </summary>
    public void Stop()
    {
        if (!HasEllipseData)
        {
            Debug.LogWarning("Cannot stop: EllipseData is not set", this);
            return;
        }

        if (movementState == MovementState.Stopped || movementState == MovementState.Decelerating)
        {
            return;
        }

        if (movementState == MovementState.Accelerating)
        {
            float currentSpeedRatio = currentSpeed / speed;
            transitionTimer = (1f - currentSpeedRatio) * transitionTime;
        }
        else
        {
            transitionTimer = 0f;
        }

        movementState = MovementState.Decelerating;
        isTransitioning = true;
    }

    /// <summary>
    /// 停止移动并朝向圆心
    /// </summary>
    public void StopAndFaceCenter()
    {
        Stop();
        faceTowardsCenterOnStop = true;
    }

    /// <summary>
    /// 立即停止并开始朝向圆心
    /// </summary>
    public void StopImmediateAndFaceCenter()
    {
        StopImmediate();
        StartFacingCenter();
    }

    /// <summary>
    /// 开始朝向圆心的过程
    /// </summary>
    public void StartFacingCenter()
    {
        if (!HasEllipseData)
        {
            Debug.LogWarning("Cannot face center: EllipseData is not set", this);
            return;
        }

        if (isRotatingToCenter) return;

        originalRotationMode = rotationMode;
        rotationMode = RotationMode.FaceCenter;
        isRotatingToCenter = true;
        movementState = MovementState.FacingCenter;
        OnStartedFacingCenter?.Invoke();
    }

    /// <summary>
    /// 停止朝向圆心的过程
    /// </summary>
    public void StopFacingCenter()
    {
        if (!isRotatingToCenter) return;

        isRotatingToCenter = false;
        rotationMode = originalRotationMode;
        movementState = MovementState.Stopped;
    }

    /// <summary>
    /// 立即停止
    /// </summary>
    public void StopImmediate()
    {
        currentSpeed = 0f;
        transitionTimer = 0f;
        isTransitioning = false;
        movementState = MovementState.Stopped;
        OnCompleteStopped?.Invoke();
    }

    /// <summary>
    /// 切换运行状态
    /// </summary>
    public void ToggleMovement()
    {
        if (IsMoving)
        {
            Stop();
        }
        else
        {
            Run();
        }
    }

    /// <summary>
    /// 设置椭圆数据
    /// </summary>
    /// <param name="newEllipseData">新的椭圆数据</param>
    public void SetEllipseData(EllipseData newEllipseData)
    {
        if (ellipseData == newEllipseData)
        {
            return;
        }
        // 如果正在移动，先停止
        if (IsMoving)
        {
            StopImmediate();
        }

        ellipseData = newEllipseData;
        isInitialized = false; // 重置初始化状态

        if (HasEllipseData)
        {
            // 立即初始化
            Initialize();
            OnEllipseDataSet?.Invoke();
        }
    }

    /// <summary>
    /// 重置到起始位置
    /// </summary>
    public void ResetToStart()
    {
        if (!HasEllipseData)
        {
            Debug.LogWarning("Cannot reset: EllipseData is not set", this);
            return;
        }

        currentAngle = startAngle;
        UpdatePosition();
        lastPosition = transform.position;
        CalculateTargetRotation();
    }

    /// <summary>
    /// 设置当前角度
    /// </summary>
    /// <param name="angle">角度（度）</param>
    public void SetCurrentAngle(float angle)
    {
        if (!HasEllipseData)
        {
            Debug.LogWarning("Cannot set angle: EllipseData is not set", this);
            return;
        }

        float previousAngle = currentAngle;
        currentAngle = angle % 360f;
        if (currentAngle < 0) currentAngle += 360f;

        UpdatePosition();

        // 检查是否完成了一圈
        CheckCircleCompletion(previousAngle, currentAngle);
    }

    private void UpdateMovement()
    {
        UpdateSpeed();

        if (currentSpeed > 0.001f)
        {
            float previousAngle = currentAngle;
            float deltaAngle = currentSpeed * Time.deltaTime * 60f;

            if (!clockwise)
            {
                deltaAngle = -deltaAngle;
            }

            currentAngle += deltaAngle;

            if (currentAngle >= 360f)
            {
                currentAngle -= 360f;
                OnCompletedCircle?.Invoke();
            }
            else if (currentAngle < 0f)
            {
                currentAngle += 360f;
                OnCompletedCircle?.Invoke();
            }

            lastPosition = transform.position;
            UpdatePosition();
        }
    }

    private void UpdateSpeed()
    {
        switch (movementState)
        {
            case MovementState.Stopped:
            case MovementState.FacingCenter:
                currentSpeed = 0f;
                break;

            case MovementState.Accelerating:
                transitionTimer += Time.deltaTime;
                float accelerationProgress = Mathf.Clamp01(transitionTimer / transitionTime);
                float accelerationCurveValue = transitionCurve.Evaluate(accelerationProgress);

                currentSpeed = speed * accelerationCurveValue;

                if (accelerationProgress >= 1f)
                {
                    movementState = MovementState.Running;
                    isTransitioning = false;
                }
                break;

            case MovementState.Running:
                currentSpeed = speed;
                break;

            case MovementState.Decelerating:
                transitionTimer += Time.deltaTime;
                float decelerationProgress = Mathf.Clamp01(transitionTimer / transitionTime);
                float decelerationCurveValue = transitionCurve.Evaluate(1f - decelerationProgress);

                currentSpeed = speed * decelerationCurveValue;

                if (decelerationProgress >= 1f)
                {
                    currentSpeed = 0f;
                    movementState = MovementState.Stopped;
                    isTransitioning = false;
                    OnCompleteStopped?.Invoke();

                    if (faceTowardsCenterOnStop)
                    {
                        faceTowardsCenterOnStop = false;
                        StartFacingCenter();
                    }
                }
                break;
        }
    }

    private void UpdatePosition()
    {
        Vector3 position = ellipseData.CalculateEllipticalPosition(currentAngle);
        transform.position = position;
    }

    private void UpdateRotation()
    {
        CalculateTargetRotation();

        float currentRotationSpeed = isRotatingToCenter ? stopRotationSpeed : rotationSpeed;

        if (smoothRotation && currentRotationSpeed > 0)
        {
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation,
                currentRotationSpeed * Time.deltaTime);

            if (isRotatingToCenter && Quaternion.Angle(transform.rotation, targetRotation) < 1f)
            {
                transform.rotation = targetRotation;
                isRotatingToCenter = false;
                movementState = MovementState.Stopped;
                OnFinishedFacingCenter?.Invoke();
            }
        }
        else if (!smoothRotation)
        {
            transform.rotation = targetRotation;
            if (isRotatingToCenter)
            {
                isRotatingToCenter = false;
                movementState = MovementState.Stopped;
                OnFinishedFacingCenter?.Invoke();
            }
        }
    }

    private void CalculateTargetRotation()
    {
        Vector3 direction = Vector3.forward;
        Vector3 centerPosition = ellipseData.CenterPosition;

        switch (rotationMode)
        {
            case RotationMode.None:
                return;

            case RotationMode.FaceMovementDirection:
                direction = GetMovementDirection();
                break;

            case RotationMode.FaceCenter:
                direction = (centerPosition - transform.position).normalized;
                break;

            case RotationMode.FaceAwayFromCenter:
                direction = (transform.position - centerPosition).normalized;
                break;

            case RotationMode.CustomDirection:
                direction = customDirection.normalized;
                break;

            case RotationMode.LookAtTarget:
                if (lookAtTarget != null)
                {
                    direction = (lookAtTarget.position - transform.position).normalized;
                }
                break;

            case RotationMode.FaceTangent:
                direction = GetTangentDirection();
                break;

            case RotationMode.FaceRadial:
                direction = (transform.position - centerPosition).normalized;
                break;
        }

        // 如果有父节点且父节点有旋转，需要将方向转换到父节点的本地空间
        if (transform.parent != null)
        {
            direction = transform.parent.InverseTransformDirection(direction);
        }

        if (rotationOffset != 0)
        {
            Quaternion offset = Quaternion.AngleAxis(rotationOffset, Vector3.up);
            direction = offset * direction;
        }

        if (direction.magnitude > 0.01f)
        {
            targetRotation = Quaternion.LookRotation(direction);
        }
    }

    private void OnDrawGizmos()
    {
        //绘制direction
        if (HasEllipseData)
        {
            Gizmos.color = Color.green;
            Vector3 tangentDirection = GetTangentDirection();
            Gizmos.DrawLine(transform.position, transform.position + tangentDirection * 2f);
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(transform.position, ellipseData.CenterPosition);
        }
    }

    private Vector3 GetMovementDirection()
    {
        Vector3 currentPos = transform.position;
        Vector3 movementDir = (currentPos - lastPosition).normalized;

        // 如果移动方向太小或者刚开始移动，直接使用切线方向
        if (movementDir.magnitude < 0.01f || currentSpeed < 0.1f)
        {
            movementDir = GetTangentDirection();
        }

        return movementDir;
    }

    private void CheckCircleCompletion(float previousAngle, float currentAngle)
    {
        if (clockwise)
        {
            if (previousAngle > currentAngle && previousAngle > 270f && currentAngle < 90f)
            {
                OnCompletedCircle?.Invoke();
            }
        }
        else
        {
            if (previousAngle < currentAngle && previousAngle < 90f && currentAngle > 270f)
            {
                OnCompletedCircle?.Invoke();
            }
        }
    }

    /// <summary>
    /// 获取当前在椭圆上的切线方向
    /// </summary>
    /// <returns>切线方向向量</returns>
    public Vector3 GetTangentDirection()
    {
        if (!HasEllipseData) return Vector3.forward;

        float radians = currentAngle * Mathf.Deg2Rad;

        // 计算椭圆参数方程的导数（切线方向）
        float dx = -ellipseData.SemiMajorAxis * Mathf.Sin(radians);
        float dz = ellipseData.SemiMinorAxis * Mathf.Cos(radians);

        // 应用水平旋转（绕Y轴旋转）
        float rotationRadians = ellipseData.EllipseRotation * Mathf.Deg2Rad;
        float rotatedDx = dx * Mathf.Cos(rotationRadians) - dz * Mathf.Sin(rotationRadians);
        float rotatedDz = dx * Mathf.Sin(rotationRadians) + dz * Mathf.Cos(rotationRadians);

        // 应用倾斜旋转（绕Z轴旋转）
        float tiltRadians = ellipseData.EllipseTilt * Mathf.Deg2Rad;
        float finalDx = rotatedDx * Mathf.Cos(tiltRadians);
        float finalDy = rotatedDx * Mathf.Sin(tiltRadians);
        float finalDz = rotatedDz; // Z坐标不受绕Z轴旋转影响

        Vector3 tangent = new Vector3(finalDx, finalDy, finalDz).normalized;

        return clockwise ? tangent : -tangent;
    }

    /// <summary>
    /// 获取椭圆上指定角度的位置
    /// </summary>
    /// <param name="angle">角度（度）</param>
    /// <returns>世界坐标位置</returns>
    public Vector3 GetPositionAtAngle(float angle)
    {
        if (!HasEllipseData) return Vector3.zero;
        return ellipseData.CalculateEllipticalPosition(angle);
    }

    /// <summary>
    /// 设置自定义朝向
    /// </summary>
    /// <param name="direction">朝向方向</param>
    public void SetCustomDirection(Vector3 direction)
    {
        customDirection = direction.normalized;
    }

    /// <summary>
    /// 设置看向目标
    /// </summary>
    /// <param name="target">目标Transform</param>
    public void SetLookAtTarget(Transform target)
    {
        lookAtTarget = target;
    }

    /// <summary>
    /// 立即设置朝向（无平滑过渡）
    /// </summary>
    public void SetRotationImmediate()
    {
        if (!HasEllipseData) return;

        CalculateTargetRotation();
        transform.rotation = targetRotation;
    }

    /// <summary>
    /// 设置目标速度
    /// </summary>
    /// <param name="newSpeed">新的目标速度</param>
    /// <param name="immediate">是否立即应用</param>
    public void SetSpeed(float newSpeed, bool immediate = false)
    {
        speed = Mathf.Max(0.1f, newSpeed);

        if (immediate && movementState == MovementState.Running)
        {
            currentSpeed = speed;
        }
    }

    private void OnValidate()
    {
        speed = Mathf.Max(0.1f, speed);
        rotationSpeed = Mathf.Max(0f, rotationSpeed);
        stopRotationSpeed = Mathf.Max(0.1f, stopRotationSpeed);
        transitionTime = Mathf.Max(0.1f, transitionTime);
        startAngle = Mathf.Clamp(startAngle, 0f, 360f);
        rotationOffset = Mathf.Clamp(rotationOffset, -180f, 180f);

        customDirection = customDirection.normalized;
    }
}