using Core;
using Cysharp.Threading.Tasks;
using SuperScrollView;
using UIFramework;
using UnityEngine;
using UnityEngine.EventSystems;

namespace GameUI
{
	public partial class UIReddotBaseItem : UIItem
	{
		protected ClickEventListener _clickEventListener;
		//初始化（具体参数各系统自行添加）
		public override void Init(params object[] param)
		{
			_reddotId = (int)param[0];
			ReddotMgr.Ins.BindReddot(_reddotId, this.OnReddotUpdate);
		}
		
		public override void OnUnInit()
		{
			base.OnUnInit();
			ReddotMgr.Ins.UnBindReddot(_reddotId, this.OnReddotUpdate);
			if (_clickEventListener)
			{
				_clickEventListener.DestroyEx();
				_clickEventListener = null;
			}
		}

		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
		}
		
		
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			if (_clickEventListener)
			{
				_clickEventListener.DestroyEx();
				_clickEventListener = null;
			}

			
			if (Cfg.Reddot.GetData(_reddotId).disappear == 1)
			{
				_clickEventListener = transform.parent.gameObject.AddComponent<ClickEventListener>();
				_clickEventListener.SetClickEventHandler(this.OnParentNodeClick);
			}
			
		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{
			var cfg = Cfg.Reddot.GetData(_reddotId);
			CanvasGroup canvasGroup = transform.GetComponent<CanvasGroup>();
			if (canvasGroup != null)
			{
				canvasGroup.alpha = cfg.transparency / 255f;
			}
			
			var rectTransform = transform as RectTransform;
			transform.localScale = Vector3.one * cfg.size;
			if (rectTransform != null)
				rectTransform.anchoredPosition = new Vector2(cfg.place[0], cfg.place[1]);
			ReddotMgr.Ins.GetReddotState(_reddotId, out bool state, out int count);
			this.gameObject.SetActiveEx(state);
		}

		public virtual void OnReddotUpdate(ReddotObject obj)
		{
			//DLogger.Log("UIReddotNormalItem.OnReddotUpdate");
			if (gameObject != null) 
				gameObject.SetActiveEx(obj.State);
		}

		/// <summary>
		/// 监听父节点的点击事件，如果红点配置了点击消失,此方法处理这种情况
		/// 红点必须是叶子节点
		/// </summary>
		/// <param name="eventData"></param>
		private void OnParentNodeClick(GameObject eventData)
		{
			if (!ReddotMgr.Ins.IsLeafReddot(_reddotId))
			{
				//非叶子节点不响应
				DLogger.Warning("非叶子节点点击消失不生效");
				return;
			}

			if (Cfg.Reddot.GetData(_reddotId).disappear == 1)
			{
				ReddotMgr.Ins.UpdateReddotState(_reddotId, false);
			}
			
		}
	}
}
