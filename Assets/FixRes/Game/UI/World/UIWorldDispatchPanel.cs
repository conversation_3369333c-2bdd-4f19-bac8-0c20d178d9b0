using Core;
using Cysharp.Threading.Tasks;
using Game;
using Game.Network;
using GameMain;
using Packet;
using SuperScrollView;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UIFramework;
using UnityEngine;

namespace GameUI
{
    public partial class UIWorldDispatchPanel : UIPanel
    {
        GridData _gridData;
        [SerializeField]
        UIWorldTopInfoItem _topInfoItem;
        [SerializeField]
        UIWorldFloatItem _worldFloatItem;

        private WLoopGridView mAwardList;
        private BigMapDispatchInfo mDispatchInfo;
        private DispatchTask mTaskConfig;
        private List<(int itemId, int cnt)> mAwrads;

        private Timer mTimer;
        private int mEndTime;
        private int LifeTime => Mathf.Max(0, (int)(mEndTime - TimeUtils.GetServerTime()));

        protected TMPro.TextMeshProUGUI txt_title_name;

        public override async UniTask OnLoadComplete()
        {
            _topInfoItem.InitComponent();
            _worldFloatItem.InitComponent();

            //主动加载异步资源
            await _topInfoItem.OnLoadComplete();

            await _worldFloatItem.OnLoadComplete();

            mAwardList = await CreateLoopGrid<UIComBaseItem>(sv_award_list, 0, OnRefreshAwardItem);

            await base.OnLoadComplete();
        }

        private void OnRefreshAwardItem(WLoopGridView view, int index, WLoopItem baseItem)
        {
            UIComBaseItem item = baseItem as UIComBaseItem;
            var award = mAwrads[index];
            item.SetData(award.itemId, award.cnt);
        }

        public override void AfterInitComponent()
        {
            _topInfoItem.AfterInitComponent();
            _worldFloatItem.AfterInitComponent();

            txt_title_name = _topInfoItem.transform.Find("img_title_bg/txt_title_name").GetComponent<TMPro.TextMeshProUGUI>();

            base.AfterInitComponent();
        }

        public override void OnStart()
        {
            _gridData = GetPanelData<GridData>(0);
            _topInfoItem.RefreshView(_gridData);

            RefreshView();

            RefreshFloatItem();

            bool isSelf = UserMgr.Ins.IsMyself(mDispatchInfo.OwnerId);
            bool isLeague = LeagueMgr.Ins.IsLeagueMember(mDispatchInfo.OwnerId);

            if (!isSelf && !isLeague)
                DispatchNetwork.Ins.ReqDispatchCheck(mDispatchInfo.Id);
        }

        void RefreshFloatItem()
        {
            _worldFloatItem.RefreshView(_gridData);
            _worldFloatItem.RefreshViewAsync<UIWorldFloatSubItem>().Forget();
        }

        private void RefreshView()
        {
            StopTimer();

            mDispatchInfo = _gridData.DispatchInfo;
            mTaskConfig = Cfg.DispatchTask.GetData(mDispatchInfo.TaskCfgId);

            bool isSelf = UserMgr.Ins.IsMyself(mDispatchInfo.OwnerId);
            bool isLeague = LeagueMgr.Ins.IsLeagueMember(mDispatchInfo.OwnerId);

            mEndTime = mDispatchInfo.StartTime + mTaskConfig.time;
            int maxLiefTime = (isSelf || isLeague) ? mTaskConfig.time : Cfg.DispathConst.GetData(14).value1;
            int lifeTime = LifeTime;
            bool isShowCountdown = lifeTime > 0 && lifeTime <= maxLiefTime;
            bool isShowInfo = isSelf || isLeague || isShowCountdown || lifeTime <= 0;

            rect_award_root.SetActiveEx(isShowInfo);
            rect_hide_root.SetActiveEx(!isShowInfo);
            rect_countdown.SetActiveEx(isShowCountdown);

            if (isShowInfo)
            {
                if (isSelf)
                {
                    mAwrads = new List<(int itemId, int cnt)>();
                    for (int i = 0; i < mTaskConfig.reward.Length; i++)
                    {
                        int[] award = mTaskConfig.reward[i];
                        mAwrads.Add((award[0], award[1]));
                    }
                    // TODO Lookchard 多语言
                    txt_title.text = "任务奖励";
                }
                else if (isLeague)
                {
                    mAwrads = DispatchMgr.Ins.GetHelpAwards(mDispatchInfo.TaskCfgId);
                    txt_title.text = "协助奖励";
                }
                else
                {
                    mAwrads = DispatchMgr.Ins.GetPlunderAwards(mDispatchInfo.TaskCfgId);
                    txt_title.text = "掠夺奖励";
                }

                mAwardList.SetListItemCount(mAwrads.Count);
                mAwardList.RefreshAllShownItem();

                if (isShowCountdown)
                {
                    mTimer = Timer.Register(lifeTime, OnCountDownComplete, OnCountDownUpdate);
                }
            }
            else
            {
                txt_title_name.text = "??????"; // TODO Lookchard  多语言
            }
        }

        private void OnCountDownUpdate(float dt)
        {
            txt_countdown_time.text = TimeUtils.GetTimeStrHMS(LifeTime);
        }

        private void OnCountDownComplete()
        {
            RefreshView();
            RefreshFloatItem();
        }

        private void StopTimer()
        {
            mTimer?.Cancel();
            mTimer = null;
        }

        public override void OnClose()
        {
            base.OnClose();

            StopTimer();
        }
    }
}
