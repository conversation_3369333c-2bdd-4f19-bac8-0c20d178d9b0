using Core;
using Cysharp.Threading.Tasks;
using Game;
using Game.Network;
using GameMain;
using Packet;
using SuperScrollView;
using System.Collections.Generic;
using System.Linq;
using UIFramework;
using UnityEngine;

namespace GameUI
{
	public partial class UIWorldDispatchTaskPanel : UIPanel
	{
        private const int MaxHeroCnt = 3;
        private const int MaxConditionCnt = 4;

		private UIWorldDispatchSlotHeroItem[] mHeroItems = new UIWorldDispatchSlotHeroItem[MaxHeroCnt];

        private List<DispatchHeroInfo> mHeroInfos;
        private DispatchHeroInfo[] mSelectedHeros = new DispatchHeroInfo[MaxHeroCnt];
        private HashSet<int> mSelectedHeroIds = new HashSet<int>();

        private BigMapDispatchInfo mDispatchInfo;
        private DispatchTask mConfig;

        private WLoopGridView mAwardList;
        private WLoopGridView mHeroList;
        private UIWorldDispatchTaskCondtionItem[] mCondtionItems = new UIWorldDispatchTaskCondtionItem[MaxConditionCnt];

        private List<DispatchConditionInfo> mConditions;
        private List<DispatchConditionInfo> mConditionsSort = new List<DispatchConditionInfo>();

        private Dictionary<DispatchConditionType, int> mCondistionShowSort = new Dictionary<DispatchConditionType, int>()
        {
            { DispatchConditionType.Quality, 1},
            { DispatchConditionType.Level, 2},
            { DispatchConditionType.Star, 3},
            { DispatchConditionType.Faction, 4},
        };

        public override async UniTask OnLoadComplete()
		{
            //主动加载异步资源

            for (int i = 0; i < mHeroItems.Length; i++)
            {
                mHeroItems[i] = await AddSubItem<UIWorldDispatchSlotHeroItem>(UIItemDefine.WorldDispatchSlotHeroItem, rect_hero_groups);
                mHeroItems[i].SetData(null, OnSelectedHeroDel);
            }

            mAwardList = await CreateLoopGrid<UIComBaseItem>(sv_award_list, 0, OnRefreshAwardItem);
            mHeroList = await CreateLoopGrid<UIWorldDispatchHeroItem>(sv_hero_list, 0, OnRefreshHeroItem);

            await base.OnLoadComplete();
		}

        private void OnSelectedHeroDel(DispatchHeroInfo heroInfo)
        {
            for (int i = 0; i < mSelectedHeros.Length; i++)
            {
                if (mSelectedHeros[i] == heroInfo)
                {
                    mSelectedHeros[i] = null;
                    mSelectedHeroIds.Remove(heroInfo.heroInfo.id);

                    mHeroList.RefreshAllShownItem();

                    RefreshConditionItems();
                    break;
                }
            }
        }

        private void SetSelectedHero(int index, DispatchHeroInfo heroInfo)
        {
            if (mSelectedHeroIds.Contains(heroInfo.heroInfo.id))
                return;

            mHeroItems[index].SetData(heroInfo, OnSelectedHeroDel);
            mSelectedHeros[index] = heroInfo;
            mSelectedHeroIds.Add(heroInfo.heroInfo.id);

            mHeroList.RefreshAllShownItem();

            RefreshConditionItems();
        }

        private void OnRefreshHeroItem(WLoopGridView view, int index, WLoopItem baseItem)
        {
            UIWorldDispatchHeroItem item = baseItem as UIWorldDispatchHeroItem;
            DispatchHeroInfo heroInfo = mHeroInfos[index];
            bool isSelected = mSelectedHeroIds.Contains(heroInfo.heroInfo.id);
            item.SetData(heroInfo, isSelected, OnHeroItemClick);
        }

        private void OnHeroItemClick(DispatchHeroInfo heroInfo)
        {
            if (mSelectedHeroIds.Contains(heroInfo.heroInfo.id))
            {
                for (int i = 0; i < mSelectedHeros.Length; i++)
                {
                    if (mSelectedHeros[i] == heroInfo)
                    {
                        OnSelectedHeroDel(heroInfo);
                        mHeroItems[i].SetData(null, OnSelectedHeroDel);
                        break;
                    }
                }
            }
            else
            {
                for (int i = 0; i < mSelectedHeros.Length; i++)
                {
                    if (mSelectedHeros[i] == null)
                    {
                        SetSelectedHero(i, heroInfo);
                        break;
                    }
                }
            }
        }

        private void OnRefreshAwardItem(WLoopGridView view, int index, WLoopItem baseItem)
        {
            UIComBaseItem item = baseItem as UIComBaseItem;
            int[] award = mConfig.reward[index];
            item.SetData(award[0], award[1]);
        }

        public override void AfterInitComponent()
		{
            //注册按钮以及其它初始化

            btn_auto_dispatch.AddOnClick(OnClickBtnAutoDispatch);
            btn_dispatch.AddOnClick(OnClickBtnDispatch);

            base.AfterInitComponent();
		}

        private void OnClickBtnDispatch()
        {
           for (int i = 0; i < mConditionsSort.Count; i++)
            {
                UIWorldDispatchTaskCondtionItem item = mCondtionItems[i];
                if (!item.IsMeet)
                    return;
            }

            DispatchNetwork.Ins.ReqDispatch((uint)mDispatchInfo.TaskId, mSelectedHeroIds.ToList(), mDispatchInfo.Pos);
            Close();
        }

        private void OnClickBtnAutoDispatch()
        {
            List<DispatchHeroInfo> selectedHeros = new List<DispatchHeroInfo>();
            HashSet<int> selectedHeroIdFlag = new HashSet<int>();
            Dictionary<DispatchConditionType, int> needMeetCntFlag = new Dictionary<DispatchConditionType, int>();
            Dictionary<DispatchConditionType, List<DispatchHeroInfo>> heroByConditionDict = new Dictionary<DispatchConditionType, List<DispatchHeroInfo>>();
            for (int i = mHeroInfos.Count - 1; i >= 0; i--)
            {
                var hero = mHeroInfos[i];
                foreach (var conditionType in hero.meetCondistions)
                {
                    if (!heroByConditionDict.ContainsKey(conditionType))
                        heroByConditionDict[conditionType] = new List<DispatchHeroInfo>();
                    heroByConditionDict[conditionType].Add(hero);
                }
            }

            for (int i = 0; i < mConditions.Count; i++)
            {
                var condition = mConditions[i];
                needMeetCntFlag[condition.conditionType] = condition.count;
            }

            for (int i = 0; i < mConditions.Count; i++)
            {
                List<DispatchHeroInfo> intersectionHeros = null;

                foreach (var kvp in needMeetCntFlag)
                {
                    if (kvp.Value <= 0)
                        continue;

                    if (intersectionHeros == null)
                        intersectionHeros = heroByConditionDict[kvp.Key];
                    else
                        intersectionHeros = intersectionHeros.Intersect(heroByConditionDict[kvp.Key]).ToList();
                }

                if (intersectionHeros != null)
                {
                    intersectionHeros.Sort(SortHeros);
                    int cnt = Mathf.Min(MaxHeroCnt - selectedHeros.Count, intersectionHeros.Count);
                    for (int j = 0; j < intersectionHeros.Count; j++)
                    {
                        if (cnt <= 0)
                            break;

                        var heroInfo = intersectionHeros[j];

                        if (selectedHeroIdFlag.Contains(heroInfo.heroInfo.id))
                            continue;

                        selectedHeros.Add(heroInfo);
                        selectedHeroIdFlag.Add(heroInfo.heroInfo.id);

                        foreach (var conditionType in heroInfo.meetCondistions)
                        {
                            if (needMeetCntFlag.ContainsKey(conditionType))
                            {
                                needMeetCntFlag[conditionType]--;
                                if (needMeetCntFlag[conditionType] <= 0)
                                    needMeetCntFlag.Remove(conditionType);
                            }
                        }

                        if (needMeetCntFlag.Count <= 0)
                            break;

                        cnt++;
                    }
                }
            }

            if (needMeetCntFlag.Count > 0)
            {
                UnmeetTips();
                return;
            }

            for (int i = 0; i < mHeroItems.Length; i++)
            {
                mHeroItems[i].SetData(null, OnSelectedHeroDel);
                mSelectedHeros[i] = null;
            }
            mSelectedHeroIds.Clear();

            for (int i = 0; i < selectedHeros.Count; i++)
            {
                DispatchHeroInfo heroInfo = selectedHeros[i];
                mHeroItems[i].SetData(heroInfo, OnSelectedHeroDel);
                mSelectedHeros[i] = heroInfo;
                mSelectedHeroIds.Add(heroInfo.heroInfo.id);
            }

            mHeroList.RefreshAllShownItem();
            RefreshConditionItems();
        }

        private int SortHeros(DispatchHeroInfo a, DispatchHeroInfo b)
        {
            // 倒序
            return a.power.CompareTo(b.power);
        }

        private void UnmeetTips()
        {
            TipsMgr.Ins.ShowTips(1000463); // TODO Lookchard 多语言
        }

        public override void OnStart()
        {
            base.OnStart();

            var gridData = GetPanelData<GridData>(0);
            mDispatchInfo = gridData.DispatchInfo;
            mConfig = Cfg.DispatchTask.GetData(mDispatchInfo.TaskCfgId);

            mHeroInfos = DispatchMgr.Ins.GetTaskCanSelectHeros(mDispatchInfo.TaskCfgId);
            mConditions = DispatchMgr.Ins.GetTaskConditions(mDispatchInfo.TaskCfgId);

            for (int i = 0; i < mConditions.Count; i++)
                mConditionsSort.Add(mConditions[i]);
            mConditionsSort.Sort(ConditionsSort);

            RefreshViewAsync().Forget();
        }

        private int ConditionsSort(DispatchConditionInfo a, DispatchConditionInfo b)
        {
            return mCondistionShowSort[a.conditionType].CompareTo(mCondistionShowSort[b.conditionType]);
        }

        private void RefreshConditionItems()
        {
            Dictionary<DispatchConditionType, int> meetCntFlag = new Dictionary<DispatchConditionType, int>();

            for (int i = 0; i < mSelectedHeros.Length; i++)
            {
                DispatchHeroInfo heroInfo = mSelectedHeros[i];
                if (heroInfo == null)
                    continue;

                foreach (var conditionType in heroInfo.meetCondistions)
                {
                    if (!meetCntFlag.ContainsKey(conditionType))
                        meetCntFlag[conditionType] = 1;
                    else
                        meetCntFlag[conditionType]++;
                }
            }

            for (int i = 0; i < mConditionsSort.Count; i++)
            {
                DispatchConditionInfo info = mConditionsSort[i];
                UIWorldDispatchTaskCondtionItem item = mCondtionItems[i];
                meetCntFlag.TryGetValue(info.conditionType, out int meetCnt);
                item.RefreshProgress(meetCnt);
            }
        }

        private async UniTask RefreshViewAsync()
        {
            mAwardList.SetListItemCount(mConfig.reward.Length);
            mAwardList.RefreshAllShownItem();

            mHeroList.SetListItemCount(mHeroInfos.Count);
            mHeroList.RefreshAllShownItem();

            for (int i = 0; i < mCondtionItems.Length; i++)
            {
                if (mCondtionItems[i] != null)
                    mCondtionItems[i].SetActiveEx(false);
            }

            for (int i = 0; i < mConditionsSort.Count; i++)
            {
                UIWorldDispatchTaskCondtionItem item = mCondtionItems[i];
                if ( item == null)
                {
                    item = await AddSubItem<UIWorldDispatchTaskCondtionItem>(UIItemDefine.WorldDispatchTaskCondtionItem, rect_conditions);
                    mCondtionItems[i] = item;
                }
                item.SetActiveEx(true);
                item.SetData(mConditionsSort[i]);
            }

            txt_title.text = Localization.GetValue(mConfig.dec);
            txt_time.text = TimeUtils.GetTimeStrHMS(mConfig.time);

            await img_task_quality.AsyncSetAtlasSprite(Localization.currentLanguage, UIHelper.GetDispatchTaskQualityImg2(mConfig.quality));
        }

        public override void OnClose()
        {
            base.OnClose();

            EventManager.Ins.DispatchEvent(UIEventType.OnCloseAllWorldPanel);
        }
	}
}
