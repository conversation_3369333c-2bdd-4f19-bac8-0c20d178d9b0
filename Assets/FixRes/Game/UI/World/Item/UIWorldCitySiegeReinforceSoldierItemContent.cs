using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIWorldCitySiegeReinforceSoldierItem : UIItem
	{
		protected TMPro.TextMeshProUGUI txt_soldier_name;
		protected TMPro.TextMeshProUGUI txt_soldier_count;
		public override void InitComponent()
		{
			txt_soldier_name = transform.Find("txt_soldier_name").GetComponent<TMPro.TextMeshProUGUI>();
			txt_soldier_count = transform.Find("txt_soldier_count").GetComponent<TMPro.TextMeshProUGUI>();
		}
	}
}
