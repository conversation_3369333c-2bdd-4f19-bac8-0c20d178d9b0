using System;
using Core;
using Cysharp.Threading.Tasks;
using Game.Framework;
using GameMain;
using SuperScrollView;
using UIFramework;
using UnityEngine;

namespace GameUI
{
    public partial class UIWorldFormationSelectHeroItem : WLoopItem
    {
        [SerializeField]
        UIBattleHeroSelectItem _selectHeroItem;

        WImage[] _heroIconImages;

        Color[] _imageColors;
        Color grayColor = new Color(152/255f, 143/255f, 140/255f);
        Material grayMaterail;

        bool _isSelect;

        const string GRAY_MATERIAL_PATH = "Materials/UI/UIGray2.mat";
        //初始化（具体参数各系统自行添加）
        public override void Init(params object[] param)
        {

        }
        //主动加载异步资源
        public override async UniTask OnLoadComplete()
        {
            //grayMaterail = await GameResMgr.Ins.LoadAssetAsync<Material>(GRAY_MATERIAL_PATH);
            _selectHeroItem.InitComponent();
            await _selectHeroItem.OnLoadComplete();
        }

        public override void OnUnInit()
        {
            base.OnUnInit();
            /*if (grayMaterail != null)
            {
                GameResMgr.Ins.UnloadAsset(GRAY_MATERIAL_PATH);
                grayMaterail = null;
            }*/
        }

        //注册按钮以及其它初始化
        public override void AfterInitComponent()
        {
            
            _selectHeroItem.AfterInitComponent();

            _heroIconImages = _selectHeroItem.HeroIconItem.GetComponentsInChildren<WImage>(true);
            _imageColors = new Color[_heroIconImages.Length];
            for (int i = 0; i < _heroIconImages.Length; i++)
            {
                _imageColors[i] = _heroIconImages[i].color;
            }
        }
        //刷新界面
        public override void RefreshView(params object[] data)
        {

        }
        public void SetData(HeroInfo info)
        {
            _selectHeroItem.ItemIndex = ItemIndex;
            _selectHeroItem.SetData(info);
            int ownTroopIndex = TroopMgr.Ins.GetOwnTroopIndex(info.id);
            if (ownTroopIndex == 0)
            {
                go_troop_index.SetActiveEx(false);
            }
            else
            {
                go_troop_index.SetActiveEx(true);
                txt_own_troop.text = ownTroopIndex.ToString("D2");
            }

            if (_isSelect)
            {
                go_lock.SetActiveEx(false);
                SetHeroIconGray(true);
                _selectHeroItem.SetSelect(true);
            }
            else
            {
                bool isBusy = false;
                if (ownTroopIndex != 0)
                {
                    TroopInfo troopInfo = TroopMgr.Ins.GetTroopInfo(ownTroopIndex);
                    if (troopInfo != null)
                    {
                        isBusy = true;
                    }
                }

                go_lock.SetActiveEx(isBusy);
                SetHeroIconGray(isBusy);
                _selectHeroItem.SetSelect(false);
            }
        }

        public void SetSelect(bool isSelect)
        {
            _isSelect = isSelect;
        }

        void SetHeroIconGray(bool isGray)
        {
            for (int i = 0; i < _heroIconImages.Length; i++)
            {
                _heroIconImages[i].color = isGray ? grayColor : _imageColors[i];

            }

            _selectHeroItem.HeroIconItem.SetTextLevelColor(isGray ? Color.gray : Color.white * 0.96f);
        }

        public void AddClick(Action<WLoopItem> onClick)
        {
            _selectHeroItem.AddClick(onClick);
        }
    }
}
