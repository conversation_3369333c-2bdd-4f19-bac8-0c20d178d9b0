using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIWorldDispatchTaskCondtionItem : WLoopItem
	{
		protected WImage img_bg;
		protected RectTransform rect_content;
		protected RectTransform rect_quality;
		protected WImage img_quality;
		protected RectTransform rect_level;
		protected TMPro.TextMeshProUGUI txt_level;
		protected RectTransform rect_faction;
		protected WImage img_faction;
		protected RectTransform rect_star;
		protected TMPro.TextMeshProUGUI txt_progress;
		public override void InitComponent()
		{
			img_bg = transform.Find("img_bg").GetComponent<WImage>();
			rect_content = transform.Find("rect_content").GetComponent<RectTransform>();
			rect_quality = transform.Find("rect_content/rect_quality").GetComponent<RectTransform>();
			img_quality = transform.Find("rect_content/rect_quality/img_quality").GetComponent<WImage>();
			rect_level = transform.Find("rect_content/rect_level").GetComponent<RectTransform>();
			txt_level = transform.Find("rect_content/rect_level/txt_level").GetComponent<TMPro.TextMeshProUGUI>();
			rect_faction = transform.Find("rect_content/rect_faction").GetComponent<RectTransform>();
			img_faction = transform.Find("rect_content/rect_faction/img_faction").GetComponent<WImage>();
			rect_star = transform.Find("rect_content/rect_star").GetComponent<RectTransform>();
			txt_progress = transform.Find("rect_content/txt_progress").GetComponent<TMPro.TextMeshProUGUI>();
		}
	}
}
