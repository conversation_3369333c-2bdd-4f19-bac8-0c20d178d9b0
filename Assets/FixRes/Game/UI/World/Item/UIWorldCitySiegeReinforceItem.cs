using System;
using Core;
using Cysharp.Threading.Tasks;
using Game;
using SuperScrollView;
using UIFramework;
using UnityEngine;
using GameMain;

namespace GameUI
{
    public partial class UIWorldCitySiegeReinforceItem : LoopListViewItem2
    {
        [SerializeField] private UIHeroIconItem[] _heroIconItems;

        [SerializeField] private UIComHeadItem _headItem;

        private FortressDefenderTroop _defenderTroop;
        private int _cityId;
        private Action<ulong> _onClickExpand;
        //初始化（具体参数各系统自行添加）
        public override void Init(params object[] param)
        {

        }
        //主动加载异步资源
        public override async UniTask OnLoadComplete()
        {
            for (int i = 0; i < _heroIconItems.Length; i++)
            {
                await AddSubItem<UIHeroIconItem>(_heroIconItems[i].gameObject);
            }
            await AddSubItemWithoutPos<UIComHeadItem>(_headItem.gameObject);
        }
        //注册按钮以及其它初始化
        public override void AfterInitComponent()
        {
            btn_repatriate.AddOnClick(OnClickBtnRepatriate);
            btn_expand.AddOnClick(OnClickBtnExpand);
        }

        //刷新界面
        public override void RefreshView(params object[] data)
        {

        }

        public void SetData(int cityId, FortressDefenderTroop defenderTroop, Action<ulong> onClickExpand)
        {
            _cityId = cityId;
            _defenderTroop = defenderTroop;
            _onClickExpand = onClickExpand;

            txt_role_name.text = defenderTroop.userInfo.RoleName;
            txt_troop_power.text = defenderTroop.power.ToString();
            for (int i = 0; i < _heroIconItems.Length; i++)
            {
                var heroIconItem = _heroIconItems[i];
                if (i >= defenderTroop.heroList.Count)
                {
                    heroIconItem.SetActiveEx(false);
                }
                else
                {
                    heroIconItem.SetActiveEx(true);
                    heroIconItem.SetData(defenderTroop.heroList[i]);
                }
            }

            CheckShowBtnRepatriate();
        }

        void CheckShowBtnRepatriate()
        {
            bool value = UserMgr.Ins.IsMyself(_defenderTroop.userInfo.role_id) || LeagueUtils.GetPermitLv(LeagueMgr.Ins.PosId) >= 4;
            btn_repatriate.SetActiveEx(value);
        }

        private void OnClickBtnRepatriate()
        {
            WorldCitySiegeMgr.Ins.SendReqKickDefender(_cityId, _defenderTroop.userInfo.role_id);
        }

        private void OnClickBtnExpand()
        {
            _onClickExpand?.Invoke(_defenderTroop.userInfo.role_id);
        }
    }
}
