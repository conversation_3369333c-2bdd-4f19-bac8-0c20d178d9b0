using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using GameMain;
using SuperScrollView;
using UIFramework;
using UnityEngine;

namespace GameUI
{
    public partial class UIWorldMarchCreateTroopItem : WLoopItem
    {
        int _troopIndex;
        Dictionary<int, Troop> _troopData;
        GridData _gridData;
        PathInfo _pathInfo;
        //初始化（具体参数各系统自行添加）
        public override void Init(params object[] param)
        {

        }
        //主动加载异步资源
        public override async UniTask OnLoadComplete()
        {


        }
        //注册按钮以及其它初始化
        public override void AfterInitComponent()
        {
            btn_create_troop.AddOnClick(OnClickCreateTroop);
        }

        //刷新界面
        public override void RefreshView(params object[] data)
        {

        }

        public void SetData(int troopIndex, Dictionary<int, Troop> troopData, GridData gridData, PathInfo pathInfo)
        {
            _troopIndex = troopIndex;
            _troopData = troopData;
            _gridData = gridData;
            _pathInfo = pathInfo;
        }

        private void OnClickCreateTroop()
        {
            TroopMgr.Ins.OpenEditScene(false, _troopIndex, _troopData, _gridData, _pathInfo).Forget();
        }
    }
}
