using UnityEngine;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIWorldComposePanel : UIPanel
	{
		protected RectTransform rect_root;
		protected RectTransform rect_bg;
		protected UIButton btn_full;
		protected RectTransform rect_content;
		protected RectTransform rect_troopitems;
		public override void InitComponent()
		{
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			rect_bg = transform.Find("rect_root/rect_bg").GetComponent<RectTransform>();
			btn_full = transform.Find("rect_root/rect_bg/btn_full").GetComponent<UIButton>();
			rect_content = transform.Find("rect_root/rect_content").GetComponent<RectTransform>();
			rect_troopitems = transform.Find("rect_root/rect_content/rect_troopitems").GetComponent<RectTransform>();
			base.InitComponent();
		}
		public override Transform GetAniRoot()
		{
			return rect_root;
		}
	}
}
