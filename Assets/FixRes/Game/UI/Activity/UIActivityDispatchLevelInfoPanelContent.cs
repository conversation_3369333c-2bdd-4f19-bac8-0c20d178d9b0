using UnityEngine;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIActivityDispatchLevelInfoPanel : UIPanel
	{
		protected UIButton btn_close;
		protected RectTransform rect_root;
		protected RectTransform rect_start_level;
		protected TMPro.TextMeshProUGUI txt_cur_start_level;
		protected WImage wimg_start_level_next;
		protected TMPro.TextMeshProUGUI txt_next_start_level;
		protected RectTransform rect_info;
		protected RectTransform rect_task_count;
		protected TMPro.TextMeshProUGUI txt_cur_task_count;
		protected WImage wimg_task_next;
		protected TMPro.TextMeshProUGUI txt_next_task_count;
		protected RectTransform rect_upgrade;
		protected TMPro.TextMeshProUGUI txt_upgrade_title;
		protected TMPro.TextMeshProUGUI txt_upgrade_condition;
		protected Slider slider_upgrade_condition;
		protected TMPro.TextMeshProUGUI txt_slider_upgrade_condition;
		public override void InitComponent()
		{
			btn_close = transform.Find("btn_close").GetComponent<UIButton>();
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			rect_start_level = transform.Find("rect_root/rect_start_level").GetComponent<RectTransform>();
			txt_cur_start_level = transform.Find("rect_root/rect_start_level/txt_cur_start_level").GetComponent<TMPro.TextMeshProUGUI>();
			wimg_start_level_next = transform.Find("rect_root/rect_start_level/wimg_start_level_next").GetComponent<WImage>();
			txt_next_start_level = transform.Find("rect_root/rect_start_level/txt_next_start_level").GetComponent<TMPro.TextMeshProUGUI>();
			rect_info = transform.Find("rect_root/rect_info").GetComponent<RectTransform>();
			rect_task_count = transform.Find("rect_root/rect_info/rect_task_count").GetComponent<RectTransform>();
			txt_cur_task_count = transform.Find("rect_root/rect_info/rect_task_count/txt_cur_task_count").GetComponent<TMPro.TextMeshProUGUI>();
			wimg_task_next = transform.Find("rect_root/rect_info/rect_task_count/wimg_task_next").GetComponent<WImage>();
			txt_next_task_count = transform.Find("rect_root/rect_info/rect_task_count/txt_next_task_count").GetComponent<TMPro.TextMeshProUGUI>();
			rect_upgrade = transform.Find("rect_root/rect_upgrade").GetComponent<RectTransform>();
			txt_upgrade_title = transform.Find("rect_root/rect_upgrade/img_upgrade_title_bg/txt_upgrade_title").GetComponent<TMPro.TextMeshProUGUI>();
			txt_upgrade_condition = transform.Find("rect_root/rect_upgrade/txt_upgrade_condition").GetComponent<TMPro.TextMeshProUGUI>();
			slider_upgrade_condition = transform.Find("rect_root/rect_upgrade/slider_upgrade_condition").GetComponent<Slider>();
			txt_slider_upgrade_condition = transform.Find("rect_root/rect_upgrade/slider_upgrade_condition/txt_slider_upgrade_condition").GetComponent<TMPro.TextMeshProUGUI>();
			base.InitComponent();
		}
		public override Transform GetAniRoot()
		{
			return rect_root;
		}
	}
}
