using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIActivityCalendarTimelineItem : UIItem
	{
		protected RectTransform rect_timeline;
		protected TMPro.TextMeshProUGUI txt_activity_name;
		protected UIButton btn_timeline;
		protected RectTransform rect_tips_pos;
		public override void InitComponent()
		{
			rect_timeline = transform.Find("rect_timeline").GetComponent<RectTransform>();
			txt_activity_name = transform.Find("rect_timeline/txt_activity_name").GetComponent<TMPro.TextMeshProUGUI>();
			btn_timeline = transform.Find("rect_timeline/btn_timeline").GetComponent<UIButton>();
			rect_tips_pos = transform.Find("rect_timeline/rect_tips_pos").GetComponent<RectTransform>();
		}
	}
}
