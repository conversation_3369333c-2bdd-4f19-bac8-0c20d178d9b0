using Core;
using Cysharp.Threading.Tasks;
using Game;
using GameMain;
using Packet;
using SuperScrollView;
using UIFramework;
using UnityEngine;

namespace GameUI
{
	public partial class UIActivityDispatchHelpLogItem : LoopListViewItem2
	{
        private UIComHeadItem mHeadItem;

        //初始化（具体参数各系统自行添加）
        public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
			
		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			
			
		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{
            var logInfo = (DispatchLogInfo)data[0];
            RefreshViewAsync(logInfo).Forget();
        }

        private async UniTask RefreshViewAsync(DispatchLogInfo data)
        {
            rect_emoji.SetActiveEx(data.EmojiId > 0);
            if (data.EmojiId > 0)
                await img_emoji.AsyncSetAtlasSprite("biaoqing", DispatchMgr.Ins.GetEmojiName(data.EmojiId));

            txt_time.text = TimeUtils.GetTimeStr1((uint)data.EventTime);

            User userInfo = data.UserInfo;

            if (userInfo.LeagueShortName.IsNullOrEmptyEx())
                txt_name.text = userInfo.Name;
            else
                txt_name.text = $"[{userInfo.LeagueShortName}]{userInfo.Name}";

            if (mHeadItem == null)
                mHeadItem = await AddSubItem<UIComHeadItem>(UIItemDefine.ComHeadItem, rect_head);
            mHeadItem.RefreshView(userInfo.IconId, userInfo.FrameId);
        }
    }
}
