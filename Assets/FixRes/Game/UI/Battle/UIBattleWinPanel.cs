using Cysharp.Threading.Tasks;
using Game.Framework;
using GameBattle;
using Packet;
using System.Collections.Generic;
using GameMain;
using SuperScrollView;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;

namespace GameUI
{
    public partial class UIBattleWinPanel : UIPanel
    {
        List<UIComBaseItem> _rewardItemList = new List<UIComBaseItem>();
        List<UIBattleHeroDamageItem> _heroDamageItemList = new List<UIBattleHeroDamageItem>();

        private List<UIComDisBtnCtrl> uiComDisBtnCtrls = new();
        private WLoopGridView scrool;
        private BattleState curBattleState = BattleState.Attack;
        public override async UniTask OnLoadComplete()
        {
            //主动加载异步资源
            
            await base.OnLoadComplete();
        }
        
        public override void AfterInitComponent()
        {
            //注册按钮以及其它初始化
            btn_background.AddOnClick(OnClickBackground);
            
            dis_btn_ctrl_Output_OutputBattle.btn_disable.AddOnClick(() => { ChangeTable(BattleState.Attack);});
            dis_btn_ctrl_Output_OutputBattle.btn_enable.AddOnClick(() => { ChangeTable(BattleState.Attack);});
            dis_btn_ctrl_Output_sprainBattle.btn_disable.AddOnClick(() => { ChangeTable(BattleState.Defense);});
            dis_btn_ctrl_Output_sprainBattle.btn_enable.AddOnClick(() => { ChangeTable(BattleState.Defense);});
            uiComDisBtnCtrls.Clear();
            uiComDisBtnCtrls.Add(dis_btn_ctrl_Output_OutputBattle);
            uiComDisBtnCtrls.Add(dis_btn_ctrl_Output_sprainBattle);
            base.AfterInitComponent();
        }

        /// <summary>
        /// 切换顶部 页签
        /// </summary>
        /// <param name="battleState"></param>
        private void ChangeTable(BattleState battleState)
        {
            var index = (int)battleState;
            if (index>=uiComDisBtnCtrls.Count)
            {
                return;
            }
            foreach (var uiComDisBtnCtrl in uiComDisBtnCtrls)
            {
                uiComDisBtnCtrl.SetGray(true);
            }
            uiComDisBtnCtrls[index].SetGray(false);
            curBattleState = battleState;
            RefreshDamageItemList(battleState).Forget();
        }

        public override void OnStart()
        {
            base.OnStart();
            
            UniTask.Void(RefreshRewardItemList);
           ChangeTable(BattleState.Attack);
        }
        
        public override void OnClose()
        {
            base.OnClose();
            for (int i = 0; i < _rewardItemList.Count; i++)
            {
                Destroy(_rewardItemList[i].gameObject);
            }
            
            // for (int i = 0; i < _heroDamageItemList.Count; i++)
            // {
            //     Destroy(_heroDamageItemList[i].gameObject);
            // }
        }
        
        public override void AfterClose()
        {
            GameResMgr.Ins.UnloadUIAsset(UIItemDefine.ComBaseItem);
            GameResMgr.Ins.UnloadAsset(sv_damage.itemName);
        }
        
        async UniTaskVoid RefreshRewardItemList()
        {
            var reward = GetPanelData()[0] as List<ItemInfo>;
            if (reward.Count <= 0)
            {
                return;
            }
            
            var go = await GameResMgr.Ins.LoadAndCreateUIItem(UIItemDefine.ComBaseItem, rect_reward_item_layout, reward.Count);
            for (int i = 0; i < reward.Count; i++)
            {
                int itemId = reward[i].Id;
                int itemCount = reward[i].Count;
                var baseItem = await AddSubItem<UIComBaseItem>(go[i]);
                baseItem.SetData(itemId, itemCount);
                baseItem.transform.localScale = Vector3.one * 0.8f;
                baseItem.GetComponent<Animator>().enabled = true;
                _rewardItemList.Add(baseItem);
            }
            
            LayoutRebuilder.MarkLayoutForRebuild(rect_reward_item_layout);
        }
        
        async UniTaskVoid RefreshDamageItemList(BattleState battleState)
        {
            var txt = battleState == BattleState.Attack ? Localization.GetValue(LanguageCode.__CODE__1000603)  : Localization.GetValue(LanguageCode.__CODE__1000604);
            txt_total_battleTitle.SetText(txt);
            var batttype = (int)battleState;
            var damageStatistics = BattleMgr.Ins.GetPVECtrl().GetDamageStatisticsResultToType((FightInfotype)batttype);
            damageStatistics.Sort((a, b) => battleState ==BattleState.Attack ?b.Value.damage.CompareTo(a.Value.damage):b.Value.beharmed.CompareTo(a.Value.beharmed));
            if (damageStatistics==null)
            {
                return;
            }
            
            float totalDamage = battleState ==BattleState.Attack ?BattleMgr.Ins.GetPVECtrl().GetDamageStatisticsResultAllToType().damage:BattleMgr.Ins.GetPVECtrl().GetDamageStatisticsResultAllToType().beharmed;
            txt_total_damage.text = UIHelper.GetNumberString(Mathf.FloorToInt(totalDamage));
            if (scrool == null)
            {
                scrool = await CreateLoopGrid<UIBattleHeroDamageItem>(sv_damage, damageStatistics.Count,RefreshUI);
            }
            else
            {
                scrool.SetListItemCount(damageStatistics.Count);
                scrool.RefreshAllShownItem();
            }
        }

        private void RefreshUI(WLoopGridView arg1, int index, WLoopItem item)
        {
            var damageStatistics = BattleMgr.Ins.GetPVECtrl().GetDamageStatisticsResultToType((FightInfotype)curBattleState);
            damageStatistics.Sort((a, b) => curBattleState ==BattleState.Attack ?b.Value.damage.CompareTo(a.Value.damage):b.Value.beharmed.CompareTo(a.Value.beharmed));
            if (damageStatistics.Count <= 0)
            {
                return;
            }
            BattleHero battleHero = damageStatistics[index].Key;
            float damage =  curBattleState ==BattleState.Attack ?damageStatistics[index].Value.damage:damageStatistics[index].Value.beharmed;
            float totalDamage = curBattleState ==BattleState.Attack ?BattleMgr.Ins.GetPVECtrl().GetDamageStatisticsResultAllToType().damage:BattleMgr.Ins.GetPVECtrl().GetDamageStatisticsResultAllToType().beharmed;
            ( item as UIBattleHeroDamageItem).SetData(battleHero.hero, damage, totalDamage,curBattleState);
        }


        void OnClickBackground()
        {
            Close();
            BattleMgr.Ins.ChangeState(GameBattle.BattleState.EXIT);
            GameSceneMgr.Ins.SwitchToPreScene().Forget();
        }
        
    }

    public enum BattleState
    {
        Attack=0,
        Defense=1,
    }
}