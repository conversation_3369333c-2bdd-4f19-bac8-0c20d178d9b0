using Cysharp.Threading.Tasks;
using DG.Tweening;
using SuperScrollView;
using UIFramework;
using UnityEngine;

namespace GameUI
{
    public partial class UITipsItem : WLoopItem
    {
        CanvasGroup mCanvasGroup;
        Tweener mCTweener;
        Tweener mTTweener;
        
        //初始化（具体参数各系统自行添加）
        public override void Init(params object[] param)
        {
        }
        
        //主动加载异步资源
        public override async UniTask OnLoadComplete()
        {
            mCanvasGroup = transform.Find("img_bg").GetComponent<CanvasGroup>();
        }
        
        //注册按钮以及其它初始化
        public override void AfterInitComponent()
        {
        }
        
        //刷新界面
        public override void RefreshView(params object[] data)
        {
        }
        
        public void ShowTips(string content, float playTime = 2.4f)
        {
            ResetTweener();
            
            txt_content.text = content;
            mCanvasGroup.alpha = 1f;
            mCTweener = mCanvasGroup.DOFade(0.1f, playTime);
            
            transform.localPosition = Vector3.zero;
            mTTweener = transform.DOLocalMoveY(150f, playTime);
            mTTweener.OnCompleteEx(OnFinished);
        }
        
        void ResetTweener()
        {
            if (mCTweener != null) mCTweener.Kill();
            if (mTTweener != null) mTTweener.Kill();
            
            mCTweener = null;
            mTTweener = null;
        }
        
        void OnFinished()
        {
            ResetTweener();
            
            gameObject.SetActive(false);
        }
    }
}