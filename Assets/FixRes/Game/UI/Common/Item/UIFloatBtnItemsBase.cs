using Core;
using Cysharp.Threading.Tasks;
using System.Collections.Generic;
using UIFramework;
using UnityEngine;

namespace GameUI
{
    public class UIFloatBtnItemsBase : UIItem
    {
        protected List<UIFloatBtnSubItemsBase> mSubItems = new List<UIFloatBtnSubItemsBase>();

        protected virtual RectTransform rectContent => null;

        protected virtual string subItemPath => string.Empty;

        protected virtual List<int> GetBtnList() => null;

        protected virtual object[] GetSubItemParams() => null;

        protected List<object> paramList = new();

        protected bool mIsInit = false;


        public virtual async UniTask RefreshViewAsync<T>() where T : UIFloatBtnSubItemsBase, new()
        {
            mIsInit = false;
            var list = GetBtnList();
            if (list == null)
            {
                return;
            }

            int diff = list.Count - mSubItems.Count;
            if (diff > 0)
            {
                var items = await AddSubItems<T>(subItemPath, rectContent, diff);
                
                for (int i = 0; i < items.Length; i++)
                {
                    
                    mSubItems.Add(items[i]);
                }
            }
            else
            {
                for (int i = 0; i < mSubItems.Count; i++)
                {
                    mSubItems[i].SetActiveEx(false);
                }
            }

            string name = System.IO.Path.GetFileNameWithoutExtension(subItemPath);
            var posList = UIHelper.GetFloatSubItemPos(list.Count);
            for (int i = 0; i < list.Count; i++)
            {
                if (posList != null && i < posList.Count)
                {
                    mSubItems[i].name = string.Format("{0}_{1}", name, list[i]);
                    mSubItems[i].SetPos(posList[i]);
                }
                mSubItems[i].RefreshView(list[i], GetSubItemParams(), paramList);
                await mSubItems[i].RefreshViewAsync();

                mSubItems[i].SetActiveEx(true);
            }

            mIsInit = true;
        }
    }
}
