using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UICommonAdditionSubItem : WLoopItem
	{
		protected GameObject go_bg;
		protected TMPro.TextMeshProUGUI txt_title;
		protected TMPro.TextMeshProUGUI txt_val;
		public override void InitComponent()
		{
			go_bg = transform.Find("go_bg").gameObject;
			txt_title = transform.Find("txt_title").GetComponent<TMPro.TextMeshProUGUI>();
			txt_val = transform.Find("txt_val").GetComponent<TMPro.TextMeshProUGUI>();
		}
	}
}
