using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UILoginServerSelItem : WLoopItem
	{
		protected UIButton btn_click;
		protected TMPro.TextMeshProUGUI txt_server_name;
		protected WImage img_state;
		protected GameObject go_new;
		public override void InitComponent()
		{
			btn_click = transform.Find("btn_click").GetComponent<UIButton>();
			txt_server_name = transform.Find("btn_click/txt_server_name").GetComponent<TMPro.TextMeshProUGUI>();
			img_state = transform.Find("btn_click/img_state").GetComponent<WImage>();
			go_new = transform.Find("btn_click/go_new").gameObject;
		}
	}
}
