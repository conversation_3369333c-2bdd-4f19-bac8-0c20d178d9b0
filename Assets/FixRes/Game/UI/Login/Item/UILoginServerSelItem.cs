using Core;
using Cysharp.Threading.Tasks;
using GameMain;
using SuperScrollView;
using UIFramework;

namespace GameUI
{
	public partial class UILoginServerSelItem : WLoopItem
	{
		ulong mServerId = 0;
		//初始化（具体参数各系统自行添加）
		public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
			
		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			btn_click.AddOnClick(OnClick);
			
		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{
			ulong serverId = (ulong)data[0];
            var info = LoginMgr.Ins.GetServerInfo(serverId);
            txt_server_name.text = Localization.ToFormat(LanguageCode.ServerCode, serverId);
			mServerId = serverId;
			go_new.SetActiveEx(info.state == GameDefine.ServerStateType.New);

			UniTask.Create(RefreshStateIcon);
        }

        async UniTask RefreshStateIcon()
        {
            var info = LoginMgr.Ins.GetServerInfo(mServerId);
            if (info == null) return;

            string iconName = GameDefine.GetServerStateIcon(info.state);

            await img_state.AsyncSetAtlasSprite("Login", iconName);
        }

		void OnClick()
		{
			mParent.RecvMsg(UILoginServerPanel.BtnType.Server, mServerId);
		}
    }
}
