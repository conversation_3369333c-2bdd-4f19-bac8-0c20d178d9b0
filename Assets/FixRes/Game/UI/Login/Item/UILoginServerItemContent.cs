using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UILoginServerItem : WLoopItem
	{
		protected UIButton btn_click;
		protected TMPro.TextMeshProUGUI txt_player_name;
		protected TMPro.TextMeshProUGUI txt_server_name;
		protected TMPro.TextMeshProUGUI txt_power;
		protected RectTransform rect_role_info;
		public override void InitComponent()
		{
			btn_click = transform.Find("btn_click").GetComponent<UIButton>();
			txt_player_name = transform.Find("btn_click/txt_player_name").GetComponent<TMPro.TextMeshProUGUI>();
			txt_server_name = transform.Find("btn_click/txt_server_name").GetComponent<TMPro.TextMeshProUGUI>();
			txt_power = transform.Find("btn_click/txt_power").GetComponent<TMPro.TextMeshProUGUI>();
			rect_role_info = transform.Find("btn_click/rect_role_info").GetComponent<RectTransform>();
		}
	}
}
