using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIStarCollisionDaybgItem : WLoopItem
	{
		protected RectTransform rect_LineR;
		protected RectTransform rect_Pass;
		public override void InitComponent()
		{
			rect_LineR = transform.Find("rect_LineR").GetComponent<RectTransform>();
			rect_Pass = transform.Find("rect_LineR/rect_Pass").GetComponent<RectTransform>();
		}
	}
}
