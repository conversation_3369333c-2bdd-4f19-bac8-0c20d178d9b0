using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIStarCollisionTipsItem : WLoopItem
	{
		protected RectTransform rect_item;
		protected TMPro.TextMeshProUGUI txt_Name;
		public override void InitComponent()
		{
			rect_item = transform.Find("rect_item").GetComponent<RectTransform>();
			txt_Name = transform.Find("txt_Name").GetComponent<TMPro.TextMeshProUGUI>();
		}
	}
}
