using System.Collections.Generic;
using Core;
using Cysharp.Threading.Tasks;
using Game;
using GameMain;
using SuperScrollView;
using UIFramework;
using UnityEngine;


namespace GameUI
{
    public partial class UIStarCollisionBannerItem
    {
        private MonumentTask _info;
        private WLoopGridView _itemGridView;
        private List<(int, int)> _rewardLists;
        private bool _isReceive;
        private EStarCollisionState _state;
        private ulong _startTime;
        private ulong _endTime;
        private Timer _timer;
        private int _targetCount;

        private WImage fillImage;

        public Color _normal;
        public Color gray;
        private readonly int _rewardMaxCount=4;

        public override void AfterInitComponent()
        {
            base.AfterInitComponent();
            btn_EndGetReward.onClick.AddListener(OnClickEndGetRewardBtn);
            btn_More.onClick.AddListener(OnClickMoreBtn);
            fillImage = slider_process.fillRect.GetComponent<WImage>();
        }


        //刷新界面
        public override void RefreshView(params object[] data)
        {
            _info = data[0] as MonumentTask;
            if (_info == null) return;
            var serverOpenTime = TimeUtils.GetServerOpenTime();
            _startTime = serverOpenTime + (ulong)_info.start_time * 1000;
            _endTime = _startTime + (ulong)_info.last_time * 1000;
            _targetCount = Cfg.MonumentTask.GetProcessTarget(_info.id);
            _state = GetState();
            _rewardLists = Cfg.MonumentTask.GetRewardItem(_info.id);
            _isReceive = StarCollisionMgr.Ins.CheckReceive(_info.id);
            RefreshAsync(RefreshUI);
        }

        private async UniTaskVoid RefreshUI()
        {
            RefreshStateUI();
            var id = _info.id.ToString();
            txt_DayNumber.SetText(_info.stage < 10 ? $"0{id.Substring(id.Length - 1)}" : _info.stage.ToString());
            txt_name.SetText(Localization.GetValue(_info.task_name));
            txt_des.SetText(Localization.GetValue(_info.task_content));
            txt_TimeLock.SetText(TimeUtils.GetTotalTimeStrForMat(_endTime, "yyyy-MM-dd"));
            txt_EndTime.SetText(TimeUtils.GetTotalTimeStrForMat(_endTime));
            rect_LockIcon.SetActiveEx(false);
            btn_EndGetReward.SetActiveEx(StarCollisionMgr.Ins.CheckGetReward(_info.id));
            RefreshSlider();

            await img_Banner.AsyncSetAtlasSprite("StarCollision", _info.task_banner);
            if (_info.unlock_func != 0) //!string.IsNullOrEmpty(_info.unlock_func.ToString()))
            {
                rect_LockIcon.SetActiveEx(true);
                await img_icon.AsyncSetAtlasSprite("StarCollision", _info.unlock_func.ToString());
            }

            await LoadGrip();
        }

        private void RefreshSlider()
        {
            var curCount = StarCollisionMgr.Ins.GetTaskProgress(_info.id);
            txt_slider.SetText($"{curCount}/{_targetCount}");
            slider_process.value = curCount / _targetCount;
        }

        private void RefreshStateUI()
        {
            rect_Lock.SetActiveEx(false);
            rect_Time.SetActiveEx(false);
            rect_End.SetActiveEx(false);
            rect_reward.SetActiveEx(false);
            img_Banner.SetImgGray(false);
            fillImage.color = _normal;
            btn_More.SetActiveEx(false);
            if (_timer != null)
            {
                _timer.Cancel();
                _timer = null;
            }

            switch (_state)
            {
                case EStarCollisionState.None:
                    rect_Lock.SetActiveEx(true);
                    rect_reward.SetActiveEx(true);
                    btn_More.SetActiveEx(_rewardLists.Count>_rewardMaxCount);
                    break;
                case EStarCollisionState.InProgress:
                    var needTime = (int)(_endTime - TimeUtils.GetServerTimeMs());
                    _timer = Timer.Register(needTime, OnCountDownComplete, OnCountDownUpdate);
                    rect_Time.SetActiveEx(true);
                    rect_reward.SetActiveEx(true);
                    btn_More.SetActiveEx(_rewardLists.Count>_rewardMaxCount);
                    break;
                case EStarCollisionState.End:
                    rect_End.SetActiveEx(true);
                    var isSucceed = StarCollisionMgr.Ins.CheckSucceed(_info.id);
                    rect_Sucess.SetActiveEx(isSucceed);
                    rect_Fail.SetActiveEx(!isSucceed);
                    img_Banner.SetImgGray(true);
                    fillImage.color = !isSucceed ? gray : _normal;
                    break;
            }
        }

        private void OnCountDownUpdate(float obj)
        {
            RefreshTime();
        }

        private void OnCountDownComplete()
        {
            _timer?.Cancel();
            _timer = null;
        }

        void RefreshTime()
        {
            var needTime = _endTime - TimeUtils.GetServerTimeMs();
            var day = TimeUtils.GetTotalDateTime(needTime);
            txt_Time?.SetText($"{day.Day}d {TimeUtils.GetTotalTimeStrForMat(needTime, "HH:mm:ss")}");
        }

        private async UniTask LoadGrip()
        {
            var maxCount = Mathf.Min(_rewardMaxCount, _rewardLists.Count);
            if (!_itemGridView)
                _itemGridView = await CreateLoopGrid<UIStarCollisionResItem>(sv_reward, maxCount, RefreshItem);
            else
            {
                _itemGridView.SetListItemCount(maxCount);
            }
        }

        private void RefreshItem(WLoopGridView arg1, int index, WLoopItem item)
        {
            var rewardItem = item as UIStarCollisionResItem;
            rewardItem?.RefreshView(_rewardLists[index], _isReceive);
        }


        private EStarCollisionState GetState()
        {
            var curTime = TimeUtils.GetServerTimeMs();
            if (curTime < _startTime)
            {
                return EStarCollisionState.None;
            }
            else if (curTime < _endTime)
            {
                return EStarCollisionState.InProgress;
            }

            return EStarCollisionState.End;
        }

        private void OnClickEndGetRewardBtn()
        {
        }

        private void OnClickMoreBtn()
        {
            UIMgr.Ins.Open(UIDefine.StarCollisionTipsPanel,rect_TipShow,_rewardLists);
        }

        public override void OnUnInit()
        {
            base.OnUnInit();
            _timer?.Cancel();
            _timer = null;
        }
    }

    public enum EStarCollisionState
    {
        /// <summary>
        /// 未开始
        /// </summary>
        None,

        /// <summary>
        /// 进行中
        /// </summary>
        InProgress,

        /// <summary>
        /// 结束
        /// </summary>
        End,
    }
}