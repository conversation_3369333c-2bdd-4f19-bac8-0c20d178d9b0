using UnityEngine;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIComSoldierFloatPanel : UIPanel
	{
		protected UIButton btn_close;
		protected RectTransform rect_root;
		protected RectTransform rect_bg;
		protected RectTransform rect_arrow;
		protected RectTransform rect_content;
		protected RectTransform rect_bar;
		protected RectTransform rect_data;
		public override void InitComponent()
		{
			btn_close = transform.Find("btn_close").GetComponent<UIButton>();
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			rect_bg = transform.Find("rect_root/rect_bg").GetComponent<RectTransform>();
			rect_arrow = transform.Find("rect_root/rect_bg/rect_arrow").GetComponent<RectTransform>();
			rect_content = transform.Find("rect_root/rect_content").GetComponent<RectTransform>();
			rect_bar = transform.Find("rect_root/rect_content/rect_bar").GetComponent<RectTransform>();
			rect_data = transform.Find("rect_root/rect_content/rect_data").GetComponent<RectTransform>();
			base.InitComponent();
		}
		public override Transform GetAniRoot()
		{
			return rect_root;
		}
	}
}
