using Core;
using Cysharp.Threading.Tasks;
using GameMain;
using SuperScrollView;
using System;
using UIFramework;
using UnityEngine;

namespace GameUI
{
	public partial class UIComDwTabItem : UIComTabItemBase
    {

        //初始化（具体参数各系统自行添加）
        public override void Init(params object[] param)
        {

        }
        //主动加载异步资源
        public override async UniTask OnLoadComplete()
        {
            base.OnLoadComplete();
        }
        //注册按钮以及其它初始化
        public override void AfterInitComponent()
        {
            btn_click.AddOnClick(OnClick);


        }
        //刷新界面
        public override void RefreshView(params object[] data)
        {

        }

        public override void SetText(string text)
        {
            txt_up.text = text;
            txt_dw.text = text;
        }

        public override void SetState(bool state)
        {
            go_up.SetActiveEx(state);
            go_dw.SetActiveEx(!state);
        }
    }
}
