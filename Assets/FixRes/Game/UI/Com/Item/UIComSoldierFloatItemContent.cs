using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIComSoldierFloatItem : WLoopItem
	{
		protected TMPro.TextMeshProUGUI txt_name;
		protected TMPro.TextMeshProUGUI txt_val;
		public override void InitComponent()
		{
			txt_name = transform.Find("txt_name").GetComponent<TMPro.TextMeshProUGUI>();
			txt_val = transform.Find("txt_val").GetComponent<TMPro.TextMeshProUGUI>();
		}
	}
}
