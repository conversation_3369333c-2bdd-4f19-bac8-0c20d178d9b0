using System.Collections.Generic;
using System.Threading.Tasks;
using Core;
using Cysharp.Threading.Tasks;
using Game.Framework;
using GameMain;
using UIFramework;
using UnityEngine;

namespace GameUI
{
    public partial class UIRecruitGetNewHeroPanel
    {
        private Queue<int> _infos = new();

        private int _infoID;
        private UIHeroSkillIconItem[] _mSkillItems;
        private HeroTb _heroInfo;
        private GameObject _mObjSpine;

        const int MaxSkillCount = 4;

        public override async UniTask OnLoadComplete()
        {
            //主动加载异步资源
            _infos = GetPanelData()[0] as Queue<int>;
            await LoadSkill();
            TryNextHero();
            await base.OnLoadComplete();
        }

        public override void AfterInitComponent()
        {
            //注册按钮以及其它初始化
            btn_close.onClick.AddListener(TryNextHero);
            base.AfterInitComponent();
        }


        public override void OnStart()
        {
            base.OnStart();
            //RefreshAsync(RefreshUI);
        }

        private async UniTaskVoid RefreshUI()
        {
            _heroInfo = Cfg.HeroTb.GetData(_infoID);
            txt_Name.SetText(Localization.GetValue(_heroInfo.name));
            await img_qualityBg.AsyncSetSingleSprite(UIHelper.GetRecruitQualityArrowBg(_heroInfo.quality));
            await img_qualityBg1.AsyncSetSingleSprite(UIHelper.GetRecruitQualityArrowBg1(_heroInfo.quality));
            await img_quality.AsyncSetAtlasSprite(Localization.currentLanguage,
                UIHelper.GetHeroQualityNameBg(_heroInfo.quality));
            RefreshSkill();
            RefreshAsync(LoadSpint);
        }

        private async UniTaskVoid LoadSpint()
        {
            DestorySpine();

            var go = await GameResMgr.Ins.LoadAndCreateObject(
                string.Format("Prefabs/Spines/{0}.prefab", _heroInfo.spine), rect_hero);
            _mObjSpine = go[0];
            await AddSubItem<UISpine>(_mObjSpine);
            _mObjSpine.name = _heroInfo.spine;
        }


        private async Task LoadSkill()
        {
            _mSkillItems = new UIHeroSkillIconItem[MaxSkillCount];
            for (int i = 0; i < _mSkillItems.Length; i++)
            {
                var item = await AddSubItem<UIHeroSkillIconItem>(UIItemDefine.HeroSkillIconItem, rect_Skill);
                _mSkillItems[i] = item;
                item.SetActiveEx(false);
            }
        }

        private void RefreshSkill()
        {
            foreach (var item in _mSkillItems)
            {
                item.SetActiveEx(false);
            }

            for (int i = 0; i < _heroInfo.skill.Length; i++)
            {
                var item = _mSkillItems[i];
                item.RefreshView(_heroInfo.skill[i], i);
                item.ShowLock(false);
                item.ShowGo(false);
                item.SetActiveEx(true);
            }
        }

        private void TryNextHero()
        {
            if (_infos.Count > 0)
            {
                _infoID = _infos.Dequeue();
                RefreshAsync(RefreshUI);
            }
            else
            {
                Close();
            }
        }

        private void DestorySpine()
        {
            if (_mObjSpine)
            {
                GameResMgr.Ins.UnloadAsset(string.Format("Prefabs/Spines/{0}.prefab", _mObjSpine.name));
                GameObject.Destroy(_mObjSpine);
                _mObjSpine = null;
            }
        }
    }
}