using Core;
using Cysharp.Threading.Tasks;
using Game;
using Game.Network;
using GameMain;
using SuperScrollView;
using System;
using UIFramework;
using UnityEngine;

namespace GameUI
{
    public partial class UIBagGetMoreItem : WLoopItem
    {
        int _itemId;
        int _leftCount;
        bool _isBuy;
        int _obtainId;
        int _needItemId;
        
        UIComBaseItem baseItem;
        
        private const int BUY_STAMINA_COUNT = 10;
        
        //初始化（具体参数各系统自行添加）
        public override void Init(params object[] param)
        {
        }
        
        //主动加载异步资源
        public override async UniTask OnLoadComplete()
        {
        }
        
        //注册按钮以及其它初始化
        public override void AfterInitComponent()
        {
            baseItem = transform.Find("base_item_icon").GetComponent<UIComBaseItem>();
            baseItem.InitComponent();
            baseItem.AfterInitComponent();
            
            btn_use.AddOnClick(OnClickUse);
            btn_use_count.AddOnClick(OnClickUseCount);
            btn_goto.AddOnClick(OnClickGoto);
            btn_buy_use.AddOnClick(OnClickBuyUse);
        }
        
        //刷新界面
        public override void RefreshView(params object[] data)
        {
        }
        
        public void SetData(int obtainId)
        {
            _obtainId = obtainId;
            ObtainWay obtainWay = Cfg.ObtainWay.GetData(obtainId);
            RefreshItem(obtainWay);
        }
        
        public void SetData(int itemId, int leftCount, bool isBuy, int needItemId)
        {
            _itemId = itemId;
            _leftCount = leftCount;
            _isBuy = isBuy;
            _needItemId = needItemId;
            _obtainId = 0;
            
            RefreshItem();
        }
        
        void RefreshItem(ObtainWay obtainWay)
        {
            baseItem.SetQualityBgAndIcon("img_common_icon_djpz_da_01", obtainWay.icon);
            baseItem.ShowExtend(false);
            baseItem.ShowCount(false);
            baseItem.SetDisableClick(true);
            txt_name.text = Localization.GetValue(obtainWay.title);
            txt_desc.text = Localization.GetValue(obtainWay.desc);
            
            btn_use.SetActiveEx(false);
            btn_buy_use.SetActiveEx(false);
            go_use_count.SetActiveEx(false);
            btn_goto.SetActiveEx(true);
        }
        
        void RefreshItem()
        {
            baseItem.SetData(_itemId, BagMgr.Ins.GetItemCount(_itemId));
            
            Item cfg = Cfg.Item.GetData(_itemId);
            
            if (IsStamina() && _isBuy)
            {
                txt_name.text = Localization.GetValue(LanguageCode.__CODE__1000327);
                txt_desc.text = Localization.GetValue(LanguageCode.__CODE__1000328);
            }
            else
            {
                txt_name.text = Localization.GetValue(cfg.name);
                txt_desc.text = Localization.GetValue(cfg.des);
            }
            
            
            txt_btn_name.text = $"+{GetUseMax()}";
            
            btn_goto.SetActiveEx(false);
            
            btn_use.SetActiveEx(!_isBuy);
            btn_buy_use.SetActiveEx(_isBuy);
            
            baseItem.ShowCount(!_isBuy);
            go_use_count.SetActiveEx(!_isBuy);
            
            if (IsStamina())
            {
                baseItem.ShowExtend(true);
                int count = _isBuy ? BUY_STAMINA_COUNT : cfg.reward[0][1];
                baseItem.SetExtend(count.ToString());
            }
            else
            {
                baseItem.ShowExtend(false);
            }
            
            if (_isBuy)
            {
                RefreshBuyUse();
            }
            
            var costItemCfg = Cfg.Item.GetData(GameDefine.SpecItemId.Gold);
            
            img_cost_icon.AsyncSetAtlasSprite("Icon", costItemCfg.icon).Forget();
        }
        
        int GetUseMax()
        {
            Item cfg = Cfg.Item.GetData(_itemId);
            if (_isBuy)
            {
                return 0;
            }
            else if (IsStamina())
            {
                int effectCount = cfg.reward[0][1];
                int ownCount = BagMgr.Ins.GetItemCount(_itemId);
                int maxStamina = StaminaMgr.Ins.MaxStamina;
                return Mathf.Min(ownCount, maxStamina / effectCount);
            }
            else
            {
                int effectCount = cfg.reward[0][1];
                int ownCount = BagMgr.Ins.GetItemCount(_itemId);
                
                return Mathf.Min(ownCount, Mathf.CeilToInt((float)_leftCount / effectCount));
            }
        }
        
        bool IsStamina() => _needItemId == GameDefine.SpecItemId.Stamina;
        
        void OnClickUseCount()
        {
            if (_isBuy)
            {
            }
            else
            {
                int useMax = GetUseMax();
                BagMgr.Ins.ReqUseItem(_itemId, useMax);
            }
        }
        
        void OnClickUse()
        {
            BagMgr.Ins.ReqUseItem(_itemId, 1);
        }
        
        void OnClickBuyUse()
        {
            var itemCfg = Cfg.Item.GetData(_itemId);
            int cost = GetBuyCost();
            if (BagMgr.Ins.GetItemCount(GameDefine.SpecItemId.Gold) < cost)
            {
                TipsMgr.Ins.ShowTips(LanguageCode.ItemNotEnough);
                return;
            }
            
            if (itemCfg.item_type == GameDefine.ItemSheetType.itemCurrency ||
                itemCfg.item_type == GameDefine.ItemSheetType.itemCombineResource)
            {
                int[] currency_combine = Cfg.ItemCombine.GetData(_itemId).currency_combine;
                var buyItemId = currency_combine[currency_combine.Length - 1];
                
                int leftCount = IsStamina() ? BUY_STAMINA_COUNT : _leftCount;
                ItemNetwork.Ins.ReqBuyRes(buyItemId, leftCount);
            }
            else
            {
                ItemNetwork.Ins.ReqBuyItem(_itemId, _leftCount, 0, 0);
            }
        }
        
        void RefreshBuyUse()
        {
            txt_buycost.text = GetBuyCost().ToString();
        }
        
        int GetBuyCost()
        {
            if (_isBuy)
            {
                if (_itemId == GameDefine.ItemCombineId.HeroTicket ||
                    _itemId == GameDefine.ItemCombineId.NormalTicket)
                {
                    var cfg = Cfg.Item.GetData(_itemId);
                    txt_btn_name_use.text = Localization.ToFormat(LanguageCode.__CODE__1000164);
                    return cfg.price[1] * _leftCount;
                }
                else
                {
                    txt_btn_name_use.text = Localization.ToFormat(LanguageCode.__CODE__1000165);
                    ImmediatelyCostType costType = ImmediatelyCostType.None;
                    switch (_itemId)
                    {
                        case GameDefine.ItemCombineId.Water:
                            costType = ImmediatelyCostType.Water;
                            break;
                        case GameDefine.ItemCombineId.Power:
                            costType = ImmediatelyCostType.Power;
                            break;
                        case GameDefine.ItemCombineId.Ore:
                            costType = ImmediatelyCostType.Ore;
                            break;
                        case GameDefine.ItemCombineId.RareMetals:
                            costType = ImmediatelyCostType.RareMetals;
                            break;
                        case GameDefine.ItemCombineId.Stamina:
                            costType = ImmediatelyCostType.Stamina;
                            break;
                    }
                    
                    if (costType == ImmediatelyCostType.None)
                    {
                        return 0;
                    }
                    
                    int leftCount = IsStamina() ? BUY_STAMINA_COUNT : _leftCount;
                    
                    int totalCost = (int)BagMgr.Ins.CalcUseCost(costType, leftCount, true);
                    return totalCost;
                }
            }
            
            return 0;
        }
        
        void OnClickGoto()
        {
            if (_obtainId != 0)
            {
                GoToUtils.GoTo(_obtainId);
            }
            else
            {
                DLogger.Error("点击前往但是_obtainId为0");
            }
        }
    }
}