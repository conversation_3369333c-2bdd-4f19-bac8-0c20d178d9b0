using Core;
using Cysharp.Threading.Tasks;
using GameMain;
using SuperScrollView;
using UIFramework;

namespace GameUI
{
	public partial class UIHeroDetailSubItem : WLoopItem
	{
		//初始化（具体参数各系统自行添加）
		public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
			
		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			
			
		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{
			int heroId = (int)data[0];
			int attrId = (int)data[1];
			int index = (int)data[2];
            int max = (int)data[3];

            txt_title.text = Localization.ToFormat(UIHelper.GetHeroDetailDescLangId(index));

			var info = HeroMgr.Ins.GetInfo(heroId);
			if (info == null) return;

			if (index == 0)
                txt_val.text = UIHelper.GetNumberString((int)info.GetAttrVal(attrId));
			else 
				txt_val.text = "0";

			if (max % 2 == 0)
                go_bg.SetActiveEx(index % 2 == 0);
			else 
				go_bg.SetActiveEx(index % 2 == 1);

        }
	}
}
