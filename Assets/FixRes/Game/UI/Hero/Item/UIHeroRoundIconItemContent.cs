using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIHeroRoundIconItem : WLoopItem
	{
		protected WImage img_quality_bg;
		protected WImage img_hero;
		public override void InitComponent()
		{
			img_quality_bg = transform.Find("img_mask/img_quality_bg").GetComponent<WImage>();
			img_hero = transform.Find("img_mask/img_hero").GetComponent<WImage>();
		}
	}
}
