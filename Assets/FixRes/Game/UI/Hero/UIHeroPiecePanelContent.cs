using UnityEngine;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIHeroPiecePanel : UIPanel
	{
		protected RectTransform rect_root;
		protected RectTransform rect_bg;
		protected RectTransform rect_content;
		protected WImage img_item_quality;
		protected WImage img_item_icon;
		protected Slider slider_item_count;
		protected TMPro.TextMeshProUGUI txt_item_count;
		protected TMPro.TextMeshProUGUI txt_item_name;
		protected WScrollRect sv_way;
		public override void InitComponent()
		{
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			rect_bg = transform.Find("rect_root/rect_bg").GetComponent<RectTransform>();
			rect_content = transform.Find("rect_root/rect_content").GetComponent<RectTransform>();
			img_item_quality = transform.Find("rect_root/rect_content/img_item_quality").GetComponent<WImage>();
			img_item_icon = transform.Find("rect_root/rect_content/img_item_quality/img_item_icon").GetComponent<WImage>();
			slider_item_count = transform.Find("rect_root/rect_content/slider_item_count").GetComponent<Slider>();
			txt_item_count = transform.Find("rect_root/rect_content/slider_item_count/txt_item_count").GetComponent<TMPro.TextMeshProUGUI>();
			txt_item_name = transform.Find("rect_root/rect_content/txt_item_name").GetComponent<TMPro.TextMeshProUGUI>();
			sv_way = transform.Find("rect_root/rect_content/sv_way").GetComponent<WScrollRect>();
			base.InitComponent();
		}
		public override Transform GetAniRoot()
		{
			return rect_root;
		}
	}
}
