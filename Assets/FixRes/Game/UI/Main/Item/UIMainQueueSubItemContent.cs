using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIMainQueueSubItem : WLoopItem
	{
		protected UIButton btn_click;
		protected GameObject go_state;
		protected WImage img_state_icon;
		protected TMPro.TextMeshProUGUI txt_state;
		protected TMPro.TextMeshProUGUI txt_name;
		protected UIButton btn_opera;
		protected UIButton btn_finished;
		protected Slider slider_time;
		protected TMPro.TextMeshProUGUI txt_time;
		public override void InitComponent()
		{
			btn_click = transform.Find("btn_click").GetComponent<UIButton>();
			go_state = transform.Find("btn_click/go_state").gameObject;
			img_state_icon = transform.Find("btn_click/go_state/img_state_icon").GetComponent<WImage>();
			txt_state = transform.Find("btn_click/txt_state").GetComponent<TMPro.TextMeshProUGUI>();
			txt_name = transform.Find("btn_click/txt_name").GetComponent<TMPro.TextMeshProUGUI>();
			btn_opera = transform.Find("btn_click/btn_opera").GetComponent<UIButton>();
			btn_finished = transform.Find("btn_click/btn_finished").GetComponent<UIButton>();
			slider_time = transform.Find("btn_click/slider_time").GetComponent<Slider>();
			txt_time = transform.Find("btn_click/slider_time/txt_time").GetComponent<TMPro.TextMeshProUGUI>();
		}
	}
}
