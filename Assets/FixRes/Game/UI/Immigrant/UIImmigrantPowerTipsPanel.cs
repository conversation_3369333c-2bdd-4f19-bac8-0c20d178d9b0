using Cysharp.Threading.Tasks;
using UIFramework;
using UnityEngine;
using GameMain;

namespace GameUI
{
	public partial class UIImmigrantPowerTipsPanel : UIPanel
	{
        private Transform m_Target;
		private ImmigrantInfo m_Info;
        public override async UniTask OnLoadComplete()
		{
			//主动加载异步资源
			
			await base.OnLoadComplete();
		}
		public override void AfterInitComponent()
		{
			//注册按钮以及其它初始化
			btn_close.AddOnClick(Close);
			base.AfterInitComponent();
		}

        public override void OnStart()
		{
            base.OnStart();
            var param = UIMgr.Ins.GetPanelData(panelId);
            if (param != null && param.Length > 1)
            {
                m_Target = (Transform)param[0];
                m_Info = param[1] as ImmigrantInfo;
            }

            if (m_Target == null) return;
            Vector2 screenPosition = RectTransformUtility.WorldToScreenPoint(UIMgr.Ins.GetUICamera(), m_Target.transform.position);
            UIHelper.AutoFollowPos(this, screenPosition, rect_root, rect_arrow, 40f, -19f, -6.5f);


            txt_total_power.text = UIHelper.GetNumberString(m_Info.Power);
            txt_starPower.text = UIHelper.GetNumberString(m_Info.StarPower);
			txt_basePower.text = UIHelper.GetNumberString(m_Info.BasePower);

        }

    }
}
