using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIImmigrantDispatchListItem : WLoopItem
	{
		protected GameObject go_select;
		protected RectTransform rect_head;
		protected GameObject go_mark;
		protected TMPro.TextMeshProUGUI txt_career;
		protected TMPro.TextMeshProUGUI txt_name;
		protected TMPro.TextMeshProUGUI txt_add;
		protected TMPro.TextMeshProUGUI txt_add_val;
		protected UIButton btn_click;
		public override void InitComponent()
		{
			go_select = transform.Find("go_select").gameObject;
			rect_head = transform.Find("rect_head").GetComponent<RectTransform>();
			go_mark = transform.Find("go_mark").gameObject;
			txt_career = transform.Find("txt_career").GetComponent<TMPro.TextMeshProUGUI>();
			txt_name = transform.Find("txt_name").GetComponent<TMPro.TextMeshProUGUI>();
			txt_add = transform.Find("txt_add").GetComponent<TMPro.TextMeshProUGUI>();
			txt_add_val = transform.Find("txt_add_val").GetComponent<TMPro.TextMeshProUGUI>();
			btn_click = transform.Find("btn_click").GetComponent<UIButton>();
		}
	}
}
