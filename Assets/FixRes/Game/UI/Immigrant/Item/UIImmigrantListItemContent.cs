using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIImmigrantListItem : UIItem
	{
		protected RectTransform rect_tab;
		protected WScrollRect sv_items;
		protected UIButton btn_filter;
		protected RectTransform rect_filter;
		public override void InitComponent()
		{
			rect_tab = transform.Find("rect_tab").GetComponent<RectTransform>();
			sv_items = transform.Find("sv_items").GetComponent<WScrollRect>();
			btn_filter = transform.Find("img_/btn_filter").GetComponent<UIButton>();
			rect_filter = transform.Find("rect_filter").GetComponent<RectTransform>();
		}
	}
}
