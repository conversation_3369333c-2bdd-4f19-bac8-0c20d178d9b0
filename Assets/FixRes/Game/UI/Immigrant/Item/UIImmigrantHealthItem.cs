using Cysharp.Threading.Tasks;
using SuperScrollView;
using UIFramework;
using UnityEngine;
using GameMain;
using Core;

namespace GameUI
{
	public partial class UIImmigrantHealthItem : UIItem
	{
		//初始化（具体参数各系统自行添加）

		public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
			
		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			
			
		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{
			
		}

		public void SetData(ImmigrationHealth cfg, int curHealth)
		{
			if (cfg.health_range != null && cfg.health_range.Length > 1) { 
				var min = cfg.health_range[0];
				var max = cfg.health_range[1];
				if(min == max)
				{
					txt_health.text = min.ToString();
				}
				else
				{
					txt_health.text = $"{min} - {max}";
				}
				go_select.SetActiveEx(curHealth >= min && curHealth <= max);
            }

            txt_efficiency.text = $"{cfg.add_percent}%";

		}

	}
}
