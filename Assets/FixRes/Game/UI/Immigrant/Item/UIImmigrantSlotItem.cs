using Cysharp.Threading.Tasks;
using SuperScrollView;
using UIFramework;
using UnityEngine;
using GameMain;
using System;
using Core;

namespace GameUI
{
	public partial class UIImmigrantSlotItem : WLoopItem
	{
		//初始化（具体参数各系统自行添加）
		private int m_SlotId;
		private int m_BuildingId;
        public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
			
		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			btn_add.AddOnClick(OnAddClick);
			btn_lock.AddOnClick(OnLockClick);
		}

		private void OnAddClick()
		{
			UIMgr.Ins.Open(UIDefine.ImmigrantDispatchListPanel, m_BuildingId, m_SlotId);
		}

		private void OnLockClick()
		{
            var unLockLv = Cfg.BuildingLevel.GetSlotUnlockLv(m_BuildingId, m_SlotId);
			var cfg = Cfg.Building.GetData(m_BuildingId);
			if(cfg != null)
			{
                TipsMgr.Ins.ShowTips(Localization.ToFormat(LanguageCode.__CODE__1000495,
                Localization.GetValue(cfg.name), unLockLv));
            }
        }

		//刷新界面
		public override void RefreshView(params object[] data)
		{
			
		}

		public void SetData(int buildingId, int slotId, bool isLock)
		{
			m_BuildingId = buildingId;
			m_SlotId = slotId;
			btn_add.SetActiveEx(!isLock);
			btn_lock.SetActiveEx(isLock);
			var langId = isLock ? LanguageCode.__CODE__1000494 : LanguageCode.__CODE__1000493;
			txt_state.SetLanguageId(langId);
        }
	}
}
