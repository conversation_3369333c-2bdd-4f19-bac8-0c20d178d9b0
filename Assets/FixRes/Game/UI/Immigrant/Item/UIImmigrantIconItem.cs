using Cysharp.Threading.Tasks;
using SuperScrollView;
using UIFramework;
using UnityEngine;
using GameMain;
using System;

namespace GameUI
{
	public partial class UIImmigrantIconItem : WLoopItem
	{
		//初始化（具体参数各系统自行添加）
		private Action<int, UIImmigrantIconItem> m_Cb;
		private ImmigrantInfo m_ImmigrantInfo;
		private ImmigrantStarCtrl m_StarCtrl;
		public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			m_StarCtrl = new ImmigrantStarCtrl(rect_star, this);
			await m_StarCtrl.LoadItems();
		}
        //注册按钮以及其它初始化
        public override void AfterInitComponent()
		{
			btn_click.AddOnClick(OnBtnClick);
			
		}
		private void OnBtnClick()
		{
			m_Cb?.Invoke(m_ImmigrantInfo.Id, this);
		}

		//刷新界面
		public override void RefreshView(params object[] data)
		{
			
		}

		public void SetData(ImmigrantInfo info, Action<int, UIImmigrantIconItem> clickCb)
		{
            m_Cb = clickCb;
			m_ImmigrantInfo = info;
            var cfg = info.ImmigrantCfg;
            img_di.AsyncSetAtlasSprite("QualityBg", UIHelper.GetImmigrantQualityBg(info.Quality)).Forget();
            img_kuang.AsyncSetAtlasSprite("QualityBg", UIHelper.GetImmigrantQualityCover(info.Quality)).Forget();
            img_icon.AsyncSetAtlasSprite("ImmigrationHead", cfg.icon).Forget();
			txt_name.SetLanguageId(cfg.name);
            img_career_di.AsyncSetAtlasSprite("QualityBg", UIHelper.GetImmigrantQualityArrowBg(info.Quality)).Forget();
			img_career.AsyncSetAtlasSprite("Common", UIHelper.GetImmigrantCareerIcon1(info.Career)).Forget();
			
			
			m_StarCtrl.RefreshView(info.Star).Forget();

        }
    }
}
