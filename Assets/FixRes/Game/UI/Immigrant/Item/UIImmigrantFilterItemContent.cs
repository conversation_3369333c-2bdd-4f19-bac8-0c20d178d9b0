using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIImmigrantFilterItem : UIItem
	{
		protected UIButton btn_close;
		protected RectTransform rect_go;
		protected RectTransform rect_layout;
		public override void InitComponent()
		{
			btn_close = transform.Find("btn_close").GetComponent<UIButton>();
			rect_go = transform.Find("rect_go").GetComponent<RectTransform>();
			rect_layout = transform.Find("rect_go/rect_layout").GetComponent<RectTransform>();
		}
	}
}
