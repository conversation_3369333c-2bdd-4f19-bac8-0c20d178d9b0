using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIImmigrantHealthItem : UIItem
	{
		protected TMPro.TextMeshProUGUI txt_efficiency;
		protected TMPro.TextMeshProUGUI txt_health;
		protected GameObject go_select;
		public override void InitComponent()
		{
			txt_efficiency = transform.Find("txt_efficiency").GetComponent<TMPro.TextMeshProUGUI>();
			txt_health = transform.Find("txt_health").GetComponent<TMPro.TextMeshProUGUI>();
			go_select = transform.Find("go_select").gameObject;
		}
	}
}
