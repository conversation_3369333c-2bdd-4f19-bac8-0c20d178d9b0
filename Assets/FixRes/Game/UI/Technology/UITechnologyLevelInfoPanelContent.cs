using UnityEngine;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UITechnologyLevelInfoPanel : UIPanel
	{
		protected RectTransform rect_root;
		protected UIButton btn_close;
		protected RectTransform rect_content;
		protected TMPro.TextMeshProUGUI txt_title;
		protected TMPro.TextMeshProUGUI txt_addition_name;
		protected WScrollRect sv_list;
		public override void InitComponent()
		{
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			btn_close = transform.Find("rect_root/btn_close").GetComponent<UIButton>();
			rect_content = transform.Find("rect_root/rect_content").GetComponent<RectTransform>();
			txt_title = transform.Find("rect_root/rect_content/txt_title").GetComponent<TMPro.TextMeshProUGUI>();
			txt_addition_name = transform.Find("rect_root/rect_content/hori/txt_addition_name").GetComponent<TMPro.TextMeshProUGUI>();
			sv_list = transform.Find("rect_root/rect_content/sv_list").GetComponent<WScrollRect>();
			base.InitComponent();
		}
		public override Transform GetAniRoot()
		{
			return rect_root;
		}
	}
}
