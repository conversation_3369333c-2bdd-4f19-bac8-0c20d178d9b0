using Cysharp.Threading.Tasks;
using Game.City;
using SuperScrollView;
using System.Collections.Generic;
using GameMain;
using UIFramework;
using UnityEngine;
using Core;

namespace GameUI
{
	public partial class UICityResOverviewPanel : UIPanel
	{
        WLoopGridView mList;
        List<int> mDataList = new();
        HashSet<int> mResIds = new(); 
        Dictionary<int, int> mProtectCountDic = new();
        
        /*
            建筑1 产 a = 10 b = 100 资源   速度缩减0.5
            建筑2 产 a = 100 b = 10 资源   速度缩减1 
            
            a资源生产效率 = 10 / 9.5 + 100 / 9    
            
         */
        private class BuildingYieldInfo
        {
            public int buildingId;
            public Dictionary<int, int> resYiledCnts = new Dictionary<int, int>();
            public float yieldTime;

            public BuildingYieldInfo(int buildingId)
            {
                this.buildingId = buildingId;
                yieldTime = Cfg.Const.GetVal(19);
            }


        }
        Dictionary<int, BuildingYieldInfo> mYieldInfos  = new Dictionary<int, BuildingYieldInfo>();
        
        public override async UniTask OnLoadComplete()
		{
			//主动加载异步资源
			
			await base.OnLoadComplete();
		}
		public override void AfterInitComponent()
		{
			//注册按钮以及其它初始化
			
			base.AfterInitComponent();
		}

        public override void OnStart()
        {
            base.OnStart();
            UniTask.Create(InitList);
        }

        async UniTask InitList()
        {
            mDataList.Clear();
            int[] ints = Cfg.Const.GetIntRow(24);
            for (int i = 0; i < ints.Length; i++) 
            {
                mDataList.Add(ints[i]);
            }

            var dic = CityData.Ins.buildingInfoDic;
            foreach (var item in dic)
            {
                var buildingInfo = item.Value;
                if (buildingInfo.unitList != null)
                {
                    var buildingId = buildingInfo.id;
                    if (!mYieldInfos.TryGetValue(buildingId, out var yieldInfo))
                    {
                        yieldInfo = new BuildingYieldInfo(buildingId);
                        mYieldInfos.Add(buildingId, yieldInfo);
                    }
                    for (int i = 0; i < buildingInfo.unitList.Count; i++)
                    {
                        var unit = buildingInfo.unitList[i];
                        var info = unit.GetBuildingUnitLevelInfo();
                        if (info != null)
                        {
                            if (info.basic_yield.Length > 1)
                            {
                                int resId = info.basic_yield[0];
                                int yieldCount = info.basic_yield[1];

                                mResIds.Add(resId);

                               
                                var resYieldCnts = yieldInfo.resYiledCnts;
                                if (!resYieldCnts.TryGetValue(resId, out var cacheYieldCnt)) { 
                                    resYieldCnts.Add(resId, yieldCount);
                                }
                                else
                                {
                                    resYieldCnts[resId] += yieldCount;
                                }

                                var cutTime = info.basic_time;
                                if (cutTime != null && cutTime.Length > 2)
                                {
                                    var cutDown = UtilTool.GetVal(cutTime[1], cutTime[2]);
                                    yieldInfo.yieldTime -= cutDown;
                                }
                            }
                            
                        }

                        
                    }
                }
            }

            var bInfo = CityData.Ins.GetBuildingInfo(BuildingId.WareHouse);
            if (bInfo != null && bInfo.level > 0)
            {
                var lvInfo = bInfo.GetBuildingLevelInfo();
                if (lvInfo != null)
                {
                    for(int i = 0; i < lvInfo.protect_cnt.Length; i++)
                    {
                        mProtectCountDic.Add(lvInfo.protect_cnt[i][0], lvInfo.protect_cnt[i][1]);
                    }
                }
            }

            mList = await CreateLoopGrid<UICityResOverviewItem>(sv_content, mDataList.Count, OnRefreshItem);

        }

        void OnRefreshItem(WLoopGridView grid, int index, WLoopItem loopItem)
        {
            if (index >= mDataList.Count) return;

            UICityResOverviewItem item = loopItem as UICityResOverviewItem;
            int id = mDataList[index];
            var itemCfg = Cfg.Item.GetData(id);
            if (itemCfg == null) return;
            int pCount = 0;
            bool showProtect = false;
            
            int unsafeCount = 0;
            

            float yieldSpeed = 0;
            if (itemCfg.item_sheet == GameDefine.ItemSheetType.itemCombine)
            {
                var cfg = Cfg.ItemCombine.GetData(id);
                if (cfg != null)
                {
                    for (int i = 0; i < cfg.currency_combine.Length; i++) 
                    {
                        int itemId = cfg.currency_combine[i];
                        
                        var resSpeed = GetResYieldSpeed(itemId);
                        yieldSpeed += resSpeed; 
                       

                        if (!showProtect && mProtectCountDic.TryGetValue(itemId, out pCount))
                        {
                            unsafeCount = BagMgr.Ins.GetItemCount(itemId);
                            showProtect = true;
                        }
                    }
                }
            }
            else if (mResIds.Contains(id))
            {

                yieldSpeed = GetResYieldSpeed(id);

                if (!showProtect && mProtectCountDic.TryGetValue(id, out pCount))
                {
                    unsafeCount = BagMgr.Ins.GetItemCount(id);
                    showProtect = true;
                }
            }
            
            
            if(yieldSpeed == 0)
            {
                var resYieldCnt = GetResYieldCnt(id);
                if(resYieldCnt > 0)
                {
                    yieldSpeed = Cfg.Const.GetVal(19);
                }
                else
                {
                    yieldSpeed = 0;
                }
            }
            
            

            //可掠夺资源 = 非安全资源数量 - 仓库保护资源数量上限
            int showCount = unsafeCount > pCount ? unsafeCount - pCount : 0;
            DLogger.Log("Data {0} {1}", unsafeCount, pCount);
            item.SetData(id, Mathf.FloorToInt(yieldSpeed * 3600), index, showProtect, showCount);
        }

        float GetResYieldCnt(int resId)
        {
            float ans = 0;
            foreach (var yieldInfo in mYieldInfos.Values)
            {
                var resCnts = yieldInfo.resYiledCnts;
                // 最低1秒
                if (resCnts.TryGetValue(resId, out var yieldCnt))
                {
                    ans += yieldCnt;
                }
            }
            return ans;
        }

        float GetResYieldSpeed(int resId)
        {
            float ans = 0;
            foreach(var yieldInfo in mYieldInfos.Values)
            {
                var resCnts = yieldInfo.resYiledCnts;
                // 最低1秒
                var yieldTime = Mathf.Max(1, yieldInfo.yieldTime);
                if(resCnts.TryGetValue(resId, out var yieldCnt))
                {
                    var rate = GetResYieldCntBuffRate(resId);
                    var finalCnt = yieldCnt * (1 + rate);
                    var speed = finalCnt / yieldTime;
                    ans += speed;
                }
            }
            return ans;
        }

        float GetResYieldCntBuffRate(int resId) {

            int attr = 0;
            switch (resId)
            {
                case GameDefine.SpecItemId.Water:
                    attr = BuffAttritube.WATER_PRODUCE_ADD_RATE;
                    break;

                case GameDefine.SpecItemId.Electricity:
                    attr = BuffAttritube.ELECTRICITY_PRODUCE_ADD_RATE;
                    break;

                case GameDefine.SpecItemId.Stone:
                    attr = BuffAttritube.STONE_PRODUCE_ADD_RATE;
                    break;
            }

            float rate = BuffEffMgr.Ins.GetBuffValue(attr);

            return rate;
        }
    }
}
