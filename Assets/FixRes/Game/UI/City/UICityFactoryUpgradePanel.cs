using System;
using Core;
using Cysharp.Threading.Tasks;
using Game;
using Game.City;
using Game.Framework;
using GameMain;
using System.Collections.Generic;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;

namespace GameUI
{
    public partial class UICityFactoryUpgradePanel : UIPanel
    {
        string _upgradeEffItemPath => CityUtils.GetCityUIItemPath("CityFactoryUpgradeEffItem");
        
        RectTransform rect_condiction_sv;
        
        private BuildingInfo buildingInfo;
        bool bIsItemEnough = false;
        
        List<UICityBuildingUpgradeEffItem> effItems = new List<UICityBuildingUpgradeEffItem>();
        List<UICityBuildingUpgradeConditionItem> conditionItems = new List<UICityBuildingUpgradeConditionItem>();
        
        public override async UniTask OnLoadComplete()
        {
            //主动加载异步资源
            
            await base.OnLoadComplete();
        }
        
        public override void AfterInitComponent()
        {
            //注册按钮以及其它初始化
            btn_upgrade.AddOnClick(OnClickUpgrade);
            btn_immediately.AddOnClick(OnClickImmediately);
            rect_condiction_sv = sv_condition_scrollview.GetComponent<RectTransform>();
            
            base.AfterInitComponent();
        }
        
        override public void OnStart()
        {
            CityMgr.Ins.SetFingerGesturesEnabled(false);
            
            buildingInfo = UIMgr.Ins.GetPanelData(panelId)[0] as BuildingInfo;
            RefreshPanel();
            
            EventManager.Ins.RegisterEvent(UIEventType.OnItemAdd, this, OnItemChange);
            CityMgr.Ins.SetFingerGesturesEnabled(false);
        }
        
        public override void AfterClose()
        {
            base.AfterClose();
            CityMgr.Ins.SetFingerGesturesEnabled(true);
        }
        
        public override void OnClose()
        {
            base.OnClose();
            foreach (var item in effItems)
            {
                Destroy(item.gameObject);
            }
            
            foreach (var item in conditionItems)
            {
                Destroy(item.gameObject);
            }
            
            effItems.Clear();
            conditionItems.Clear();
        }
        
        void OnItemChange()
        {
            RefreshCondition();
        }
        
        async UniTask RefreshPanel()
        {
            if (buildingInfo != null)
            {
                //txt_title.text = Localization.ToFormat(LanguageCode.__CODE__1000045, Localization.GetValue(buildingInfo.cfg.name), buildingInfo.level + 1);
                txt_time.text = TimeUtils.GetTimeStrDHMS(buildingInfo.GetBuildingLevelInfo().upgrade_time);
            }
            
            int itemCount = BagMgr.Ins.GetItemCount(GameDefine.SpecItemId.Gold);
            int needCount = GetImmediatelyCost();
            bIsItemEnough = itemCount >= needCount;
            
            txt_cost.text = UIHelper.GetNumberStringWithColor(needCount, itemCount);
            
            await UniTask.Create(ShowBuildEff);
            await UniTask.Create(ShowBuildCondition);
            LayoutRebuilder.MarkLayoutForRebuild(rect_layout);
        }
        
        async UniTask ShowBuildEff()
        {
            BuildingLevel buildingLevel = CityData.Ins.GetBuildingLevelInfo(buildingInfo.id, buildingInfo.level);
            BuildingLevel nextBuildingLevel = CityData.Ins.GetBuildingLevelInfo(buildingInfo.id, buildingInfo.level + 1);
            
            GameObject[] go = await GameResMgr.Ins.LoadAndCreateObject(_upgradeEffItemPath, rect_upgrade_eff_bg,
                buildingLevel.attribute_add.Length + 1);
            
            //战力
            UICityBuildingUpgradeEffItem effItem = await AddSubItem<UICityBuildingUpgradeEffItem>(go[0]);
            effItem.SetEff(Localization.ToFormat(
                    LanguageCode.__CODE__1000044),
                UtilTool.GetIntValStr(buildingLevel.power),
                UtilTool.GetIntValStr(nextBuildingLevel.power - buildingLevel.power));
            effItems.Add(effItem);
            //额外加成
            for (int i = 0; i < buildingLevel.attribute_add.Length; i++)
            {
                effItem = await AddSubItem<UICityBuildingUpgradeEffItem>(go[i + 1]);
                int arrtId = buildingLevel.attribute_add[i];
                int nextAttrId = nextBuildingLevel.attribute_add[i];
                var buffEffect = Cfg.BuffEffect.GetData(arrtId);
                
                string curStr = Cfg.BuffEffect.GetBuffEffectValueStr(arrtId);
                string changValStr = Cfg.BuffEffect.GetBuffChangeValStr(arrtId, nextAttrId);
                effItem.SetEff(Localization.ToFormat(buffEffect.effectName), curStr, changValStr);
            }
        }
        
        async UniTask ShowBuildCondition()
        {
            BuildingLevel buildingLevel = buildingInfo.GetBuildingLevelInfo();
            int itemCount = buildingLevel.upgrade_building.Length + buildingLevel.upgrade_cost.Length;
            
            GameObject[] go = await GameResMgr.Ins.LoadAndCreateObject(sv_condition_scrollview.itemName, null, itemCount);
            
            for (int i = 0; i < buildingLevel.upgrade_building.Length; i++)
            {
                UICityBuildingUpgradeConditionItem conditionItemBuild = await AddSubItem<UICityBuildingUpgradeConditionItem>(go[0]);
                conditionItemBuild.transform.SetParent(rect_svcontent, false);
                conditionItems.Add(conditionItemBuild);
            }
            
            for (int i = 0; i < buildingLevel.upgrade_cost.Length; i++)
            {
                UICityBuildingUpgradeConditionItem conditionItemBuild = await AddSubItem<UICityBuildingUpgradeConditionItem>(go[i + buildingLevel.upgrade_building.Length]);
                conditionItemBuild.transform.SetParent(rect_svcontent, false);
                conditionItems.Add(conditionItemBuild);
            }
            
            RefreshCondition();
        }
        
        void RefreshCondition()
        {
            BuildingLevel buildingLevel = buildingInfo.GetBuildingLevelInfo();
            for (int i = 0; i < buildingLevel.upgrade_building.Length; i++)
            {
                UICityBuildingUpgradeConditionItem conditionItemBuild = conditionItems[i];
                
                conditionItemBuild.SetCondition(BuildCondition.BuildingLevel, buildingLevel.upgrade_building[i]);
                conditionItemBuild.AddClickBtnGoto(OnClickBtnGoto);
            }
            
            for (int i = buildingLevel.upgrade_building.Length; i < buildingLevel.upgrade_building.Length + buildingLevel.upgrade_cost.Length; i++)
            {
                UICityBuildingUpgradeConditionItem conditionItemBuild = conditionItems[i];
                
                conditionItemBuild.SetCondition(BuildCondition.Cost, buildingLevel.upgrade_cost[i - buildingLevel.upgrade_building.Length]);
                conditionItemBuild.AddClickBtnGetMore(OnClickBtnGetMore);
            }
            
            conditionItems.Sort((a, b) =>
            {
                if (a.IsClear != b.IsClear)
                {
                    return a.IsClear.CompareTo(b.IsClear);
                }
                
                return a.BuildCondition.CompareTo(b.BuildCondition);
            });
            
            for (int i = 0; i < conditionItems.Count; i++)
            {
                conditionItems[i].transform.SetSiblingIndex(i);
            }
            
            float conditionScrollviewHeight = 0;
            if (conditionItems.Count >= 5)
            {
                conditionScrollviewHeight = 476;
            }
            else
            {
                switch (conditionItems.Count)
                {
                    case 4:
                        conditionScrollviewHeight = 378;
                        break;
                    case 3:
                        conditionScrollviewHeight = 302;
                        break;
                    case 2:
                        conditionScrollviewHeight = 200;
                        break;
                    case 1:
                        conditionScrollviewHeight = 98;
                        break;
                }
            }
            
            rect_condiction_sv.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, conditionScrollviewHeight);
        }
        
        private void OnClickBtnGoto(int[] param)
        {
            Close();
            int unlockBuildingId = param[0];
            CityMgr.Ins.FocusBuilding(unlockBuildingId);
        }
        
        public bool CheckBuildCondition()
        {
            var cfg = buildingInfo.GetBuildingLevelInfo();
            int[][] unlockBuildingConditions = cfg.upgrade_building;
            for (int i = 0; i < unlockBuildingConditions.Length; i++)
            {
                var condition = unlockBuildingConditions[i];
                if (condition.Length > 0)
                {
                    int unlockBuildingId = condition[0];
                    int unlockLevel = condition[1];
                    BuildingInfo conditionBuildingInfo = CityData.Ins.GetBuildingInfo(unlockBuildingId);
                    
                    bool ret = conditionBuildingInfo.level >= unlockLevel;
                    if (!ret)
                    {
                        TipsMgr.Ins.ShowTips(Localization.ToFormat(LanguageCode.TipsLevelNotEnough
                            , Localization.ToFormat(conditionBuildingInfo.cfg.name), unlockLevel));
                    }
                    
                    return ret;
                }
            }
            
            return true;
        }
        
        public bool CheckResCondition()
        {
            BuildingLevel cfg = buildingInfo.GetBuildingLevelInfo();
            var upgradeCost = cfg.upgrade_cost;
            for (int i = 0; i < upgradeCost.Length; i++)
            {
                int itemId = upgradeCost[i][0];
                int itemCount = upgradeCost[i][1];
                if (BagMgr.Ins.GetItemCount(itemId) < itemCount)
                {
                    //DLogger.Log("itemId " + itemId + " " + BagMgr.Ins.GetItemCount(itemId) + " itemCount " + itemCount);
                    return false;
                }
            }
            
            return true;
        }
        
        public List<KeyValuePair<int, int>> GetNotEngoughtRes()
        {
            List<KeyValuePair<int, int>> res = new List<KeyValuePair<int, int>>();
            BuildingLevel cfg = buildingInfo.GetBuildingLevelInfo();
            var upgradeCost = cfg.upgrade_cost;
            for (int i = 0; i < upgradeCost.Length; i++)
            {
                int itemId = upgradeCost[i][0];
                int itemCount = upgradeCost[i][1];
                if (BagMgr.Ins.GetItemCount(itemId) < itemCount)
                {
                    res.Add(new KeyValuePair<int, int>(itemId, itemCount));
                }
            }
            
            return res;
        }
        
        int GetImmediatelyCost()
        {
            long cost = 0;
            BuildingLevel cfg = buildingInfo.GetBuildingLevelInfo();
            var upgradeCost = cfg.upgrade_cost;
            for (int i = 0; i < upgradeCost.Length; i++)
            {
                int itemId = upgradeCost[i][0];
                int itemCount = upgradeCost[i][1];
                int needCount = itemCount; // - BagMgr.Ins.GetItemCount(itemId);
                if (needCount > 0)
                {
                    int typeId = 0;
                    switch (itemId)
                    {
                        case GameDefine.SpecItemId.Water:
                            typeId = 1;
                            break;
                        case GameDefine.SpecItemId.Electricity:
                            typeId = 2;
                            break;
                        case GameDefine.SpecItemId.Stone:
                            typeId = 3;
                            break;
                    }
                    
                    cost += BagMgr.Ins.CalcUseCost(typeId, needCount, false);
                }
            }
            
            cost += BagMgr.Ins.CalcUseCost(ImmediatelyCostType.Time, buildingInfo.GetBuildingLevelInfo().upgrade_time, false);
            int result = (int)Math.Ceiling(cost / 100000.0);
            return result;
        }
        
        void OnClickBtnGetMore()
        {
            UIMgr.Ins.Open(UIDefine.BagGetMorePanel, GetNotEngoughtRes());
        }
        
        void OnClickUpgrade()
        {
            if (!CheckBuildCondition())
            {
                // TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000129);
                return;
            }
            
            if (!CheckResCondition())
            {
                UIMgr.Ins.Open(UIDefine.BagGetMorePanel, GetNotEngoughtRes());
                return;
            }
            
            CityMgr.Ins.SendReqBuildingLvUp(buildingInfo.id);
            Close();
            UIMgr.Ins.ClosePanel(UIDefine.CityFactoryPanel);
        }
        
        void OnClickImmediately()
        {
            if (!bIsItemEnough)
            {
                TipsMgr.Ins.ShowTips(LanguageCode.ItemNotEnough);
                return;
            }
            
            CityMgr.Ins.SendReqBuildingImmediateComplete(buildingInfo.id);
        }
    }
}