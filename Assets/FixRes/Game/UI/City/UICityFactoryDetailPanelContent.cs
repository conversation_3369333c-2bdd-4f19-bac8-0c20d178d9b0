using UnityEngine;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UICityFactoryDetailPanel : UIPanel
	{
		protected RectTransform rect_root;
		protected RectTransform rect_bg;
		protected RectTransform rect_content;
		protected TMPro.TextMeshProUGUI txt_desc;
		protected WScrollRect sv_details;
		public override void InitComponent()
		{
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			rect_bg = transform.Find("rect_root/rect_bg").GetComponent<RectTransform>();
			rect_content = transform.Find("rect_root/rect_content").GetComponent<RectTransform>();
			txt_desc = transform.Find("rect_root/rect_content/txt_desc").GetComponent<TMPro.TextMeshProUGUI>();
			sv_details = transform.Find("rect_root/rect_content/sv_details").GetComponent<WScrollRect>();
			base.InitComponent();
		}
		public override Transform GetAniRoot()
		{
			return rect_root;
		}
	}
}
