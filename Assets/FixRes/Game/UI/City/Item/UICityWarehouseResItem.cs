using Core;
using Cysharp.Threading.Tasks;
using GameMain;
using SuperScrollView;
using UIFramework;
using UnityEngine;

namespace GameUI
{
	public partial class UICityWarehouseResItem : UIItem
	{
		//初始化（具体参数各系统自行添加）
		public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
			
		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			
			
		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{
			
		}

		public async UniTask RefreshContent(int itemId, int curVal, int addVal, int index)
		{
			await UIHelper.LoadItemIconAsync(itemId, img_icon, this);
			txt_val.text = Localization.ToFormat(LanguageCode.AddVal_p, curVal, addVal);
			
			go_bg.SetActiveEx(index % 2 == 1);

            var cfg = Cfg.Item.GetData(itemId);
			if (cfg != null)
			{
                txt_name.text = Localization.ToFormat(cfg.name);
            }
        }
	}
}
