using Cysharp.Threading.Tasks;
using GameMain;
using SuperScrollView;
using UIFramework;

namespace GameUI
{
	public partial class UICityStarShipResCostItem : WLoopItem
	{
		int mItemId = 0;
		int mCost = 0;

		//初始化（具体参数各系统自行添加）
		public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
			
		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			
			
		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{
            mItemId = (int)data[0];
			mCost = (int)data[1];

			txt_val.text = Localization.ToFormat(LanguageCode.Perminute, mCost);
			RefreshAsync(RefreshImg);
		}

		async UniTaskVoid RefreshImg()
		{
			await UIHelper.LoadItemIconAsync(mItemId, img_icon, this);
		}
	}
}
