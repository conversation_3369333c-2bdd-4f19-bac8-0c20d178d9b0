using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UICityFactorySoldierItem : WLoopItem
	{
		protected WImage img_bg;
		protected WImage img_Icon;
		protected WImage img_kuang;
		protected WImage img_lvbg;
		protected TMPro.TextMeshProUGUI txt_lv;
		protected RectTransform rect_bow;
		protected TMPro.TextMeshProUGUI txt_Count;
		protected RectTransform rect_Select;
		protected UIButton btn_click;
		protected GameObject go_up;
		protected GameObject go_lock;
		public override void InitComponent()
		{
			img_bg = transform.Find("img_bg").GetComponent<WImage>();
			img_Icon = transform.Find("img_bg/img_Icon").GetComponent<WImage>();
			img_kuang = transform.Find("img_bg/img_kuang").GetComponent<WImage>();
			img_lvbg = transform.Find("img_bg/img_lvbg").GetComponent<WImage>();
			txt_lv = transform.Find("img_bg/img_lvbg/txt_lv").GetComponent<TMPro.TextMeshProUGUI>();
			rect_bow = transform.Find("rect_bow").GetComponent<RectTransform>();
			txt_Count = transform.Find("rect_bow/img_/txt_Count").GetComponent<TMPro.TextMeshProUGUI>();
			rect_Select = transform.Find("rect_Select").GetComponent<RectTransform>();
			btn_click = transform.Find("btn_click").GetComponent<UIButton>();
			go_up = transform.Find("go_up").gameObject;
			go_lock = transform.Find("go_lock").gameObject;
		}
	}
}
