using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UICityRepairProgressItem : UIComFollowItem
	{
		protected GameObject go_progress;
		protected Slider slider_progress;
		protected TMPro.TextMeshProUGUI txt_time;
		protected UIButton btn_help;
		public override void InitComponent()
		{
			go_progress = transform.Find("go_progress").gameObject;
			slider_progress = transform.Find("go_progress/slider_progress").GetComponent<Slider>();
			txt_time = transform.Find("go_progress/slider_progress/txt_time").GetComponent<TMPro.TextMeshProUGUI>();
			btn_help = transform.Find("btn_help").GetComponent<UIButton>();
		}
	}
}
