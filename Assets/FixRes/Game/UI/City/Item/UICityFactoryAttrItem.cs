using Cysharp.Threading.Tasks;
using SuperScrollView;
using UIFramework;

namespace GameUI
{
	public partial class UICityFactoryAttrItem : WLoopItem
	{
		//初始化（具体参数各系统自行添加）
		public override void Init(params object[] param)
		{

		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{


		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{


		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{

		}

		public void SetData(int value, float rate)
		{
			txt_value.text = value.ToString();
			slider_attr.value = rate;
		}
	}
}
