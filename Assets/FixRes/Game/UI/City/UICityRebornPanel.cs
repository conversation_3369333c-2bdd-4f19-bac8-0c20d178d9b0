using System.Collections.Generic;
using Core;
using Cysharp.Threading.Tasks;
using Game;
using GameMain;
using Packet;
using SuperScrollView;
using UIFramework;
using UnityEngine;
using static GameDefine;
using static GameMain.SoldierRebornMgr;

namespace GameUI
{
    public partial class UICityRebornPanel : UIPanel
    {
        
        private WLoopGridView m_GridView;
        private Dictionary<int, SoldierRebornInfo> m_Soldiers;
        private List<SoldierRebornInfo> m_SoldierList;
        private Dictionary<int, int> m_SoldierCnts = new Dictionary<int, int>();
        private WImage m_BtnFastImg;
        private WImage m_ProgressImg;
        private TMPro.TextMeshProUGUI m_BtnAllTxt;
        private int kSelectAll = LanguageCode.__CODE__1000379;
        private int kCancel = LanguageCode.__CODE__1000380;
        
        public override async UniTask OnLoadComplete()
        {
            //主动加载异步资源
            await base.OnLoadComplete();
        }
        
        public override void AfterInitComponent()
        {
            //注册按钮以及其它初始化
            RegisterEventHandlers();
            btn_all.AddOnClick(OnBtnAllClick);
            m_BtnFastImg = btn_fast.GetComponent<WImage>();
            m_BtnAllTxt = btn_all.transform.Find("txt_btn_name").GetComponent<TMPro.TextMeshProUGUI>();
            m_ProgressImg = go_loyal_progress.GetComponent<WImage>();
            btn_fast.AddOnClick(OnBtnFastClick);
            btn_reborn.AddOnClick(OnBtnRebornClick);
            btn_more_loyal.AddOnClick(OnMoreLoyalClick);
            base.AfterInitComponent();
        }
        
        private void OnBtnRebornClick()
        {
            var has = BagMgr.Ins.GetItemCount(SpecItemId.Loyalty);
            var need = GetLoyaltyCost();
            var isLoyaltyEnough = has >= need;
            if (isLoyaltyEnough)
            {
                var robots = GetKvList();
                if (robots.Count > 0)
                {
                    SoldierRebornMgr.Ins.ReqRebornRobot(robots, false);
                }
            }
            else
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000400);
            }
        }
        
        private List<CustomKV> GetKvList()
        {
            var robots = new List<CustomKV>(m_SoldierCnts.Count);
            foreach (var item in m_SoldierCnts)
            {
                if (item.Value > 0)
                {
                    var kv = new CustomKV();
                    kv.Key = (ulong)item.Key;
                    kv.Value = (ulong)item.Value;
                    robots.Add(kv);
                }
            }
            
            return robots;
        }
        
        private void OnBtnFastClick()
        {
            if (CheckLoyalItemEnough())
            {
                var robots = GetKvList();
                if (robots.Count > 0)
                {
                    SoldierRebornMgr.Ins.ReqRebornRobot(robots, true);
                }
            }
            else
            {
                TipsMgr.Ins.ShowTips(LanguageCode.ItemNotEnough);
            }
        }
        
        private bool CheckLoyalItemEnough()
        {
            var need = GetLoyaltyCost();
            var itemToLoyalty = Cfg.Const.GetVal1(80);
            var itemCnt = Mathf.CeilToInt(need * 1.0f / itemToLoyalty);
            var isItemEnough = BagMgr.Ins.IsEnough(SpecItemId.Loyalty, itemCnt);
            return isItemEnough;
        }
        
        private void OnMoreLoyalClick()
        {
            TipsMgr.Ins.ShowTips(LanguageCode.TipsFunctionLocked);
        }
        
        private void OnBtnAllClick()
        {
            var toMax = IsToMax();
            if (toMax)
            {
                RefreshCntList();
            }
            else
            {
                foreach (var item in m_Soldiers)
                {
                    m_SoldierCnts[item.Key] = 0;
                }
            }
            
            m_GridView.RefreshAllShownItem();
            RefreshBottom();
        }
        
        private void RefreshCntList()
        {
            // 按照高等级到低等级能满足的最大忠诚值来选择
            var remainLoyal = BagMgr.Ins.GetItemCount(SpecItemId.Loyalty);
            for (int i = 0; i < m_SoldierList.Count; i++)
            {
                var soldier = m_SoldierList[i];
                var cfg = soldier.cfg;
                var loyalUnit = cfg.cost_loyalty;
                var needLoyal = loyalUnit * soldier.Count;
                var availCnt = 0;
                if (needLoyal > remainLoyal)
                {
                    availCnt = remainLoyal / loyalUnit;
                }
                else
                {
                    availCnt = soldier.Count;
                }
                
                m_SoldierCnts[soldier.Id] = availCnt;
                remainLoyal -= availCnt * loyalUnit;
                if (remainLoyal == 0)
                {
                    break;
                }
            }
        }
        
        private bool IsToMax()
        {
            foreach (var item in m_SoldierCnts)
            {
                var cur = item.Value;
                if (cur > 0)
                {
                    return false;
                }
            }
            
            return true;
        }
        
        private void RegisterEventHandlers()
        {
            EventManager.Ins.RegisterEvent(UIEventType.SyncRebornSoldierList, this, OnSyncRebornSoldierList);
            EventManager.Ins.RegisterEvent(UIEventType.RspRebornRobot, this, OnRspRebornRobot);
            EventManager.Ins.RegisterEvent(UIEventType.RspDeleteRebornRobot, this, OnRspDeleteRebornRobot);
            EventManager.Ins.RegisterEvent(UIEventType.OnItemAdd, this, OnItemAdd);
        }
        
        private void OnRspDeleteRebornRobot()
        {
            RefreshView();
        }
        
        private void OnRspRebornRobot()
        {
            RefreshView();
        }
        
        private void OnItemAdd()
        {
            RefreshView();
        }
        
        private void OnSyncRebornSoldierList()
        {
            RefreshView();
        }
        
        public override void OnStart()
        {
            base.OnStart();
            UniTask.Create(CreateItems).ContinueWith(() => { RefreshView(); }).Forget();
        }
        
        private void RefreshView()
        {
            RefreshBanner();
            m_Soldiers = SoldierRebornMgr.Ins.GetSoldiers();
            if (m_Soldiers.Count == 0)
            {
                rect_empty.SetActiveEx(true);
                rect_bottom.SetActiveEx(false);
                rect_sv.SetActiveEx(false);
                go_sv_bg.SetActiveEx(false);
                return;
            }
            else
            {
                go_sv_bg.SetActive(true);
                rect_empty.SetActiveEx(false);
                rect_bottom.SetActiveEx(true);
                rect_sv.SetActiveEx(true);
            }
            
            RefreshGridView();
            RefreshBottom();
        }
        
        
        private void RefreshBottom()
        {
            RefreshCost();
            RefreshBtns();
        }
        
        private void RefreshBtns()
        {
            var has = BagMgr.Ins.GetItemCount(SpecItemId.Loyalty);
            var need = GetLoyaltyCost();
            var isLoyaltyEnough = has >= need;
            var itemCnt = 0;
            var isItemEnough = false;
            if (!isLoyaltyEnough)
            {
                var itemToLoyalty = Cfg.Const.GetVal1(80);
                itemCnt = Mathf.CeilToInt(need * 1.0f / itemToLoyalty);
                isItemEnough = BagMgr.Ins.IsEnough(SpecItemId.LoyaltyItem, itemCnt);
            }
            
            // 忠诚值不足 且 道具也不足 红色
            string color = !isLoyaltyEnough && !isItemEnough ? "#D63A3B" : "#4ECB35";
            txt_fast_cost.text = $"<color={color}>{itemCnt}</color>";
            

            m_BtnFastImg.SetImgGray(isLoyaltyEnough);
            go_gary_txt.SetActive(isLoyaltyEnough);
            btn_fast.interactable = !isLoyaltyEnough;
            
            m_BtnAllTxt.text = IsToMax() ? Localization.GetValue(kSelectAll) : Localization.GetValue(kCancel);
        }
        
        
        private void RefreshCost()
        {
            var cur = BagMgr.Ins.GetItemCount(SpecItemId.Loyalty);
            var max = GetLoyaltyCost();
            var isEnough = cur >= max;
            var color = isEnough ? "#F5EEE6" : "#D63A3B";
            txt_loyal_cost.text = $"<color={color}>{cur}</color>/{max}";
        }
        
        private int GetLoyaltyCost()
        {
            var reader = Cfg.Soldier;
            var sum = 0;
            foreach (var item in m_SoldierCnts)
            {
                var id = item.Key;
                var cnt = item.Value;
                var cfg = reader.GetData(id);
                if (cfg != null)
                {
                    sum += cnt * cfg.cost_loyalty;
                }
            }
            
            return sum;
        }
        
        private void RefreshGridView()
        {
            m_SoldierCnts.Clear();
            m_SoldierList = SoldierRebornMgr.Ins.GetSoldierList();
            RefreshCntList();
            
            m_GridView.SetListItemCount(m_SoldierList.Count);
            m_GridView.RefreshAllShownItem();
        }
        
        private void RefreshBanner()
        {
            var curLoyalty = BagMgr.Ins.GetItemCount(SpecItemId.Loyalty);
            var maxLoyalty = SoldierRebornMgr.Ins.GetMaxLoyalty();
            txt_loyal_cnt.text = $"{curLoyalty}<size=30><color=#ABE1EF>/{maxLoyalty}</color></size>";
            if (maxLoyalty != 0)
            {
                m_ProgressImg.fillAmount = (float)curLoyalty / maxLoyalty;
            }
            else
            {
                m_ProgressImg.fillAmount = 0;
            }
            
            var accSpeed = SoldierRebornMgr.Ins.GetAccLoyalSpeed();
            txt_loyal_speed.text = Localization.ToFormat(LanguageCode.__CODE__1000420, accSpeed);
            var maxCnt = SoldierRebornMgr.Ins.GetMaxSoldierCnt();
            var curCnt = SoldierRebornMgr.Ins.GetSoldierCnt();
            txt_soldier_cnt.text = $"{curCnt}/{maxCnt}";
            RefreshLoyalItemRes();
            
            var loyalCfg = Cfg.Item.GetData(SpecItemId.Loyalty);
            if (loyalCfg != null)
            {
                img_loyal_icon.AsyncSetAtlasSprite("Icon", loyalCfg.icon).Forget();
                //img_loyal_icon_1.AsyncSetAtlasSprite("Soldier", loyalCfg.icon).Forget();
            }
        }
        
        private void RefreshLoyalItemRes()
        {
            var curLoyalty = BagMgr.Ins.GetItemCount(SpecItemId.LoyaltyItem);
            txt_loyal_num.text = curLoyalty.ToString();
            var cfg = Cfg.Item.GetData(SpecItemId.LoyaltyItem);
            if (cfg != null)
            {
                img_loyal.AsyncSetAtlasSprite("Icon", cfg.icon).Forget();
                img_cost.AsyncSetAtlasSprite("Icon", cfg.icon).Forget();
            }
        }
        
        private async UniTask CreateItems()
        {
            LoopGridViewSettingParam param = new LoopGridViewSettingParam();
            param.mItemPadding = new Vector2(0, 10);
            param.mPadding = new RectOffset(0, 0, 10, 0);
            m_GridView = await CreateLoopGrid<UICityRebornItem>(sv_robots, 0, OnRefreshItem, param);
        }
        
        private void OnRefreshItem(WLoopGridView grid, int index, WLoopItem loopItem)
        {
            var item = loopItem as UICityRebornItem;
            var repairSoldier = m_SoldierList[index];
            m_SoldierCnts.TryGetValue(repairSoldier.Id, out var soldierCnt);
            item.SetData(repairSoldier, OnItemCntChange, soldierCnt);
        }
        
        private void OnItemCntChange(int soldierId, int cnt)
        {
            m_SoldierCnts[soldierId] = cnt;
            RefreshBottom();
        }
        
        public override void AfterClose()
        {
            base.AfterClose();
            CityMgr.Ins.SetFingerGesturesEnabled(true, true);
        }
    }
}