using System;
using System.Collections.Generic;
using Core;
using Cysharp.Threading.Tasks;
using Game;
using GameMain;
using MiniAnimation;
using UIFramework;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UICityFactoryPromotionPanel : UIPanel
	{
	    UICityFactorySoldierItem from_item;
		UICityFactorySoldierItem to_item;
		int _factoryBuildingId;
		int _fromSoldierId;
		int _toSoldierId;

		int _curPromotionCount;

		int _maxPromotionCount;

		UIBagUseAllInfoItem[] _costItems;
		public override async UniTask OnLoadComplete()
		{
			from_item =await AddSubItem<UICityFactorySoldierItem>(UIItemDefine.CityFactorySoldierItem, rect_from);
			to_item = await AddSubItem<UICityFactorySoldierItem>(UIItemDefine.CityFactorySoldierItem, rect_to);
			await base.OnLoadComplete();
		}
		public override void AfterInitComponent()
		{
			btn_immediately.AddOnClick(OnClickImmediately);
			btn_upgrade.AddOnClick(OnClickUpgrade);

			slider_num.onValueChanged.AddListener(OnSliderValueChanged);
			btn_min.AddOnClick(OnClickBtnMin);
			btn_plus.AddOnClick(OnClickBtnPlus);

			_costItems = rect_cost_layout.GetComponentsInChildren<UIBagUseAllInfoItem>();
			for (int i = 0; i < _costItems.Length; i++)
			{
				AddSubItem<UIBagUseAllInfoItem>(_costItems[i].gameObject);
			}

			from_item.InitComponent();
			from_item.AfterInitComponent();

			to_item.InitComponent();
			to_item.AfterInitComponent();

			EventManager.Ins.RegisterEvent(UIEventType.OnItemAdd, this, RefreshPanel);

			base.AfterInitComponent();
		}

		private void OnClickBtnPlus()
		{
			SetSlider(_curPromotionCount + 1);
		}

		private void OnClickBtnMin()
		{
			SetSlider(_curPromotionCount - 1);

		}

		public override void OnStart()
		{
			base.OnStart();
			_factoryBuildingId = (int)GetPanelData()[0];
			_fromSoldierId = (int)GetPanelData()[1];
			_toSoldierId = SoldierMgr.Ins.GetBuildingMaxLevleSoldierId(_factoryBuildingId, out var _);

			from_item.SetSoldierData(_fromSoldierId);
			to_item.SetSoldierData(_toSoldierId);
			from_item.SetBowActive(false);
			to_item.SetBowActive(false);

			string fromName = Localization.GetValue(Cfg.Soldier.GetData(_fromSoldierId).name);
			string toName = Localization.GetValue(Cfg.Soldier.GetData(_toSoldierId).name);
			//txt_from_name.text = fromName;
			//txt_to_name.text = toName;

			_maxPromotionCount = SoldierMgr.Ins.GetBuildingPromotionMaxNum(_factoryBuildingId, _fromSoldierId, _toSoldierId);
			slider_num.maxValue = _maxPromotionCount;

			SetPromotionSliderToMax();
		}

		void RefreshPanel()
		{
			OnSliderValueChanged(_curPromotionCount);
		}

		void SetSlider(float value)
		{
			value = Mathf.Clamp(value, slider_num.minValue, slider_num.maxValue);
			slider_num.value = value;
			OnSliderValueChanged(value);
		}

		private void OnSliderValueChanged(float value)
		{
			_curPromotionCount = Mathf.FloorToInt(value);
			//input_num.text = string.Format($"{Mathf.FloorToInt(value)}<size=40><color=#6D6D6D>/{_maxPromotionCount}</color></size>").ToString();
			input_num.text = Mathf.FloorToInt(value).ToString();
			var cost = GetCost();
			for (int i = 0; i < cost.Length; i++)
			{
				int itemId = cost[i][0];
				int needCount = cost[i][1];

				int curCount = BagMgr.Ins.GetItemCount(itemId);
				_costItems[i].SetData(itemId, curCount, needCount);
			}
			for (int i = cost.Length; i < _costItems.Length; i++)
			{
				_costItems[i].SetActiveEx(false);
			}

			txt_cost.text = GetImmediatelyCost().ToString();
			txt_time.text = TimeUtils.GetTimeStrDHMS(GetCostTime());

			UniTask.Void(async () =>
			{
				await UniTask.Yield();
				LayoutRebuilder.MarkLayoutForRebuild(rect_time);
				LayoutRebuilder.MarkLayoutForRebuild(rect_cost);
			});
		}

		int[][] GetCost()
		{
			using (var pooledObject = ListPool<int[]>.Get(out List<int[]> result))
			{
				var fromCfg = Cfg.Soldier.GetData(_fromSoldierId);
				var toCfg = Cfg.Soldier.GetData(_toSoldierId);
				var fromCost = fromCfg.cost;
				var toCost = toCfg.cost;
				for (int i = 0; i < toCost.Length; i++)
				{
					int itemId = toCost[i][0];
					int diffCount = toCost[i][1];
					if (i < fromCost.Length)
					{
						diffCount = toCost[i][1] - fromCost[i][1];
					}
					result.Add(new int[] { itemId, diffCount * _curPromotionCount });
				}
				return result.ToArray();
			}
		}

		void SetPromotionSliderToMax()
		{
			SetSlider(_maxPromotionCount);
		}

		private void OnClickUpgrade()
		{
			if (_curPromotionCount <= 0)
			{
				return;
			}
			if (!CheckResEnough())
			{
				var notEnoughRes = GetNotEnoughRes();
				UIMgr.Ins.Open(UIDefine.BagGetMorePanel, notEnoughRes);
				return;
			}
			SoldierMgr.Ins.SendReqUpgradeRobot(_factoryBuildingId, _fromSoldierId, _curPromotionCount, false);
			Close();
		}

		private void OnClickImmediately()
		{
			if (_curPromotionCount <= 0)
			{
				return;
			}
			int immediatelyCost = GetImmediatelyCost();
			if (!BagMgr.Ins.IsEnough(GameDefine.SpecItemId.Gold, immediatelyCost))
			{
				TipsMgr.Ins.ShowTips(LanguageCode.ItemNotEnough);
				return;
			}
			SoldierMgr.Ins.SendReqUpgradeRobot(_factoryBuildingId, _fromSoldierId, _curPromotionCount, true);
			Close();
		}

		int GetImmediatelyCost()
		{
			var cost = GetCost();
			return BagMgr.Ins.GetImmediatelyCost(cost, _curPromotionCount, GetCostTime());
		}

		int GetCostTime()
		{
			var fromCfg = Cfg.Soldier.GetData(_fromSoldierId);
			var toCfg = Cfg.Soldier.GetData(_toSoldierId);
			int diffTime = toCfg.cost_time - fromCfg.cost_time;
			float rate = SoldierMgr.Ins.GetProduceRate(_factoryBuildingId);
			return Mathf.FloorToInt(diffTime * _curPromotionCount * (1 - rate));
		}
		bool CheckResEnough()
		{
			var cost = GetCost();
			return BagMgr.Ins.CheckResEnough(cost, _curPromotionCount);
		}

		List<KeyValuePair<int, int>> GetNotEnoughRes()
		{
			var cost = GetCost();
			return BagMgr.Ins.GetNotEnoughRes(cost, _curPromotionCount);
		}
	}
}
