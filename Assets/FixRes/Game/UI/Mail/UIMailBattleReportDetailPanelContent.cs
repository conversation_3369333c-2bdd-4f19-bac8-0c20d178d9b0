using UnityEngine;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIMailBattleReportDetailPanel : UIPanel
	{
		protected RectTransform rect_root;
		protected RectTransform rect_bg;
		protected RectTransform rect_content;
		protected RectTransform rect_top;
		protected WImage img_title_bg;
		protected TMPro.TextMeshProUGUI txt_fight_rst;
		protected TMPro.TextMeshProUGUI txt_fight_info;
		protected TMPro.TextMeshProUGUI txt_dead_line_val;
		protected TMPro.TextMeshProUGUI txt_send_time;
		protected UIButton btn_share;
		protected UIButton btn_collect;
		protected WImage wimg_mark_bg;
		protected WScrollRect sv_content;
		protected RectTransform rect_loot;
		protected RectTransform rect_trophy;
		protected RectTransform rect_building_def;
		protected RectTransform rect_solider_rst;
		protected RectTransform rect_hero_compare;
		protected RectTransform rect_gear_compare;
		protected RectTransform rect_solider_compare;
		protected RectTransform rect_attr_rst;
		protected UIButton btn_detail;
		protected RectTransform rect_bottom;
		protected UIButton btn_delete;
		public override void InitComponent()
		{
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			rect_bg = transform.Find("rect_root/rect_bg").GetComponent<RectTransform>();
			rect_content = transform.Find("rect_root/rect_content").GetComponent<RectTransform>();
			rect_top = transform.Find("rect_root/rect_content/rect_top").GetComponent<RectTransform>();
			img_title_bg = transform.Find("rect_root/rect_content/rect_top/img_title_bg").GetComponent<WImage>();
			txt_fight_rst = transform.Find("rect_root/rect_content/rect_top/txt_fight_rst").GetComponent<TMPro.TextMeshProUGUI>();
			txt_fight_info = transform.Find("rect_root/rect_content/rect_top/txt_fight_info").GetComponent<TMPro.TextMeshProUGUI>();
			txt_dead_line_val = transform.Find("rect_root/rect_content/rect_top/txt_dead_line_val").GetComponent<TMPro.TextMeshProUGUI>();
			txt_send_time = transform.Find("rect_root/rect_content/rect_top/txt_send_time").GetComponent<TMPro.TextMeshProUGUI>();
			btn_share = transform.Find("rect_root/rect_content/rect_top/btn_share").GetComponent<UIButton>();
			btn_collect = transform.Find("rect_root/rect_content/rect_top/btn_collect").GetComponent<UIButton>();
			wimg_mark_bg = transform.Find("rect_root/rect_content/rect_top/btn_collect/wimg_mark_bg").GetComponent<WImage>();
			sv_content = transform.Find("rect_root/rect_content/sv_content").GetComponent<WScrollRect>();
			rect_loot = transform.Find("rect_root/rect_content/sv_content/Viewport/Content/rect_loot").GetComponent<RectTransform>();
			rect_trophy = transform.Find("rect_root/rect_content/sv_content/Viewport/Content/rect_trophy").GetComponent<RectTransform>();
			rect_building_def = transform.Find("rect_root/rect_content/sv_content/Viewport/Content/rect_building_def").GetComponent<RectTransform>();
			rect_solider_rst = transform.Find("rect_root/rect_content/sv_content/Viewport/Content/rect_solider_rst").GetComponent<RectTransform>();
			rect_hero_compare = transform.Find("rect_root/rect_content/sv_content/Viewport/Content/rect_hero_compare").GetComponent<RectTransform>();
			rect_gear_compare = transform.Find("rect_root/rect_content/sv_content/Viewport/Content/rect_gear_compare").GetComponent<RectTransform>();
			rect_solider_compare = transform.Find("rect_root/rect_content/sv_content/Viewport/Content/rect_solider_compare").GetComponent<RectTransform>();
			rect_attr_rst = transform.Find("rect_root/rect_content/sv_content/Viewport/Content/rect_attr_rst").GetComponent<RectTransform>();
			btn_detail = transform.Find("rect_root/rect_content/sv_content/Viewport/Content/btn_detail").GetComponent<UIButton>();
			rect_bottom = transform.Find("rect_root/rect_content/rect_bottom").GetComponent<RectTransform>();
			btn_delete = transform.Find("rect_root/rect_content/rect_bottom/btn_delete").GetComponent<UIButton>();
			base.InitComponent();
		}
		public override Transform GetAniRoot()
		{
			return rect_root;
		}
	}
}
