using System.Collections.Generic;
using Core;
using Cysharp.Threading.Tasks;
using Game;
using GameMain;
using TMPro;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;
using MailInfo = GameMain.MailInfo;

namespace GameUI
{
    public partial class UIMailBattleReportDetailPanel : UIPanel
    {
        private MailInfo m_MailInfo;
        private BattleReportInfo m_RptTinyInfo;
        private BattleReportInfo m_RptDetailInfo;
        private MultiRptDetailInfo m_MultiRptDetailInfo;
        private int m_RptId;
        private List<ulong> m_TmpList = new List<ulong>();
        private int kLimitCnt;
        private UIRptLootItem m_LootItem;
        private UIRptTrophyItem m_TrophyItem;
        private UIRptBuildingDefItem m_BuildingDefItem;
        private UIRptSoldierRstItem m_SoldierRstItem;
        private UIRptHeroCompareItem m_HeroCmpItem;
        private UIRptGearCompareItem m_GearCmpItem;
        private UIRptSoldierCompareItem m_SoldierCompareItem;
        private UIRptAttrCompareItem m_AttrCompareItem;
        
        //private 
        public override async UniTask OnLoadComplete()
        {
            //主动加载异步资源
            
            await base.OnLoadComplete();
        }
        
        public override void AfterInitComponent()
        {
            //注册按钮以及其它初始化
            btn_delete.AddOnClick(OnClickDelete);
            btn_collect.AddOnClick(OnClickMark);
            btn_share.AddOnClick(OnClickShare);
            btn_detail.AddOnClick(OnDetailClick);
            
            RegisterEventHandlers();
            base.AfterInitComponent();
        }
        
        private void OnDetailClick()
        {
            if(m_MultiRptDetailInfo != null)
            {
                UIMgr.Ins.Open(UIDefine.RptFightDetailPanel, m_RptDetailInfo.reportId, m_RptDetailInfo.lockSee, m_MultiRptDetailInfo.index);
            }
            else
            {
                UIMgr.Ins.Open(UIDefine.RptFightDetailPanel, m_RptDetailInfo.reportId, m_RptDetailInfo.lockSee);
            }
        }
        
        private void OnClickShare()
        {
            TipsMgr.Ins.ShowTips(LanguageCode.TipsFunctionLocked);
        }
        
        private void OnClickMark()
        {
            if (!m_MailInfo.is_collect) //没收藏
            {
                int totalCount = MailMgr.Ins.GetCollectedMailCount();
                if (totalCount >= kLimitCnt)
                {
                    TipsMgr.Ins.ShowTips(LanguageCode.MailCollectionLImited);
                    return;
                }
            }
            //else
            //{
            //    TipsMgr.Ins.ShowTips(1000126);
            //    return;
            //}
            
            MailMgr.Ins.ReqCollectMail(m_TmpList);
        }
        
        private void OnClickDelete()
        {
            var info = MailMgr.Ins.GetMailInfo(m_MailInfo.id);
            if (info == null) return;
            
            if (info.HaveItemToGet())
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000120);
                return;
            }
            
            MailMgr.Ins.ReqDeleteMail(m_TmpList);
        }
        
        
        private void RegisterEventHandlers()
        {
            EventManager.Ins.RegisterEvent(UIEventType.RspRptDetailInfo, this, OnRspRptDetailInfo);
            EventManager.Ins.RegisterEvent(UIEventType.OnRefreshMailInfo, this, OnRefreshMailInfo);
            EventManager.Ins.RegisterEvent(UIEventType.OnDelMail, this, OnDelMail);
        }
        
        private void OnDelMail(object mailId)
        {
            if (m_MailInfo.id == (ulong)mailId)
                Close();
        }
        
        void OnRefreshMailInfo(object mailId)
        {
            if (m_MailInfo.id == (ulong)mailId)
            {
                RefreshCollect();
            }
        }
        
        private void OnRspRptDetailInfo()
        {
            m_RptDetailInfo = BattleReportMgr.Ins.GetDetailInfo(m_RptTinyInfo);
            if (m_RptDetailInfo != null)
            {
                InitView();
            }
        }

        public override void OnStart()
        {
            base.OnStart();

            var limit = Cfg.Const.GetIntRow2(21);
            for (int i = 0; i < limit.Length; i++)
            {
                if (limit[i][0] == 1)
                {
                    kLimitCnt = limit[i][1];
                    break;
                }
            }

            var param = UIMgr.Ins.GetPanelData(panelId);
            if (param != null && param.Length > 0)
            {
                m_MailInfo = param[0] as MailInfo;
                m_TmpList.Clear();
                m_TmpList.Add(m_MailInfo.id);
                m_RptTinyInfo = m_MailInfo.rpt_tiny_info;
                if(param.Length > 1)
                {
                    m_MultiRptDetailInfo = param[1] as MultiRptDetailInfo;
                }
            }
            
            if (m_RptTinyInfo != null)
            {
                m_RptId = m_RptTinyInfo.reportId;
                m_RptDetailInfo = BattleReportMgr.Ins.GetDetailInfo(m_RptTinyInfo);
                if (m_RptDetailInfo == null)
                {
                    BattleReportMgr.Ins.ReqRptDetailInfo(m_RptId);
                    return;
                }
            }else if(m_MultiRptDetailInfo != null)
            {
                var detailInfo = m_MultiRptDetailInfo.rptDetailInfo;
                if (detailInfo == null)
                {
                    return;
                }
                m_RptDetailInfo = detailInfo;
                m_RptId = m_RptDetailInfo.reportId;
            }


            InitView();

        }

        private void InitView()
        {
            RefreshBanner();
            UniTask.Create(LoadItems).ContinueWith(() =>
            {
                RefreshView();
                sv_content.verticalNormalizedPosition = 1;
            }).Forget();
        }
        
        private void RefreshView()
        {
            if (!m_MailInfo.state)
            {
                MailMgr.Ins.ReqReadMail(m_TmpList);
            }
            RefreshLootItem();
            RefreshTrophyItem();
            RefreshBuildingDefItem();
            RefreshSoldierRstItem();
            RefreshHeroCompareItem();
            RefreshGearCompareItem();
            RefreshSoldierCompareItem();
            RefreshAttrCompareItem();
        }
        
        private void RefreshAttrCompareItem()
        {
            if (m_AttrCompareItem == null)
            {
                return;
            }
            
            m_AttrCompareItem.RefreshView(m_RptDetailInfo);
        }
        
        private void RefreshSoldierCompareItem()
        {
            if (m_SoldierCompareItem == null)
            {
                return;
            }
            
            m_SoldierCompareItem.RefreshView(m_RptDetailInfo);
        }
        private void RefreshGearCompareItem()
        {
            if (m_GearCmpItem == null)
            {
                return;
            }
            m_GearCmpItem.RefreshView(m_RptDetailInfo);
        }
        private void RefreshHeroCompareItem()
        {
            if (m_HeroCmpItem == null)
            {
                return;
            }
            
            m_HeroCmpItem.RefreshView(m_RptDetailInfo);
        }
        
        private void RefreshSoldierRstItem()
        {
            if (m_SoldierRstItem == null)
            {
                return;
            }
            
            m_SoldierRstItem.RefreshView(m_RptDetailInfo);
        }
        private void RefreshBuildingDefItem()
        {
            if (m_BuildingDefItem == null)
            {
                return;
            }
            var buildingDefVal = m_RptDetailInfo.builidngDefVal;
            m_BuildingDefItem.RefreshView(buildingDefVal, m_RptDetailInfo.IsAtk);
        }
        private void RefreshTrophyItem()
        {
            if (m_TrophyItem == null)
            {
                return;
            }
            
            var trophyReward = m_RptDetailInfo.trophyReward;
            m_TrophyItem.RefreshView(trophyReward).Forget();
        }
        
        private void RefreshLootItem()
        {
            if (m_LootItem == null)
            {
                return;
            }
            
            var lootReward = m_RptDetailInfo.lootReward;
            m_LootItem.RefreshView(lootReward, m_RptDetailInfo.IsAtk).Forget();
        }
        
        private void RefreshBanner()
        {
            var mailCfg = Cfg.Mail.GetData(m_MailInfo.cfg_id);
            if (mailCfg != null)
            {
                img_title_bg.AsyncSetSingleSprite(string.Format("Texture/UITex/Mail/{0}.png", mailCfg.banner)).Forget();
            }
            else
            {
                img_title_bg.AsyncSetSingleSprite(string.Format("Texture/UITex/Mail/{0}.png", "img_mail_bg_zwd_banner4_xitong")).Forget();
            }
            
            txt_fight_rst.text = m_RptDetailInfo.FightRst;
            
            //         var posStr = $"{m_RptDetailInfo.fightPos.x},{m_RptDetailInfo.fightPos.y}";
            //txt_fight_info.text = m_RptDetailInfo.FightContent + $"<link={posStr}><u>({m_RptDetailInfo.fightPos.x}, {m_RptDetailInfo.fightPos.y})</u></link>";
            txt_fight_info.text = m_RptDetailInfo.FightContent;
            txt_dead_line_val.text = Localization.ToFormat(LanguageCode.__CODE__1000454,
                TimeUtils.GetTotalTimeStr(m_MailInfo.over_time));
            txt_send_time.text = TimeUtils.GetTotalTimeStr(m_MailInfo.send_time);
            RefreshCollect();
        }
        
        private void RefreshCollect()
        {
            wimg_mark_bg.color = m_MailInfo.is_collect ? UIHelper.MailMarkColor : Color.white;
        }
        
        private async UniTask LoadItems()
        {
            rect_loot.SetActiveEx(m_RptDetailInfo.lootRewardExist);
            if (m_RptDetailInfo.lootRewardExist)
            {
                m_LootItem = await AddSubItem<UIRptLootItem>(UIItemDefine.RptLootItem, rect_loot);
                await UniTask.Yield();
            }
            
            rect_trophy.SetActiveEx(m_RptDetailInfo.trophyRewardExist);
            if (m_RptDetailInfo.trophyRewardExist)
            {
                m_TrophyItem = await AddSubItem<UIRptTrophyItem>(UIItemDefine.RptTrophyItem, rect_trophy);
                await UniTask.Yield();
            }

            rect_building_def.SetActiveEx(m_RptDetailInfo.builidngDefVal > 0);
            if(m_RptDetailInfo.builidngDefVal > 0)
            {
                m_BuildingDefItem = await AddSubItem<UIRptBuildingDefItem>(UIItemDefine.RptBuildingDefItem, rect_building_def);
                await UniTask.Yield();
            }

            m_SoldierRstItem = await AddSubItem<UIRptSoldierRstItem>(UIItemDefine.RptSoldierRstItem, rect_solider_rst);
            await UniTask.Yield();
            
            
            m_HeroCmpItem = await AddSubItem<UIRptHeroCompareItem>(UIItemDefine.RptHeroCompareItem, rect_hero_compare);
            await UniTask.Yield();
            
            m_GearCmpItem = await AddSubItem<UIRptGearCompareItem>(UIItemDefine.RptGearCompareItem, rect_gear_compare);
            await UniTask.Yield();

            m_SoldierCompareItem = await AddSubItem<UIRptSoldierCompareItem>(UIItemDefine.RptSoldierCompareItem, rect_solider_compare);
            await UniTask.Yield();
            
            var demoAttrs = Cfg.Const.GetIntRow(83);
            if (demoAttrs != null && demoAttrs.Length > 0)
            {
                var titleHeight = 113;
                var unitHeight = 80;
                var totalHeight = titleHeight + unitHeight * demoAttrs.Length;
                rect_attr_rst.sizeDelta = new Vector2(rect_attr_rst.sizeDelta.x, totalHeight);
                LayoutRebuilder.MarkLayoutForRebuild(rect_attr_rst.transform as RectTransform);
                m_AttrCompareItem = await AddSubItem<UIRptAttrCompareItem>(UIItemDefine.RptAttrCompareItem, rect_attr_rst);
            }
        }
        
        public override void OnLinkEvent(GameObject go, TMP_LinkInfo linkInfo)
        {
            var linkId = linkInfo.GetLinkID();
            var v2 = linkId.Split(',');
            int.TryParse(v2[0], out var posX);
            int.TryParse(v2[1], out var posY);
            var pos = new Vector2(posX, posY);
            UIHelper.FocusToWorldPos(pos);
            Close();
            UIMgr.Ins.ClosePanel(UIDefine.MailPanel);
        }

        public override void OnClose()
        {
            base.OnClose();
            if (m_LootItem)
            {
                Destroy(m_LootItem);
            }

            if (m_TrophyItem)
            {
                Destroy(m_TrophyItem);
            }

            if (m_SoldierRstItem)
            {
                Destroy(m_SoldierRstItem);
            }

            if (m_HeroCmpItem)
            {
                Destroy(m_HeroCmpItem);
            }

            if (m_GearCmpItem)
            {
                Destroy (m_GearCmpItem);
            }

            if (m_SoldierCompareItem)
            {
                Destroy(m_SoldierCompareItem);
            }
            
            if (m_AttrCompareItem)
            {
                Destroy(m_AttrCompareItem);
            }
        }
    }
}