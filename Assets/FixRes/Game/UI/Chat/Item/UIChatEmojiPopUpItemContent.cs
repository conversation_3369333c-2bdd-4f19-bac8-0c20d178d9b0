using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIChatEmojiPopUpItem : UIItem
	{
		protected UIButton btn_close;
		protected RectTransform rect_root;
		protected RectTransform rect_tab;
		protected WScrollRect sv_list;
		public override void InitComponent()
		{
			btn_close = transform.Find("btn_close").GetComponent<UIButton>();
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			rect_tab = transform.Find("rect_root/rect_tab").GetComponent<RectTransform>();
			sv_list = transform.Find("rect_root/sv_list").GetComponent<WScrollRect>();
		}
	}
}
