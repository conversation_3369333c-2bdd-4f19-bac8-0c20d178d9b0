using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIChatPrivateMsgOpItem : UIItem
	{
		protected UIButton btn_close;
		protected RectTransform rect_root;
		protected GameObject go_arrow;
		protected UIButton btn_top;
		protected UIButton btn_delete;
		public override void InitComponent()
		{
			btn_close = transform.Find("btn_close").GetComponent<UIButton>();
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			go_arrow = transform.Find("rect_root/go_arrow").gameObject;
			btn_top = transform.Find("rect_root/btn_top").GetComponent<UIButton>();
			btn_delete = transform.Find("rect_root/btn_delete").GetComponent<UIButton>();
		}
	}
}
