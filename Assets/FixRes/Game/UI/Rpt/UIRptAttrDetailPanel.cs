using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using GameMain;
using UIFramework;
using UnityEngine;
using static GameUI.UIConfig;
using static GameUI.UIRptAttrCompareItem;

namespace GameUI
{
	public partial class UIRptAttrDetailPanel : UIPanel
	{
        private BattleReportInfo m_Info;
        private List<UIRptAttrItem> m_SubItems;

        public class RptAttrParamEx
        {
            public RptAttrParam param;
            public Dictionary<int, List<int>> atkSourceBuffMap;
            public Dictionary<int, List<int>> defSourceBuffMap;
        }

        public override async UniTask OnLoadComplete()
		{
            //主动加载异步资源
            var demoAttrs = Cfg.Const.GetIntRow(83);
            m_SubItems = new List<UIRptAttrItem>(demoAttrs.Length);

            for (int i = 0; i < demoAttrs.Length; i++)
            {
                var item = await AddSubItem<UIRptAttrItem>(UIItemDefine.RptAttrItem, sv_list.content);
                m_SubItems.Add(item);
            }
            await base.OnLoadComplete();
		}
		public override void AfterInitComponent()
		{
			//注册按钮以及其它初始化
			
			base.AfterInitComponent();
		}

        public override void OnStart()
        {
            base.OnStart();
            var param = UIMgr.Ins.GetPanelData(panelId);
            if (param != null && param.Length > 0)
            {
                m_Info = param[0] as BattleReportInfo;
            }
            if (m_Info == null)
            {
                return;
            }
            if (m_SubItems != null)
            {
                RefreshView();
            }
        }

        private void RefreshView()
        {
            var demoAttrs = Cfg.Const.GetIntRow(83);
            for (int i = 0; i < m_SubItems.Count; i++)
            {
                var attrId = demoAttrs[i];
                var attrCfg = Cfg.Attribute.GetData(attrId);
                if (attrCfg == null)
                {
                    continue;
                }

                var title = Localization.GetValue(attrCfg.name);
                var atkArmy = m_Info.myArmy;
                var atkBuffVal = atkArmy.GetTotalBuffValueStr(attrId);
                var atkFactionInfo = atkArmy.maxFactionInfo;
                var defArmy = m_Info.oppArmy;
                var defBuffVal = defArmy.GetTotalBuffValueStr(attrId);
                var defFactionInfo = defArmy.maxFactionInfo;
                var param = new RptAttrParam()
                {
                    txtAtk = atkBuffVal,
                    txtDef = defBuffVal,
                    txtTitle = title,
                    atkFactionInfo = atkFactionInfo,
                    defFactionInfo = defFactionInfo,
                };
                var item = m_SubItems[i];
                var atkSourceBuffMap = atkArmy.GetSourceBuffMap(attrId);
                var defSourceBuffMap = defArmy.GetSourceBuffMap(attrId);


                var paramEx = new RptAttrParamEx()
                {
                    param = param,
                    atkSourceBuffMap = atkSourceBuffMap,
                    defSourceBuffMap = defSourceBuffMap,
                };

                item.SetData(paramEx);
            }
            var firstItem = m_SubItems[0];
            firstItem.SetExpand(true);
        }

        public override void OnClose()
        {
            base.OnClose();
            foreach (var item in m_SubItems)
            {
                Destroy(item);
            }
            m_SubItems.Clear();

        }
    }
}
