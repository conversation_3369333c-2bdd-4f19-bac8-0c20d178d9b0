using System;
using System.Collections.Generic;
using Core;
using Cysharp.Threading.Tasks;
using GameMain;
using Packet;
using SuperScrollView;
using UIFramework;
using UnityEngine;
using UnityEngine.UI;
using static GameUI.UIRptAttrDetailPanel;
using RptFightUnitInfo = GameMain.RptFightUnitInfo;

namespace GameUI
{
	public partial class UIRptFightDetailItem : UIItem
	{
		//初始化（具体参数各系统自行添加）
		private UIRptFightHeroHeadItem m_AtkHead;
		private UIRptFightHeroHeadItem m_DefHead;

		private List<UIRptFightSkillItem> m_AtkSkillItems;
		private List<UIRptFightSkillItem> m_DefSkillItems;
        private RectTransform rect_trans;

        private Action m_ExpandCb;

        private RptFightUnitInfo m_AtkInfo;
        private RptFightUnitInfo m_DefInfo;
        private bool m_IsExpand;
		public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
            var atkXform = rect_atk.Find("AtkFightHeroHead");
            if (atkXform != null)
            {
                m_AtkHead = await AddSubItem<UIRptFightHeroHeadItem>(atkXform.gameObject);
            }
            var defXform = rect_def.Find("DefFightHeroHead");
            if (defXform != null)
            {
                m_DefHead = await AddSubItem<UIRptFightHeroHeadItem>(defXform.gameObject);
            }


            m_AtkSkillItems = new List<UIRptFightSkillItem>(4);
            m_DefSkillItems = new List<UIRptFightSkillItem>(4);
            for (int i = 0; i < 4; i++)
            {
                var atkSkillRect = rect_atk_skill.Find($"rect_skill_{i + 1}");
                if (atkSkillRect != null)
                {
                    var skillItem = atkSkillRect.GetComponentInChildren<UIRptFightSkillItem>();
                    var item = await AddSubItem<UIRptFightSkillItem>(skillItem.gameObject);
                    m_AtkSkillItems.Add(item);
                }
                var defSkillRect = rect_def_skill.Find($"rect_skill_{i + 1}");
                if (defSkillRect != null)
                {
                    var skillItem = defSkillRect.GetComponentInChildren<UIRptFightSkillItem>();
                    var item = await AddSubItem<UIRptFightSkillItem>(skillItem.gameObject);
                    m_DefSkillItems.Add(item);
                }
            }

        }
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			btn_expand.AddOnClick(OnExpandClick);
            rect_trans = transform as RectTransform;

        }
        private void OnExpandClick()
        {
            m_IsExpand = !m_IsExpand;
            m_ExpandCb?.Invoke();
            RefreshView();
        }
        //刷新界面
        public override void RefreshView(params object[] data)
		{
			
		}

        public void SetExpand(bool isExpand)
        {
            if (isExpand != m_IsExpand)
            {
                m_IsExpand = !m_IsExpand;
                RefreshView();
            }
        }

        public void CheckLayoutRebuild()
        {
            var titleHeight = 210;
            var listHeight = 410;
            var totalHeight = listHeight + titleHeight;
            var nowHeight = m_IsExpand ? totalHeight : titleHeight;
            var curHeight = rect_trans.sizeDelta.y;
            if(curHeight != nowHeight)
            {
                rect_trans.sizeDelta = new Vector2(rect_trans.sizeDelta.x, nowHeight);
                LayoutRebuilder.MarkLayoutForRebuild(transform.parent as RectTransform);
            }
        }

        public void SetData(RptFightUnitInfo atkInfo, RptFightUnitInfo defInfo, Action expandCb)
        {
            m_AtkInfo = atkInfo;
            m_DefInfo = defInfo;
            m_ExpandCb = expandCb;
            RefreshView();
        }

        public void RefreshView()
		{
            if (m_IsExpand)
            {
                btn_expand.transform.SetEulerZ(0);
            }
            else
            {
                btn_expand.transform.SetEulerZ(180);
            }
            CheckLayoutRebuild();
            rect_detail.SetActiveEx(m_IsExpand);
            

            RefreshHeadItem(m_AtkHead, m_AtkInfo);
			RefreshHeadItem(m_DefHead, m_DefInfo);

			RefreshSkillItems(m_AtkInfo, m_AtkSkillItems, rect_atk_skill);
			RefreshSkillItems(m_DefInfo, m_DefSkillItems, rect_def_skill);

        }

		private void RefreshSkillItems(RptFightUnitInfo info, List<UIRptFightSkillItem> skillItems, RectTransform itemParent)
		{
			var isShowSkill = IsShowSkill(info);
			itemParent.SetActiveEx(isShowSkill);
			if(isShowSkill && skillItems != null)
			{
				var skillInfos = info.skillInfos;
				for (int i = 0; i < skillItems.Count; i++)
				{
					GameMain.RptFightSkillInfo skillInfo = null;
					var skillItem = skillItems[i];
					if(i < skillInfos.Count)
					{
                        skillInfo = skillInfos[i];
                    }
					skillItem.RefreshView(skillInfo, i);
                }
            }
		}

		private bool IsShowSkill(RptFightUnitInfo info)
		{
			return info != null && info.skillInfoExist;
		}

		private void RefreshHeadItem(UIRptFightHeroHeadItem item, RptFightUnitInfo info)
		{
            var isShowHead = IsShowHead(info);
            item.SetActiveEx(isShowHead);
            if (isShowHead && item != null)
            {
                item.RefreshView(info);
            }
        }

		private bool IsShowHead(RptFightUnitInfo info)
		{
			return info != null && info.hero != null;
		}

        public bool GetExpand()
        {
            return m_IsExpand;
        }
	}
}
