using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIRptSoldierCompareItem : UIItem
	{
		protected RectTransform rect_atk_head;
		protected RectTransform rect_def_head;
		protected TMPro.TextMeshProUGUI txt_atk_power;
		protected TMPro.TextMeshProUGUI txt_def_power;
		protected RectTransform rect_img_power;
		protected UIButton btn_detail;
		public override void InitComponent()
		{
			rect_atk_head = transform.Find("rect_atk_head").GetComponent<RectTransform>();
			rect_def_head = transform.Find("rect_def_head").GetComponent<RectTransform>();
			txt_atk_power = transform.Find("txt_atk_power").GetComponent<TMPro.TextMeshProUGUI>();
			txt_def_power = transform.Find("txt_def_power").GetComponent<TMPro.TextMeshProUGUI>();
			rect_img_power = transform.Find("rect_img_power").GetComponent<RectTransform>();
			btn_detail = transform.Find("btn_detail").GetComponent<UIButton>();
		}
	}
}
