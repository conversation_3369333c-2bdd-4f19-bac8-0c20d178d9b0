using UnityEngine;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIRptSoliderDetailPanel : UIPanel
	{
		protected RectTransform rect_root;
		protected RectTransform rect_bg;
		protected RectTransform rect_content;
		protected RectTransform rect_title;
		protected UIButton btn_hurt;
		protected UIButton btn_ill;
		protected UIButton btn_remain;
		protected UIButton btn_die;
		protected WScrollRect sv_list;
		public override void InitComponent()
		{
			rect_root = transform.Find("rect_root").GetComponent<RectTransform>();
			rect_bg = transform.Find("rect_root/rect_bg").GetComponent<RectTransform>();
			rect_content = transform.Find("rect_root/rect_content").GetComponent<RectTransform>();
			rect_title = transform.Find("rect_root/rect_content/rect_title").GetComponent<RectTransform>();
			btn_hurt = transform.Find("rect_root/rect_content/rect_title/btn_hurt").GetComponent<UIButton>();
			btn_ill = transform.Find("rect_root/rect_content/rect_title/btn_ill").GetComponent<UIButton>();
			btn_remain = transform.Find("rect_root/rect_content/rect_title/btn_remain").GetComponent<UIButton>();
			btn_die = transform.Find("rect_root/rect_content/rect_title/btn_die").GetComponent<UIButton>();
			sv_list = transform.Find("rect_root/rect_content/sv_list").GetComponent<WScrollRect>();
			base.InitComponent();
		}
		public override Transform GetAniRoot()
		{
			return rect_root;
		}
	}
}
