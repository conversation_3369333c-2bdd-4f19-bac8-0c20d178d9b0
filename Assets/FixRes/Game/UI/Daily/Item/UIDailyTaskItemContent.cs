using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIDailyTaskItem : WLoopItem
	{
		protected RectTransform rect_time;
		protected TMPro.TextMeshProUGUI txt_time;
		protected RectTransform rect_reward;
		protected Slider slider_reward;
		protected RectTransform rect_award;
		protected TMPro.TextMeshProUGUI txt_award;
		protected RectTransform rect_items;
		protected RectTransform rect_top_item;
		protected WImage img_top_item;
		protected WScrollRect sv_tasks;
		protected UIButton btn_get;
		public override void InitComponent()
		{
			rect_time = transform.Find("rect_time").GetComponent<RectTransform>();
			txt_time = transform.Find("rect_time/txt_time").GetComponent<TMPro.TextMeshProUGUI>();
			rect_reward = transform.Find("rect_reward").GetComponent<RectTransform>();
			slider_reward = transform.Find("rect_reward/slider_reward").GetComponent<Slider>();
			rect_award = transform.Find("rect_reward/rect_award").GetComponent<RectTransform>();
			txt_award = transform.Find("rect_reward/rect_award/img_award/txt_award").GetComponent<TMPro.TextMeshProUGUI>();
			rect_items = transform.Find("rect_reward/rect_award/rect_items").GetComponent<RectTransform>();
			rect_top_item = transform.Find("rect_reward/rect_award/rect_top_item").GetComponent<RectTransform>();
			img_top_item = transform.Find("rect_reward/rect_award/rect_top_item/img_top_item_bg/img_top_item").GetComponent<WImage>();
			sv_tasks = transform.Find("sv_tasks").GetComponent<WScrollRect>();
			btn_get = transform.Find("btn_get").GetComponent<UIButton>();
		}
	}
}
