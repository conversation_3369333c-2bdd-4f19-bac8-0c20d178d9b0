using System;
using System.Collections.Generic;
using Core;
using Cysharp.Threading.Tasks;
using GameMain;
using UIFramework;


namespace GameUI
{
    public partial class UILeagueTechnologyDetailPanel
    {
        private UnionTechnology _data;

        private UILeagueNodeUPDetailItem[] _detailItems;

        private UILeagueTechnologyDonateItem _donateItem;
        private UILeagueTechnologyStudyItem _studyItem;

        public override async UniTask OnLoadComplete()
        {
            _detailItems = await AddSubItems<UILeagueNodeUPDetailItem>(UIItemDefine.LeagueNodeUPDetailItem,
                rect_LeagueNodeUPDetailItem, 2);
            var data = UIMgr.Ins.GetPanelData(panelId);
            var unionid = (int)data[0];
            _data = Cfg.UnionTechnology.GetData(unionid);
            _donateItem =
                await AddSubItem<UILeagueTechnologyDonateItem>(UIItemDefine.LeagueTechnologyDonateItem, rect_Items,
                    unionid);
            _studyItem =
                await AddSubItem<UILeagueTechnologyStudyItem>(UIItemDefine.LeagueTechnologyStudyItem, rect_Items,
                    unionid);
            await base.OnLoadComplete();
        }

        public override void AfterInitComponent()
        {
            dis_btn_ctrl_recommend.btn_disable.onClick.AddListener(() => { SetNodeIsRecommend(true); });
            dis_btn_ctrl_recommend.btn_enable.onClick.AddListener(() => { SetNodeIsRecommend(false); });
            EventManager.Ins.RegisterEvent(UIEventType.RspLeagueUnionTechnologyIsRecommendInfo, this, RefreshRecommend);
            EventManager.Ins.RegisterEvent(UIEventType.RspLeagueUnionTechnologyAllUpdata, this, OnNextLevel);
            //注册按钮以及其它初始化
            base.AfterInitComponent();
            RefreshUI();
        }
        private void OnNextLevel()
        {
            if (_data==null)return;
            _data= LeagueMgr.Ins.GetNodeLevel(_data);
            RefreshUI();
        }
        private void RefreshUI()
        {
            var dataTemp = LeagueMgr.Ins.GetUnionNodeData(_data.id);
            var curExp = dataTemp?.Progress ?? 0;
            img_Icon.AsyncSetAtlasSprite("League", _data.icon).Forget();
            var maxLevel = Cfg.UnionTechnology.GetNodeMax(_data).level;
            txt_ItemName.SetText(Localization.GetValue(_data.name_id));
            txt_ItemDes.SetText(Localization.GetValue(_data.dec_id));

            var levelMax = Cfg.UnionTechnology.CheckUnionLevelIsMax(_data);
            RefreshRecommend();

            bool isExpMax = Cfg.UnionTechnology.CheckExpMax(_data.id, curExp);
            //是否解锁
            bool permission = LeagueMgr.Ins.CheckUnionNodeISActive(_data.id);
            bool isLevelExpMax = Cfg.UnionTechnology.CheckUnionLevelAndExpIsMax(_data) ;
            txt_processCount.text =isLevelExpMax? "Max": $"{_data.level}/{maxLevel}";
            bool changeCommon = LeagueMgr.Ins.CheckChangeCommon();
            // dis_btn_ctrl_recommend.btn_disable.SetEnabledEx(changeCommon);
            // dis_btn_ctrl_recommend.btn_enable.SetEnabledEx(changeCommon);
            _donateItem.SetActiveEx(false);
            _studyItem.SetActiveEx(false);
            rect_MaxTIps.SetActiveEx(false);
            if (levelMax)
            {
                _detailItems[0].SetMax(UILeagueNodeUPDetailItem.DetaType.LevelChange, _data.id);
                _detailItems[1].SetMax(UILeagueNodeUPDetailItem.DetaType.AdditionChange, _data.id);
            }
            else
            {
                var nextLevelData = Cfg.UnionTechnology.GetNextLevelData(_data);
                _detailItems[0].RefreshView(UILeagueNodeUPDetailItem.DetaType.LevelChange, _data.id, nextLevelData.id);
                _detailItems[1].RefreshView(UILeagueNodeUPDetailItem.DetaType.AdditionChange, _data.id,
                    nextLevelData.id);
            }

            if (isLevelExpMax)
            {
                rect_MaxTIps.SetActiveEx(true);
                return;
            }

            bool isCurrent = LeagueMgr.Ins.CheckISCurrent(_data.id);
            //是否可以研究
            bool isActive = LeagueMgr.Ins.CheckUnionNodeActive(_data.id);

            dis_btn_ctrl_recommend.SetActiveEx(!isCurrent && changeCommon && permission && !isExpMax);
            LoadGroup(!isCurrent|| !isActive).Forget();
            if (isCurrent)
            {
                return;
            }


            if (isActive)
            {
                if (permission && !isExpMax)
                {
                    Action action = () => { RefreshUI(); };
                    _donateItem.SetActiveEx(true);
                    _donateItem.RefreshView(_data.id, action);
                }
                else
                {
                    if (!changeCommon) return;
                    _studyItem.RefreshView(_data.id);
                    _studyItem.SetActiveEx(true);
                }
            }
        }
        
        async UniTask LoadGroup(bool isShow)
        {
            rect_CommonAdditionGroupItemRoot.SetActiveEx(isShow);
            var addList =LeagueMgr.Ins. GetTitleAndValueList(_data);
            txt_title_1.SetText(Localization.GetValue(LanguageCode.__CODE__1000631));
            var lists=await AddSubItems<UILeagueTechnologyAdditionItem>(UIItemDefine.LeagueTechnologyAdditionItem, rect_content_1,addList.Count);

            for (int i = 0; i < addList.Count; i++)
            {
                var data= addList[i];
                lists[i].RefreshView(data,i);
            }
        }
        
        void RefreshRecommend()
        {
            var isGray = !LeagueMgr.Ins.CheckRecommend(_data.id);
            rect_recommend.SetActiveEx(!isGray);
            dis_btn_ctrl_recommend.SetGray(isGray);

            var gainPercentage = Cfg.UnionTb.GetData(Cfg.UnionTb.RecommendTechBonus).value3 * 100;
            txt_BuffDes.SetText(Localization.ToFormat(LanguageCode.__CODE__1000576, gainPercentage));
        }

        void SetNodeIsRecommend(bool isRecommend)
        {
            LeagueMgr.Ins.ReqLeagueUnionTechnologyIsRecommendInfo(_data.id, isRecommend);
        }
    }
}