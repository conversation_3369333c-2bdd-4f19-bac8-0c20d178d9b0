using Core;
using Cysharp.Threading.Tasks;
using GameMain;
using SuperScrollView;
using System.Collections.Generic;
using UIFramework;
using UnityEngine;

namespace GameUI
{
	public partial class UILeagueSetPosPanel : UIPanel
	{
		int mPosId = 0;
		ulong mGuid = 0;
		WLoopGridView mGrid;
		List<ulong> mUidList;

        public override async UniTask OnLoadComplete()
		{
            //主动加载异步资源
            mUidList = LeagueMgr.Ins.GetMemberListByRank(4);
			if (mUidList != null)
				DLogger.Log("OnLoadComplete {0}", mUidList.Count);
            if (mUidList != null && mUidList.Count > 0)
			{
                mGrid = await CreateLoopGrid<UILeagueSetPosItem>(sv_member, mUidList.Count, RefreshItem);
            }

            await base.OnLoadComplete();
		}
		public override void AfterInitComponent()
		{
			//注册按钮以及其它初始化
			btn_confirm.AddOnClick(OnClickConfirm);
			
			base.AfterInitComponent();
		}

        public override void OnStart()
        {
            base.OnStart();
			var param = UIMgr.Ins.GetPanelData(panelId);
			if (param != null)
			{
				mPosId = (int)param[0];

            }
            bool showEmpty = mUidList == null || mUidList.Count == 0;
			rect_empty.gameObject.SetActiveEx(showEmpty);
			sv_member.gameObject.SetActiveEx(!showEmpty);

            var cfg = Cfg.Permit.GetData(mPosId);
			if (cfg == null) return;

			txt_pos.text = Localization.ToFormat(cfg.nameCN);
			txt_pos_desc.text = Localization.ToFormat(cfg.dsc);
			RefreshAsync(RefreshImg);
        }

		async UniTaskVoid RefreshImg()
		{
            var cfg = Cfg.Permit.GetData(mPosId);
            if (cfg == null) return;

			await img_pos.AsyncSetAtlasSprite("League", cfg.bg_image);
		}

		void OnClickConfirm()
		{
			if (mGuid == 0)
			{
				TipsMgr.Ins.ShowTips(LanguageCode.PleaseChooseMember);
				return;
			}

			LeagueMgr.Ins.ReqLeagueChgPos(mGuid, mPosId);
		}

		void RefreshItem(WLoopGridView grid, int index, WLoopItem loopItem)
		{
			if (index >= mUidList.Count) return;

			ulong guid = mUidList[index];
            UILeagueSetPosItem item = loopItem as UILeagueSetPosItem;
            item.RefreshView(guid, mPosId, mGuid);
			item.SetSelCallBack(OnSelect);
        }

		void OnSelect(ulong guid)
		{
			//DLogger.Warning("OnSelect {0}", guid);

			if (mGuid == guid)
			{
                mGuid = 0;
                mGrid.RefreshAllShownItem();
                return;
			}

			mGuid = guid;
            mGrid.RefreshAllShownItem();

        }
    }
}
