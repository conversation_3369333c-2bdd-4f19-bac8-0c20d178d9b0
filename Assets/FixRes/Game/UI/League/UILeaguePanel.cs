using Core;
using Cysharp.Threading.Tasks;
using Game;
using GameMain;
using Packet;
using SuperScrollView;
using System.Collections.Generic;
using UIFramework;
using UnityEngine;

namespace GameUI
{
	public partial class UILeaguePanel : UIPanel
	{
        Dictionary<int, int> mTabNameDic = new Dictionary<int, int>
        {
            {1, LanguageCode.Join},
            {2, LanguageCode.Create},
        };

        UITabGroup mTabGroup;
        int mCurIndex = 0;
        WLoopGridView mLeagueGrid;
        UILeagueFlagItem mLeagueFlagItem;

        int mFlagBgIndex = 0;
        int mFlagColorIndex = 0;
        int mFlagIconIndex = 0;
        int maxNameCnt = 12;
        int maxShortNameCnt = 6;
        int maxNoticeCnt = 100;
        int needCost = 1000;    
        bool bEnableNotice = false;
        string mNoticeStr = string.Empty;

        List <LeagueListInfo> mLeagueList = new List<LeagueListInfo>();

        public override async UniTask OnLoadComplete()
		{
            //主动加载异步资源

            mTabGroup = new UITabGroup();
            await mTabGroup.Create(UITabGroup.TabType.ComUpTab, rect_tabs, mTabNameDic.Count, OnRefreshTab, this, null);
            maxNoticeCnt = Cfg.Const.GetVal1(122);
            await base.OnLoadComplete();
            RandomFlagParams();

            mLeagueFlagItem = await AddSubItem<UILeagueFlagItem>(UIItemDefine.LeagueFlagItem, rect_flag);

            mLeagueGrid = await CreateLoopGrid<UILeagueListItem>(sv_league_list, 0, RefreshListItem);
            OnSelTab(1);

            await RefreshFlag();
        }
		public override void AfterInitComponent()
		{
            //注册按钮以及其它初始化
            btn_search.AddOnClick(OnClickSearch);
            btn_join_league.AddOnClick(OnClickJoin);
            btn_rank.AddOnClick(OnClickRank);
            btn_create.AddOnClick(OnClickCreate);
            btn_create_disable.AddOnClick(OnClickCreate);
            btn_chg.AddOnClick(OnClickChg);
            btn_notice.AddOnClick(OnClickNotice);
            btn_notice_sure.AddOnClick(OnClickNoticeSure);
            btn_notice_cancle.AddOnClick(OnClickNoticeCancle);

            input_league_name.onValueChanged.AddListener(OnLeagueNameChg);
            input_league_short_name.onValueChanged.AddListener(OnLeagueShortNameChg);
            input_league_notice.onValueChanged.AddListener(OnLeagueNoticeChg);

            EventManager.Ins.RegisterEvent(UIEventType.OnRetLeagueList, this, RefreshLeagueList);
            EventManager.Ins.RegisterEvent(UIEventType.OnChgFlag, this, RefreshFlagAsync);
            EventManager.Ins.RegisterEvent(UIEventType.OnItemAdd, this, RefreshCost);
            EventManager.Ins.RegisterEvent(UIEventType.OnLeagueApplied, this, RefreshListItems);

            base.AfterInitComponent();
		}

        void OnRefreshTab(int index, WLoopItem loopItem)
        {
            int id = index + 1;

            UIComUpTabItem item = loopItem as UIComUpTabItem;

            item.SetIndex(id);
            item.SetCallBack(OnSelTab);
            item.SetState(mCurIndex == id);
            if (mTabNameDic.ContainsKey(id))
                item.SetTextById(mTabNameDic[id]);
        }

        void OnSelTab(int index)
        {
            if (mCurIndex == index)
                return;

            mCurIndex = index;
            mTabGroup.RefreshAll(index);
            rect_list.gameObject.SetActiveEx(mCurIndex == 1);
            rect_create.gameObject.SetActiveEx(mCurIndex == 2);

            RefreshView();
        }

        public override void OnStart()
        {
            needCost = Cfg.Const.GetVal1(84);
            RefreshNoticeBtns();
            RefreshCost();
            base.OnStart();
        }

        public override void OnClose()
        {
            base.OnClose();

            input_league_name.onValueChanged.RemoveAllListeners();
            input_league_short_name.onValueChanged.RemoveAllListeners();
            input_league_notice.onValueChanged.RemoveAllListeners();

            mTabGroup.Destroy();
            LeagueMgr.Ins.ResetReqMark();
        }

        void RefreshView()
        {
            if (mCurIndex == 1)
            {
                LeagueMgr.Ins.ReqLeagueList(false, RefreshLeagueList);
            }
        }

        void RefreshCost()
        {
            int itemCnt = BagMgr.Ins.GetItemCount(GameDefine.SpecItemId.Gold);
            txt_cost.text = needCost.ToString();
            txt_cost_disable.text = UIHelper.GetNumberStringWithColor(needCost, itemCnt);
            bool isShowNormal = !input_league_name.text.IsNullOrEmptyEx();
            isShowNormal &= !input_league_short_name.text.IsNullOrEmptyEx();
            isShowNormal &= !input_league_notice.text.IsNullOrEmptyEx();
            isShowNormal &= itemCnt >= needCost;

            btn_create.gameObject.SetActiveEx(isShowNormal);
            btn_create_disable.gameObject.SetActiveEx(!isShowNormal);
        }
        void RefreshLeagueList()
        {
            var list = LeagueMgr.Ins.GetLeagueList();
            if (list == null || list.Count == 0)
            {

                return;
            }

            mLeagueList = list;
            mLeagueGrid.SetListItemCount(list.Count);
            mLeagueGrid.RefreshAllShownItem();
        }

        void RefreshListItems()
        {
            mLeagueGrid.RefreshAllShownItem();
        }

        void RefreshListItem(WLoopGridView grid, int index, WLoopItem loopItem)
        {
            if (index >= mLeagueList.Count) return;

            var info = mLeagueList[index];
            UILeagueListItem item = loopItem as UILeagueListItem;
            item.RefreshView(info);
        }

        void OnClickSearch()
        {
            string searchName = input_league_search_name.text;
            if (string.IsNullOrEmpty(searchName))
            {
                //TipsMgr.Ins.ShowTips(LanguageCode.PleaseInputLeagueName);
                LeagueMgr.Ins.ReqLeagueList(true);
                return;
            }
            LeagueMgr.Ins.ReqSearchLeague(searchName);
        }

        void OnClickJoin()
        {
            LeagueMgr.Ins.ReqAutoJoinLeague();
        }

        void OnClickRank()
        {
            UIMgr.Ins.Open(UIDefine.LeagueRankPanel);
        }

        void OnClickChg()
        {
            UIMgr.Ins.Open(UIDefine.LeagueFlagPanel, mFlagBgIndex, mFlagColorIndex, mFlagIconIndex);
        }

        void OnClickCreate()
        {
            string name = input_league_name.text;
            if (string.IsNullOrEmpty(name))
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000293);
                return;
            }

            string shortName = input_league_short_name.text;
            if (string.IsNullOrEmpty(shortName))
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000294);
                return;
            }

            string notice = mNoticeStr;
            if (string.IsNullOrEmpty(notice))
            {
                TipsMgr.Ins.ShowTips(LanguageCode.__CODE__1000295);
                return;
            }

            if (!BagMgr.Ins.IsEnough(GameDefine.SpecItemId.Gold, needCost))
            {
                TipsMgr.Ins.ShowTips(LanguageCode.ItemNotEnough);
                return;
            }
            LeagueMgr.Ins.ReqCreateLeague(name, shortName, notice, mFlagBgIndex, mFlagColorIndex, mFlagIconIndex);
        }

        void RandomFlagParams()
        {
            //旗帜颜色
            var cfg = Cfg.UnionTb.GetData(1);
            if (cfg == null) return;
            mFlagColorIndex = Random.Range(0, cfg.value1.Length);

            //旗帜底图
            cfg = Cfg.UnionTb.GetData(2);
            if (cfg == null) return;
            mFlagBgIndex = Random.Range(0, cfg.value1.Length);

            //旗帜图标
            cfg = Cfg.UnionTb.GetData(3);
            if (cfg == null) return;
            mFlagIconIndex = Random.Range(0, cfg.value1.Length);
        }

        void RefreshFlagAsync(object flagBgId, object flagBgColorId, object flagIconId)
        {
            mFlagBgIndex = (int)flagBgId;
            mFlagColorIndex = (int)flagBgColorId;
            mFlagIconIndex = (int)flagIconId;

            UniTask.Create(RefreshFlag);
        }

        async UniTask RefreshFlag()
        {
            await mLeagueFlagItem.RefreshFlag(mFlagBgIndex, mFlagColorIndex, mFlagIconIndex);
        }

        void OnLeagueNameChg(string str)
        {
            input_league_name.SetTextWithoutNotify(StringUtil.GetGBLimitStr(str, maxNameCnt));
            RefreshCost();
        }

        void OnLeagueShortNameChg(string str)
        {
            input_league_short_name.SetTextWithoutNotify(StringUtil.GetGBLimitStr(str, maxShortNameCnt));
            RefreshCost();
        }

        void OnLeagueNoticeChg(string str)
        {
            input_league_notice.SetTextWithoutNotify(StringUtil.GetGBLimitStr(str, maxNoticeCnt));
            RefreshCost();
        }

        void RefreshNoticeBtns()
        {
            btn_notice.SetActiveEx(!bEnableNotice);
            btn_notice_cancle.SetActiveEx(bEnableNotice);
            btn_notice_sure.SetActiveEx(bEnableNotice);

            input_league_notice.interactable = bEnableNotice;
        }

        void OnClickNotice()
        {
            bEnableNotice = true;
            RefreshNoticeBtns();
        }

        void OnClickNoticeSure()
        {
            bEnableNotice = false;
            RefreshNoticeBtns();

            mNoticeStr = input_league_notice.text;
        }

        void OnClickNoticeCancle()
        {
            bEnableNotice = false;
            RefreshNoticeBtns();

            input_league_notice.SetTextWithoutNotify(mNoticeStr);
        }
    }
}
