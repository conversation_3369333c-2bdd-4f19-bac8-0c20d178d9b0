using System.Collections.Generic;
using Core;
using Cysharp.Threading.Tasks;
using UIFramework;
using SuperScrollView;

namespace GameUI
{
	public partial class UILeagueTerritoryPanel 
	{
		private UITabGroup _mTabGroup;
		private int _mCurIndex;
		private UIBase[] _uiBaseArray;
		Dictionary<int, int> mTabNameDic = new Dictionary<int, int>
		{
			{1, LanguageCode.__CODE__1000562},
			{2, LanguageCode.__CODE__1000563},
			{3, LanguageCode.__CODE__1000564},
		};
		public override async UniTask OnLoadComplete()
		{
			//主动加载异步资源
			_mTabGroup = new UITabGroup();
			_uiBaseArray = new UIBase[3];
			await _mTabGroup.Create(UITabGroup.TabType.ComUpTab, rect_tabs, mTabNameDic.Count, OnRefreshTab, this);
			await base.OnLoadComplete();
		}

		public override void AfterInitComponent()
		{
			//注册按钮以及其它初始化
			base.AfterInitComponent();
		}

		private void OnRefreshTab(int index, WLoopItem loopItem)
		{
			UIComUpTabItem item = loopItem as UIComUpTabItem;
			if (item)
			{
				int id = index + 1;
				item.SetIndex(index);
				item.SetCallBack(OnSelTab);
				item.SetState(_mCurIndex == index);
				if( _uiBaseArray[index] ) _uiBaseArray[index].SetActiveEx(_mCurIndex == index);
				item.SetTextById(mTabNameDic[id]);
			}
		}

		private void OnSelTab(int index)
		{
			if (_mCurIndex == index)
				return;
			_mCurIndex = index;
			_mTabGroup.RefreshAll(index);
			RefreshTabAsync().Forget();
		}

		private async UniTask RefreshTabAsync()
		{
			LeagueResourceType type = (LeagueResourceType)_mCurIndex;
			switch (type)
			{
				case LeagueResourceType.Resource:
					if (!_uiBaseArray[_mCurIndex])
						_uiBaseArray[_mCurIndex] =
							await AddSubItem<UILeagueResourceItem>(UIItemDefine.LeagueResourceItem, rect_sub_content);
					break;
			}
		}
	}

	public enum LeagueResourceType
	{
		Resource = 2,
	}
}
