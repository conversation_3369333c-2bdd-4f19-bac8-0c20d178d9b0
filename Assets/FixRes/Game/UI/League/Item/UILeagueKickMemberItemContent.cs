using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UILeagueKickMemberItem : WLoopItem
	{
		protected UIToggle check_box_sel;
		protected TMPro.TextMeshProUGUI txt_toggle;
		public override void InitComponent()
		{
			check_box_sel = transform.Find("check_box_sel").GetComponent<UIToggle>();
			txt_toggle = transform.Find("check_box_sel/txt_toggle").GetComponent<TMPro.TextMeshProUGUI>();
		}
	}
}
