using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UILeagueChgPosItem : WLoopItem
	{
		protected TMPro.TextMeshProUGUI txt_rank;
		protected UIToggle check_box_sel;
		protected TMPro.TextMeshProUGUI txt_name;
		protected RectTransform rect_NoName;
		public override void InitComponent()
		{
			txt_rank = transform.Find("img_bg/img_rank_bg/txt_rank").GetComponent<TMPro.TextMeshProUGUI>();
			check_box_sel = transform.Find("img_bg/check_box_sel").GetComponent<UIToggle>();
			txt_name = transform.Find("img_bg/txt_name").GetComponent<TMPro.TextMeshProUGUI>();
			rect_NoName = transform.Find("img_bg/rect_NoName").GetComponent<RectTransform>();
		}
	}
}
