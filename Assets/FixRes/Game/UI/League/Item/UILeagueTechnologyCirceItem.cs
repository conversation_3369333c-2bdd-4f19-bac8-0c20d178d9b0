using System;
using Core;
using Cysharp.Threading.Tasks;
using GameMain;
using UnityEngine;


namespace GameUI
{
	public partial class UILeagueTechnologyCirceItem 
	{
		
		/// <summary>
		/// 显示的item
		/// </summary>
		private int _itemId;
		/// <summary>
		/// 暴击倍数
		/// </summary>
		private int _circe;

		/// <summary>
		/// 生命周期
		/// </summary>
		private float _lifeTime=1f;
		
		
		private Timer _timer;
		[SerializeField]
		private WImage _icon;
		[SerializeField]
		private TMPro.TextMeshProUGUI _circetext;
		//初始化（具体参数各系统自行添加）
		public override void Init(params object[] param)
		{
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
			
		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			
			
		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{
			_itemId = (int)data[0];
			_circe = (int)data[1];
			var callback= (Action) data[2];
			if (data.Length > 3)
			{
				_lifeTime= (float)data[2];
			}
			
			var cfg= Cfg.Item.GetData(_itemId);
			_circetext.SetActiveEx(_circe>0);
			_circetext.SetText($"x{_circe}");
			_timer?.Cancel();
			_timer =Timer.Register(_lifeTime, () => { callback?.Invoke(); });
			_icon.AsyncSetAtlasSprite("Icon",cfg.icon).Forget();
		}
	}
}
