using Core;
using Cysharp.Threading.Tasks;
using UnityEngine;


namespace GameUI
{
    public partial class UILeagueTechnologyAdditionItem
    {
        public Color noraml;
        public Color noNormal;

        /// <summary>
        /// _type  0   联盟科技Id
        /// 1 建筑id
        /// 2 itemid 
        /// </summary>
        private int _type;

        private int _index;
        private bool _isPass;
        private int _iconId;

        //刷新界面
        public override void RefreshView(params object[] data)
        {
            var datas = data[0] as (int, int, bool)?;
            if (datas == null) return;
            _type = datas.Value.Item1;
            _index = (int)data[1];
            _isPass = datas.Value.Item3;
            _iconId = datas.Value.Item2;

            go_bg.SetActive(_index % 2 == 1);
            go_Component.SetActiveEx(_isPass);
            go_Fail.SetActive(!_isPass);
            RefreshAsync(RefreshUI);
        }

        private async UniTaskVoid RefreshUI()
        {
            rect_League.SetActiveEx(false);
            rect_Normal.SetActiveEx(false);
            switch (_type)
            {
                case 0:
                    rect_League.SetActiveEx(true);
                    var unionTechnology = Cfg.UnionTechnology.GetData(_iconId);
                    await img_Icon.AsyncSetAtlasSprite("League", unionTechnology.icon);
                    break;
                case 1:
                    rect_Normal.SetActiveEx(true);
                    var buildingUnitInfo = Cfg.BuildingUnit.GetData(_iconId);
                    await img_NormalIcon.AsyncSetAtlasSprite("BuildingIcon", buildingUnitInfo.icon);
                    break;
                case 2:
                    rect_Normal.SetActiveEx(true);
                    var cfg = Cfg.Item.GetData(_iconId);
                    await img_NormalIcon.AsyncSetAtlasSprite("Icon", cfg.icon);
                    break;
            }
        }
    }
}