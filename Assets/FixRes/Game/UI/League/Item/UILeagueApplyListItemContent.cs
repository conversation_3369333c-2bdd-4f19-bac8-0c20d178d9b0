using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UILeagueApplyListItem : WLoopItem
	{
		protected RectTransform rect_head;
		protected TMPro.TextMeshProUGUI txt_player_name;
		protected WImage img_gender;
		protected TMPro.TextMeshProUGUI txt_ship_lv;
		protected TMPro.TextMeshProUGUI txt_power;
		protected TMPro.TextMeshProUGUI txt_kill_cnt;
		protected UIButton btn_refuse;
		protected UIButton btn_confirm;
		public override void InitComponent()
		{
			rect_head = transform.Find("rect_head").GetComponent<RectTransform>();
			txt_player_name = transform.Find("txt_player_name").GetComponent<TMPro.TextMeshProUGUI>();
			img_gender = transform.Find("txt_player_name/img_gender").GetComponent<WImage>();
			txt_ship_lv = transform.Find("txt_ship_lv").GetComponent<TMPro.TextMeshProUGUI>();
			txt_power = transform.Find("txt_power").GetComponent<TMPro.TextMeshProUGUI>();
			txt_kill_cnt = transform.Find("txt_kill_cnt").GetComponent<TMPro.TextMeshProUGUI>();
			btn_refuse = transform.Find("btn_refuse").GetComponent<UIButton>();
			btn_confirm = transform.Find("btn_confirm").GetComponent<UIButton>();
		}
	}
}
