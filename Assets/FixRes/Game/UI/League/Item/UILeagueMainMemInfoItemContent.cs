using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UILeagueMainMemInfoItem : UIItem
	{
		protected RectTransform rect_mem_cnt;
		protected TMPro.TextMeshProUGUI txt_mem_cnt;
		protected RectTransform rect_leader;
		protected UIButton btn_leader;
		protected RectTransform rect_leader_power;
		protected TMPro.TextMeshProUGUI txt_leader_power;
		protected RectTransform rect_head;
		protected TMPro.TextMeshProUGUI txt_leader_login_time;
		protected TMPro.TextMeshProUGUI txt_leader_name;
		protected TMPro.TextMeshProUGUI txt_leader_ship_lv;
		protected RectTransform rect_middle;
		protected RectTransform rect_mid_members;
		protected WScrollRect sv_sub_mem;
		public override void InitComponent()
		{
			rect_mem_cnt = transform.Find("rect_mem_cnt").GetComponent<RectTransform>();
			txt_mem_cnt = transform.Find("rect_mem_cnt/img_bg/txt_mem_cnt").GetComponent<TMPro.TextMeshProUGUI>();
			rect_leader = transform.Find("rect_leader").GetComponent<RectTransform>();
			btn_leader = transform.Find("rect_leader/btn_leader").GetComponent<UIButton>();
			rect_leader_power = transform.Find("rect_leader/rect_leader_power").GetComponent<RectTransform>();
			txt_leader_power = transform.Find("rect_leader/rect_leader_power/txt_leader_power").GetComponent<TMPro.TextMeshProUGUI>();
			rect_head = transform.Find("rect_leader/rect_head").GetComponent<RectTransform>();
			txt_leader_login_time = transform.Find("rect_leader/txt_leader_login_time").GetComponent<TMPro.TextMeshProUGUI>();
			txt_leader_name = transform.Find("rect_leader/img_leader_name_bg/txt_leader_name").GetComponent<TMPro.TextMeshProUGUI>();
			txt_leader_ship_lv = transform.Find("rect_leader/img_leader_name_bg/txt_leader_ship_lv").GetComponent<TMPro.TextMeshProUGUI>();
			rect_middle = transform.Find("rect_middle").GetComponent<RectTransform>();
			rect_mid_members = transform.Find("rect_middle/rect_mid_members").GetComponent<RectTransform>();
			sv_sub_mem = transform.Find("sv_sub_mem").GetComponent<WScrollRect>();
		}
	}
}
