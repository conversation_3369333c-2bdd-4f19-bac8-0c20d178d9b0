using Core;
using Cysharp.Threading.Tasks;
using GameMain;

namespace GameUI
{
	public partial class UILeagueNodeUPDetailItem
	{

		private WImage _bg;
		public enum DetaType
		{
			/// <summary>
			/// 升级 等级改变
			/// </summary>
			LevelChange,
			/// <summary>
			/// 数值改变
			/// </summary>
			AdditionChange,
		}
		
		//初始化（具体参数各系统自行添加）
		public override void Init(params object[] param)
		{
			
		}
		//主动加载异步资源
		public override async UniTask OnLoadComplete()
		{
			
			
		}
		//注册按钮以及其它初始化
		public override void AfterInitComponent()
		{
			_bg = txt_title.transform.parent.GetComponent<WImage>();
		}
		//刷新界面
		public override void RefreshView(params object[] data)
		{
			DetaType  type= (DetaType)data[0];
			var curDataID= (int)data[1]; 
			var nextDataID= (int)data[2];
			string titleId =string.Empty;
			string curDataName=string.Empty;
			string nextDataName=string.Empty;
			var curCfg = Cfg.UnionTechnology.GetData(curDataID);
			var nextCfg = Cfg.UnionTechnology.GetData(nextDataID);
			switch (type)
			{
				case DetaType.LevelChange:
					titleId = "等级";
					curDataName=curCfg.level.ToString();
					nextDataName= nextCfg.level.ToString();
					_bg.SetEnabledEx(true);
					break;
				case DetaType.AdditionChange:
					var curBuff = Cfg.BuffEffect.GetData(curCfg.buff_id);
					titleId =Localization.GetValue(curBuff.effectName);
					var nextBuff = Cfg.BuffEffect.GetData(nextCfg.buff_id);
					curDataName=Cfg.BuffEffect.GetBuffEffectValueStr(curBuff.id);
					nextDataName= Cfg.BuffEffect.GetBuffEffectValueStr(nextBuff.id);
					_bg.SetEnabledEx(false);
					break;
			}
			txt_title.SetText(titleId);
			txt_curLevel.SetText(curDataName);
			txt_NextLevel.SetText(nextDataName);
		}

		public void SetMax(params object[] data)
		{
			DetaType  type= (DetaType)data[0];
			var curDataID= (int)data[1]; 
			string titleId =string.Empty;
			string curDataName=string.Empty;
			string nextDataName=string.Empty;
			var curCfg = Cfg.UnionTechnology.GetData(curDataID);
			switch (type)
			{
				case DetaType.LevelChange:
					titleId = "等级";
					curDataName=curCfg.level.ToString();
					break;
				case DetaType.AdditionChange:
					var curBuff = Cfg.BuffEffect.GetData(curCfg.buff_id);
					titleId =Localization.GetValue(curBuff.effectName);
					curDataName=Cfg.BuffEffect.GetBuffEffectValueStr(curBuff.id);
					break;
			}
			txt_title.SetText(titleId);
			txt_curLevel.SetText(curDataName);
			txt_NextLevel.SetActiveEx(false);
			rect_arrow.SetActiveEx(false);
		}
	}
}
