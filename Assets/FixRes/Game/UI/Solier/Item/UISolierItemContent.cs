using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UISolierItem : WLoopItem
	{
		protected WImage img_qualitybg;
		protected WImage img_soldier_head_icon;
		protected TMPro.TextMeshProUGUI txt_level;
		public override void InitComponent()
		{
			img_qualitybg = transform.Find("img_qualitybg").GetComponent<WImage>();
			img_soldier_head_icon = transform.Find("img_soldier_head_icon").GetComponent<WImage>();
			txt_level = transform.Find("txt_level").GetComponent<TMPro.TextMeshProUGUI>();
		}
	}
}
