using Cysharp.Threading.Tasks;
using SuperScrollView;
using UIFramework;
using UnityEngine;
using GameMain;
using Core;

namespace GameUI
{
    public partial class UIGuidePointItem : UIItem
    {
        public FollowMode FollowMode { get; set; } = FollowMode.UI;

        GameObject mTargetObj;

        Vector2 mOffset;

        //初始化（具体参数各系统自行添加）
        public override void Init(params object[] param)
        {

        }
        //主动加载异步资源
        public override async UniTask OnLoadComplete()
        {


        }
        //注册按钮以及其它初始化
        public override void AfterInitComponent()
        {
            /*var ani = gameObject.GetComponent<Animation>();
			ani.Play();*/

        }
        //刷新界面
        public override void RefreshView(params object[] data)
        {

        }

        public void PointToObj(GameObject go, Vector2 offset = default)
        {
            mTargetObj = go;

            mOffset = offset;
        }

        private void Update()
        {
            if (mTargetObj == null) return;

            Camera targetCamera = null;
            Camera uiCamera = UIMgr.Ins.GetUICamera();
            Canvas canvas = UIMgr.Ins.GetMiddleCanvas();
            RectTransform rectTransform = null;

            if (FollowMode == FollowMode.UI)
            {
                targetCamera = UIMgr.Ins.GetUICamera();
                rectTransform = transform as RectTransform;
            }
            else if (FollowMode == FollowMode.Transform)
            {
                targetCamera = Camera.main;
                rectTransform = canvas.transform as RectTransform;
            }

            Vector2 screenPosition = RectTransformUtility.WorldToScreenPoint(targetCamera, mTargetObj.transform.position);

            Vector2 localPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                rectTransform,
                screenPosition,
                canvas.worldCamera,
                out localPoint);

            rect_finger.anchoredPosition = localPoint + mOffset;
        }

        private void LateUpdate()
        {
            transform.SetAsLastSibling();
        }

        public void SetZeroPos()
        {
            mTargetObj = null;

            rect_finger.anchoredPosition = Vector2.zero;
        }

        public void ShowDesc(int id)
        {
            rect_desc_bg.gameObject.SetActiveEx(id > 0);

            if (id == 0) return;
            txt_desc.text = Localization.GetValue(id);
        }
    }
}
