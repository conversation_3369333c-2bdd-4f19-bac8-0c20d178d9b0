using UnityEngine;
using SuperScrollView;
using UIFramework;
using UnityEngine.UI;

namespace GameUI
{
	public partial class UIGuidePointItem : UIItem
	{
		protected RectTransform rect_finger;
		protected RectTransform rect_desc_bg;
		protected RectTransform rect_arrow;
		protected TMPro.TextMeshProUGUI txt_desc;
		public override void InitComponent()
		{
			rect_finger = transform.Find("rect_finger").GetComponent<RectTransform>();
			rect_desc_bg = transform.Find("rect_finger/rect_desc_bg").GetComponent<RectTransform>();
			rect_arrow = transform.Find("rect_finger/rect_desc_bg/rect_arrow").GetComponent<RectTransform>();
			txt_desc = transform.Find("rect_finger/rect_desc_bg/txt_desc").GetComponent<TMPro.TextMeshProUGUI>();
		}
	}
}
