using System.Collections;
using System.Collections.Generic;
using Core;
using Game;
using UIFramework;
using UnityEngine;
using UnityEngine.Pool;
using WorldMapUnitType = GameDefine.WorldMapUnitType;

namespace GameMain
{
    public class WorldMapBattleController
    {
        private Dictionary<ulong, WorldMapBattlePlayer> _battlePlayers;
        bool isInited = false;

        public void Init()
        {
            _battlePlayers = new();
            isInited = true;
        }

        public void UnInit()
        {
            foreach (var kv in _battlePlayers)
            {
                kv.Value.Clear();
            }

            _battlePlayers.Clear();
            _battlePlayers = null;
            isInited = false;
        }

        public void Update()
        {
            if (!isInited) return;

            var battleInfos = WorldMgr.Ins.GetBattleInfos();
            if (battleInfos == null)
            {
                return;
            }

            foreach (var kv in battleInfos)
            {
                // 检查是否需要创建战斗
                if (CheckNeedCreateBattlePlayer(kv.Value))
                {
                    Vector2Int atkPos = kv.Value.atkInfo.pos;
                    Vector2Int defPos = kv.Value.defInfo.pos;
                    WorldMapBehaviour worldMapBehaviour = WorldMgr.Ins.WorldMapBehaviour;
                    using (HashSetPool<int>.Get(out var showGrid))
                    {
                        //如果进攻方或者防守方的格子没有创建，则需要创建
                        if (!worldMapBehaviour.ShowRangeContainsPos(atkPos))
                        {
                            showGrid.Add(WorldMapUtils.GetGridIndex(atkPos));
                        }

                        if (!worldMapBehaviour.ShowRangeContainsPos(defPos))
                        {
                            showGrid.Add(WorldMapUtils.GetGridIndex(defPos));
                        }

                        worldMapBehaviour.UpdateVisibleGrids(showGrid);
                    }

                    var battlePlayer = new WorldMapBattlePlayer();
                    WorldMapUnitType unitType = GetUnitType(kv.Value.fightType);
                    var atkTroop = worldMapBehaviour.GetWorldMapUnit(unitType, kv.Value.BattleId);
                    var defTiles = worldMapBehaviour.GetWorldMapTiles(defPos);
                    if (atkTroop && defTiles)
                    {
                        battlePlayer.StartBattle(atkTroop, defTiles, kv.Value);
                        _battlePlayers.Add(kv.Key, battlePlayer);

                        DLogger.Log($"世界战斗开始 进攻方{kv.Value.atkInfo.guid} 进攻方位置{kv.Value.atkInfo.pos} 防守方{kv.Value.defInfo.guid} 防守方位置{kv.Value.defInfo.pos} 开始时间{TimeUtils.ConvertToDateTimeString((long)kv.Value.startTime * 1000)} 结束时间{TimeUtils.ConvertToDateTimeString((long)kv.Value.endTime * 1000)}");
                        EventManager.Ins.DispatchEvent(UIEventType.WorldStartShowBattle, kv.Value);
                    }
                }
            }

            long curTime = (long)TimeUtils.GetServerTimeMs();
            string timeStr = TimeUtils.ConvertToDateTimeString(curTime);
            //先更新数据层
            foreach (var kv in battleInfos)
            {
                var battleInfo = kv.Value;
                var atkInfo = battleInfo.atkInfo;
                var defInfo = battleInfo.defInfo;

                if (atkInfo.normalShowDamageInfo != null)
                {
                    // 更新进攻方对防守方造成的 普通 伤害
                    foreach (var damageInfo in atkInfo.normalShowDamageInfo)
                    {
                        long showTimeMs = damageInfo.showTime;
                        if (curTime >= showTimeMs && !damageInfo.calculated)
                        {
                            DLogger.Log($"{timeStr} 进攻方{atkInfo.guid} 对 防守方{defInfo.pos} 造成了普通伤害{damageInfo.damageNum}");
                            damageInfo.calculated = true;
                            damageInfo.takeDamageTargetInfo.curHp -= damageInfo.damageNum;
                        }
                    }
                }

                if (atkInfo.skillShowDamageInfo != null)
                {
                    // 更新进攻方对防守方造成的 技能 伤害
                    foreach (var damageInfo in atkInfo.skillShowDamageInfo)
                    {
                        long showTimeMs = damageInfo.showTime;
                        if (curTime >= showTimeMs && !damageInfo.calculated)
                        {
                            DLogger.Log($"{timeStr} 进攻方{atkInfo.guid} 对 防守方{defInfo.pos} 造成了技能伤害{damageInfo.damageNum}");
                            damageInfo.calculated = true;
                            damageInfo.takeDamageTargetInfo.curHp -= damageInfo.damageNum;
                        }
                    }
                }

                if (defInfo.normalShowDamageInfo != null)
                {
                    // 更新防守方对进攻方造成的 普通 伤害
                    foreach (var damageInfo in defInfo.normalShowDamageInfo)
                    {
                        long showTimeMs = damageInfo.showTime;
                        if (curTime >= showTimeMs && !damageInfo.calculated)
                        {
                            DLogger.Log($"{timeStr} 防守方{defInfo.pos} 对 进攻方{atkInfo.guid} 造成了普通伤害{damageInfo.damageNum}");

                            damageInfo.calculated = true;
                            damageInfo.takeDamageTargetInfo.curHp -= damageInfo.damageNum;
                        }
                    }
                }

                if (defInfo.skillShowDamageInfo != null)
                {
                    // 更新防守方对进攻方造成的 技能 伤害
                    foreach (var damageInfo in defInfo.skillShowDamageInfo)
                    {
                        long showTimeMs = damageInfo.showTime;
                        if (curTime >= showTimeMs && !damageInfo.calculated)
                        {
                            DLogger.Log($"{timeStr} 防守方{defInfo.pos} 对 进攻方{atkInfo.guid} 造成了技能伤害{damageInfo.damageNum}");
                            damageInfo.calculated = true;
                            damageInfo.takeDamageTargetInfo.curHp -= damageInfo.damageNum;
                        }
                    }
                }
            }

            foreach (var kv in _battlePlayers)
            {
                kv.Value.Update();
            }

            using (ListPool<ulong>.Get(out var list))
            {
                WorldMgr.Ins.GetRemoveBattleIds(list, curTime);

                foreach (var removeId in list)
                {
                    if (_battlePlayers.TryGetValue(removeId, out var battlePlayer))
                    {
                        DLogger.Log($"{timeStr} {removeId} 战斗结束");
                        battlePlayer.OnBattleEnd();

                        battlePlayer.Clear();
                        _battlePlayers.Remove(removeId);
                    }
                }
            }
        }

        WorldMapUnitType GetUnitType(int fightType)
        {
            switch (fightType)
            {
                case WorldMapUtils.FightType.TeamToMonster:
                case WorldMapUtils.FightType.TeamToPlayer:
                    return GameDefine.WorldMapUnitType.Team;
                default:
                    return GameDefine.WorldMapUnitType.Troop;
            }
        }

        private bool CheckNeedCreateBattlePlayer(TroopBattleInfo battleInfo)
        {
            int curTime = TimeUtils.GetServerTimeInt();
            //已经创建了
            if (_battlePlayers.ContainsKey(battleInfo.BattleId))
            {
                return false;
            }

            //已经结束了
            if (curTime >= battleInfo.endTime)
            {
                return false;
            }

            Vector2Int atkPos = battleInfo.atkInfo.pos;
            Vector2Int defPos = battleInfo.defInfo.pos;
            int atkIndex = WorldMapUtils.GetGridIndex(atkPos);
            int defIndex = WorldMapUtils.GetGridIndex(defPos);
            //没有找到格子信息
            if (WorldMgr.Ins.GetGridData(atkIndex) == null || WorldMgr.Ins.GetGridData(defIndex) == null)
            {
                return false;
            }

            WorldMapBehaviour worldMapBehaviour = WorldMgr.Ins.WorldMapBehaviour;
            if (!worldMapBehaviour)
            {
                return false;
            }

            //进攻方和防守方都不在范围内
            if (!worldMapBehaviour.ShowRangeContainsPos(atkPos) && !worldMapBehaviour.ShowRangeContainsPos(defPos))
            {
                return false;
            }

            return true;
        }
    }
}