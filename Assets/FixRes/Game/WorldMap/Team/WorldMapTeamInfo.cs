using System.Collections;
using System.Collections.Generic;
using UnityEngine;


namespace GameMain
{
    public class WorldMapTeamInfo : WorldMapUnitInfo
    {
        ulong mTeamId = 0;
        string mLeaderName;
        string mLeagueShortName;
        public string Name { get { return $"[{mLeagueShortName}]{mLeaderName}"; } }
        public ulong TeamId => mTeamId;

        public void UpdateData(Packet.WorldTeamInfo info)
        {
            mTeamId = info.TeamId;
            mLeaderName = info.LeaderName;
            mLeagueShortName = info.LeagueShotName;
            UpdateUnitInfo(info.UnitInfo);
        }

        public override bool IsMyUnit => LeagueTeamMgr.Ins.IsLeagueTeam(TeamId);
    }
}

