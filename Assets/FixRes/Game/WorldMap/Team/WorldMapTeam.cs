using Core;
using Cysharp.Threading.Tasks;
using GameMain;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class WorldMapTeam : WorldMapUnit
{
    WorldMapTeamInfo mInfo;
    Transform mModelRoot;
    GameObject mModel;

    public override void Init()
    {
        base.Init();

        mModelRoot = transform.Find("ModelPos");
        UniTask.Create(LoadModel);
    }

    void Update()
    {
        OnUpdate(transform);
    }

    public void SetData(WorldMapTeamInfo info)
    {
        WorldMapTeamInfo oldInfo = mInfo;
        if (oldInfo == info)
        {
            return;
        }

        UpdateState(oldInfo, info);
        mInfo = info;
        mUnitInfo = info;

        UpdateType();
        //EventManager.Ins.DispatchEvent(UIEventType.UpdateWorldMapTroop, this);
    }

    public override void OnRecycle()
    {
        mInfo = null;

        base.OnRecycle();

        ModelMgr.Ins.RemoveModel(mModel);
    }

    async UniTask LoadModel()
    {
        int modelId = Cfg.Const.GetVal1(91); //集结战斗形象低模
        mModel = await ModelMgr.Ins.CreateModelObj(modelId, mModelRoot);
        mModel.transform.localPosition = Vector3.zero;
        mModel.transform.localRotation = Quaternion.identity;
    }

    /// <summary>
    /// 更新部队类型和显示样式
    /// </summary>
    void UpdateType()
    {
        CampType campType = CampType.STRANGER;
        if (mInfo.IsMyUnit)
        {
            campType = CampType.ALLY;
        }

        UpdateCampType(campType);
    }
}
