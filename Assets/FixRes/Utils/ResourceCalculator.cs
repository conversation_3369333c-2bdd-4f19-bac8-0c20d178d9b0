using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.Pool;

namespace Game
{
    public class ResourceCalculator
    {
        public class ResourceItem
        {
            public int Id { get; set; }        // 道具ID
            public int Value { get; set; }      // 道具提供的资源量
            public int Count { get; set; }      // 拥有数量
        }

        public static Dictionary<int, int> Calculate(List<ResourceItem> items, int requiredAmount)
        {
            // 参数检查
            if (items == null || !items.Any() || requiredAmount <= 0)
            {
                return DictionaryPool<int, int>.Get();
            }

            // 按Value从小到大排序
            var sortedItems = items.OrderBy(x => x.Value).ToList();

            // 使用贪心策略：从小到大尝试每种道具
            var result = DictionaryPool<int, int>.Get();
            long remainingAmount = requiredAmount;

            foreach (var item in sortedItems)
            {
                if (remainingAmount <= 0) break;

                // 计算需要使用的数量
                int neededCount = (int)Mathf.Min(
                    Mathf.Ceil((float)remainingAmount / item.Value),
                    item.Count);

                if (neededCount > 0)
                {
                    result[item.Id] = neededCount;
                    remainingAmount -= (long)item.Value * neededCount;
                }
            }

            // 如果无法达到目标值，检查是否所有道具都用完还是不够
            if (remainingAmount > 0)
            {
                // 计算使用所有道具能获得的资源总量
                long totalPossible = items.Sum(item => (long)item.Value * item.Count);

                if (totalPossible < requiredAmount)
                {
                    // 如果所有道具都不够，返回使用所有道具的方案
                    return items.ToDictionary(item => item.Id, item => item.Count);
                }
                else
                {
                    // 如果理论上够但是贪心策略找不到解，尝试使用最大价值的道具
                    result.Clear();
                    var maxValueItem = sortedItems.Last();
                    int count = (int)Mathf.Ceil((float)requiredAmount / maxValueItem.Value);
                    if (count <= maxValueItem.Count)
                    {
                        result[maxValueItem.Id] = count;
                    }
                }
            }

            return result;
        }
    }
}