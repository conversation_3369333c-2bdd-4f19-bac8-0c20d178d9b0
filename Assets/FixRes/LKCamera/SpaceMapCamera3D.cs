using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.EventSystems;

using UnityEngine.UI;

namespace Game.LKCamera
{
    /// <summary>
    /// 3DCamara控制脚本
    /// </summary>

    public class SpaceMapCamera3D : MonoBehaviour
    {
        // UICamera
        public Camera UICamera;

        // 点击特效
        public bool enableTouchEffect = true;

        public bool enableGyro = false;

        // 速度衰减（乘）
        public float moveDecay = 0.075f;
        // 返回速度
        public float backSpeed = 0.95f;
        //移动速度
        public float moveSpeed = 1f;
        // 锁定横竖移动
        public bool horizontalLock = false;
        public bool verticalLock = false;
        public bool taglock = true;
        // 缩放限制
        public bool zoomLock = true;
        public float zoomInLimit = 4f;
        public float zoomOutLimit = 10f;
        // 缩放速度
        public float zoomSpeed = 0.02f;
        // 缩放回弹阈值
        public float zoomRestrict = 1f;
        // 缩放时移动速度
        public float zoomOffsetSpeed = 0.1f;

        // 地图拖动限制，顺序为：left, top, right, bottom
        public Vector4 mapLimit;
        public float addRange = 0f;

        Vector2 _OldPosition = Vector2.zero;
        // 摄像机
        public Camera Camera { get { return _Camera; } }
        // 摄像机
        protected Camera _Camera;
        protected Vector4 _CameraLimit;
        protected Vector4 _CameraLimitRange;
        protected Camera _MainCamera;
        // 回弹时候的目标位置
        Vector3 _TargetPos;
        // 动量
        Vector2 _Momentum = Vector2.zero;
        // 是否发生过移动或缩放
        bool _Moved = false;
        // 锁死触控
        bool _Lock = false;
        // 城市拖拽状态
        int _DragState = 0;
        // 拖动
        protected Camera3dDragable _DragObj;

        bool _IsTweenPos = false;
        bool _IsTweenSize = false;

        private Quaternion mOrgRotation = Quaternion.identity;

        public bool isRotation = false;

        public Vector2 pinchLimit;

        protected Vector2 _CameraPinchLimit;
        protected Vector2 _CameraPinchLimitRange;

        //移动速度
        public float pinchSpeed = 1f;

        public float pinchScale = 1f;

        private float _pinchLength = 0f;

        private bool _IsInDrag = false;

        Vector3 _pinchPosition = Vector2.zero;

        public float rotateSpeed = 1f;

        public float TargetAngleMaxX = 45;
        public float TargetAngleMinX = -45;

        private Transform _target;
        public Transform target
        {
            set
            {
                if(_target != null)
                {
                    smoothTime = Mathf.Lerp(0.3f, smoothTimeScale, Vector3.Distance(_target.position, value.position));
                }
                else
                {
                    smoothTime = 0.3f;
                }
                _target = value;
                
            }
            get
            {
                return _target;
            }
        }
         
        public Vector3 LocalOffset = Vector3.zero;

        private Vector3 _smoothPos = Vector3.zero;

        public float pinchVal = 0;
        public float minPinchVal = 0;
        public float maxPinchVal = 0;

        /// <summary>
        /// 缓存的Transform
        /// </summary>
        public Transform CachedTransform { get; protected set; }

        /// <summary>
        /// 绕X轴旋转角度
        /// </summary>
        public float TargetAngleX = 30f;
        /// <summary>
        /// 绕Z轴旋转角度
        /// </summary>
        public float TargetAngleZ = 0f;

        /// <summary>
        /// 绕Y轴旋转角度
        /// </summary>
        public float TargetAngleY = 0;

        private Vector3 _smooth = Vector3.one;

        private float _fixAngleX;
        private float _fixAngleY;
        private float _fixAngleZ;

        private Vector3 _fixPos = Vector3.one;

        public bool isInSpace = false;

        public Vector3 MaxMap = Vector3.one;
        public Vector3 MinMap = Vector3.zero;

        public float smoothTime = 0;
        public float smoothTimeScale = 0.1f;

        private void Awake()
        {
            _fixAngleX = TargetAngleX;
            _fixAngleY = TargetAngleY;
            CachedTransform = transform;
            _Camera = GetComponent<Camera>();
            if (_Camera == null)
            {
                Debug.LogError("Camera3D is not binding to a Camera!");
                return;
            }

            ResetCameraLimit();
        }

        //初始化游戏信息设置
        protected virtual void Start()
        {
            _Camera = GetComponent<Camera>();
            /*_MainCamera = _Camera;//Camera.main;*/
            if (enableGyro && SystemInfo.supportsGyroscope)
            {
                Input.gyro.enabled = true;
                mOrgRotation = transform.localRotation;
            }
            else
            {
                FingerGestures.RegGestureHandler<TapDragGesture>(OnDragGesture);
                FingerGestures.RegGestureHandler<PinchGesture>(OnPinchGesture);
            }
        }
        /// <summary>
        /// 析构函数
        /// </summary>
        protected virtual void OnDestroy()
        {
            if (enableGyro && SystemInfo.supportsGyroscope)
            {
                Input.gyro.enabled = false;
            }
            else
            {
                FingerGestures.UnRegGestureHandler<TapDragGesture>(OnDragGesture);
                FingerGestures.UnRegGestureHandler<PinchGesture>(OnPinchGesture);
            }

            //CancelInvoke();
        }

        private void OnEnable()
        {
            if (enableGyro && SystemInfo.supportsGyroscope)
            {
                Input.gyro.enabled = true;
            }
        }

        private void Update()
        {
            //Input.gyro.rotationRateUnbiased
            if (enableGyro && SystemInfo.supportsGyroscope)
            {
                /*float deltaY = Input.gyro.rotationRateUnbiased.y;
                float deltaX = Input.gyro.rotationRateUnbiased.x;
                //if (Mathf.Abs(deltaY) > moveSpeed || Mathf.Abs(deltaX) > moveSpeed)
                Move(-deltaY, deltaX);*/

                float vertRotateRate = Input.gyro.rotationRateUnbiased.x;

                vertRotateRate = Mathf.Sign(vertRotateRate) * Mathf.Clamp(Mathf.Abs(vertRotateRate), 0, moveSpeed);

                float horiRotateRate = Input.gyro.rotationRateUnbiased.y;

                horiRotateRate = Mathf.Sign(horiRotateRate) * Mathf.Clamp(Mathf.Abs(horiRotateRate), 0, moveSpeed);

                float factor = Mathf.Max(Mathf.Abs(vertRotateRate), Mathf.Abs(horiRotateRate)) / moveSpeed;

                //z轴不旋转

                transform.Rotate(horiRotateRate, -vertRotateRate, 0);

                //逐帧往初始位置回复

                transform.localRotation = Quaternion.Slerp(transform.localRotation, mOrgRotation, backSpeed + backSpeed * factor);
            }

            Zoom(Input.GetAxis("Mouse ScrollWheel") * 200f, Input.mousePosition);

            _OnUpdateTick();
        }

        void CheckUICollider(TapDragGesture gesture)
        {
            _Lock = false;
            // 2D UI碰撞
            if (taglock == true && EventSystem.current != null)
            {
                for (int i = 0; i < gesture.Fingers.Count; i++)
                {
                    PointerEventData eventDataCurrentPosition = new PointerEventData(EventSystem.current);
                    eventDataCurrentPosition.position = gesture.Fingers[i].Position;

                    //eventDataCurrentPosition.position = new Vector2(Input.mousePosition.x, Input.mousePosition.y);
                    List<RaycastResult> results = new List<RaycastResult>();
                    EventSystem.current.RaycastAll(eventDataCurrentPosition, results);

                    if (results.Count > 0)
                    {
                        for (int j = 0; j < results.Count; j++)
                        {
                            if (results[j].gameObject.layer == 5)
                            {
                                _Lock = true;
                                return;
                            }
                        }
                    }
                }
            }
        }

        void OnDragGesture(TapDragGesture gesture)
        {
            try
            {
                if (gesture == null)
                {
                    //Debug.Log("gesture is null");
                    return;
                }


                if (_Camera == null)
                {
                    //Debug.Log("_Camera is null");
                    return;
                }

                if (gameObject == null)
                {
                    //Debug.Log("gameObject is null");
                    return;
                }

                if (!gameObject.activeSelf)
                {
                    //Debug.Log("gameObject activeSelf is false");
                    return;
                }

                if (Math.Abs(Input.GetAxis("Mouse ScrollWheel")) > 0) return;

                CheckUICollider(gesture);

                if (_Lock) //锁死则不接受事件
                {
                    //Debug.Log("touched UI");
                    return;
                }


                if (!_Camera.gameObject.activeInHierarchy || !_Camera.enabled)
                {
                    //Debug.Log("_Camera disabled");
                    return;
                }

                if (gesture.Phase == ContinuousGesturePhase.Started)
                {
                    StopCoroutine(CheckAndMoveBack());
                    StopCoroutine(SmoothMove());

                    for (int i = 0; i < gesture.Fingers.Count; i++)
                    {
                        OnDragStart(gesture.Fingers[i].StartPosition);
                    }

                    _Moved = true;

                    _IsInDrag = true;
                }
                else if (gesture.Phase == ContinuousGesturePhase.Updated)
                {
                    if (gesture.Fingers.Count == 1 && _IsInDrag)
                    {
                        FingerGestures.Finger finger = gesture.Fingers[0];
                        _OldPosition.Set(finger.DeltaPosition.x, finger.DeltaPosition.y);
                        if (isInSpace)
                        {
                            SpaceMove(finger.DeltaPosition.x, finger.DeltaPosition.y);
                        }
                        else if (isRotation)
                        {
                            if (target)
                            {
                                RotateByTagget(finger.DeltaPosition.x, finger.DeltaPosition.y);
                            }
                            else
                            {
                                RotateSelf(finger.DeltaPosition.x, finger.DeltaPosition.y);
                            }
                        }
                        else
                        {
                            Move(finger.DeltaPosition.x, finger.DeltaPosition.y);
                        }
                    }
                    else if(_IsInDrag)
                    {
                        _OldPosition.Set(0, 0);
                    }

                    if (_IsInDrag)
                    {
                        for (int i = 0; i < gesture.Fingers.Count; i++)
                        {
                            OnDraging(gesture.Fingers[i].Position);
                        }
                        _Moved = true;
                    }
                }
                else if (gesture.Phase == ContinuousGesturePhase.Ended)
                {
                    if (_IsInDrag)
                    {
                        for (int i = 0; i < gesture.Fingers.Count; i++)
                        {
                            OnDragEnd(gesture.Fingers[i].Position);
                        }

                        _Moved = false;
                        /*if (gameObject != null && gameObject.activeInHierarchy)
                        {
                            StartCoroutine(SmoothMove(_OldPosition.x, _OldPosition.y));
                            StartCoroutine(CheckAndMoveBack());
                        }*/

                        _IsInDrag = false;
                    }
                }
            }
            catch (Exception ex)
            {
                Core.DLogger.Error(ex.ToString());

            }
        }

        // 计算可拖动范围
        protected virtual void ResetCameraLimit()
        {
            float moveElememt = _Camera.transform.localPosition.z;

            // 左
            _CameraLimit.x = _Camera.transform.localPosition.x + mapLimit.x;
            // 上
            _CameraLimit.y = moveElememt + mapLimit.y;
            // 右
            _CameraLimit.z = _Camera.transform.localPosition.x + mapLimit.z;
            // 下
            _CameraLimit.w = moveElememt + mapLimit.w;

            _CameraLimitRange.x = _CameraLimit.x - addRange;
            _CameraLimitRange.y = _CameraLimit.y + addRange;
            _CameraLimitRange.z = _CameraLimit.z + addRange;
            _CameraLimitRange.w = _CameraLimit.w - addRange;

            _pinchLength = GetPinchLeagth(_Camera.transform.localPosition.y, _Camera.transform.localPosition.z);

            _CameraPinchLimit.x = pinchLimit.x;
            _CameraPinchLimit.y = pinchLimit.y;

            _CameraPinchLimitRange.x = _CameraPinchLimit.x - addRange;
            _CameraPinchLimitRange.y = _CameraPinchLimit.y + addRange;

            if(_CameraPinchLimitRange.x < 0)
            {
                _CameraPinchLimitRange.x = 0;
            }

            _pinchPosition = _Camera.transform.localPosition + _Camera.transform.forward * pinchVal;
        }

        public void GetLimitedRange(out float x, out float y, out float z, out float w)
        {
            x = _CameraLimit.x;
            y = _CameraLimit.y;
            z = _CameraLimit.z;
            w = _CameraLimit.w;
        }

        // 焦点缩放
        protected virtual void Zoom(float length, Vector3 focus)
        {
            if (zoomLock) return;
            if (length == 0) return;

            length = -length;

            // 缩放前焦点位置
            // 缩放前焦点位置
            Vector3 org = _Camera.ScreenToWorldPoint(focus);

            // 缩放
            _Camera.fieldOfView += zoomSpeed * length;
            if (_Camera.fieldOfView < zoomInLimit - zoomRestrict)
                _Camera.fieldOfView = zoomInLimit - zoomRestrict;
            if (_Camera.fieldOfView > zoomOutLimit + zoomRestrict)
                _Camera.fieldOfView = zoomOutLimit + zoomRestrict;

            // 对齐焦点
            Vector3 nw = _Camera.ScreenToWorldPoint(focus);

            Vector3 offset = nw - org;

            // 重新计算可拖动范围
            ResetCameraLimit();

            Move(offset.x, offset.y);
        }

        public virtual void MoveTo(float offsetX = 0, float offsetY = 0)
        {
            StopCoroutine(SmoothMove());
            StartCoroutine(SmoothMove(offsetX, offsetY));
        }

        protected IEnumerator SmoothMove(float offsetX = 0, float offsetY = 0)
        {
            if (_Camera == null)
                yield break;

            if (!_Camera.gameObject.activeSelf)
                yield break;

            if (Mathf.Abs(offsetX) < 1f)
                offsetX = 0;
            if (Mathf.Abs(offsetY) < 1f)
                offsetY = 0;

            if (offsetX == 0 && offsetY == 0)
            {
                yield break;
            }

            offsetX *= moveDecay;
            offsetY *= moveDecay;

            if (!Move(offsetX, offsetY))
            {
                yield break;
            }

            yield return new WaitForFixedUpdate();

            yield return SmoothMove(offsetX, offsetY);
        }

        protected IEnumerator CheckAndMoveBack()
        {
            if (_Camera == null)
                yield break;

            if (!_Camera.gameObject.activeInHierarchy)
                yield break;

            Vector3 pos = _Camera.transform.localPosition;
            float moveX = 0, moveY = 0;

            if (pos.x < _CameraLimit.x)
                moveX = pos.x - _CameraLimit.x;
            else if (pos.x > _CameraLimit.z)
                moveX = pos.x - _CameraLimit.z;

            float moveElement = 0f;
            moveElement = pos.z;

            if (moveElement < _CameraLimit.w)
                moveY = moveElement - _CameraLimit.w;
            else if (moveElement > _CameraLimit.y)
                moveY = moveElement - _CameraLimit.y;

            if (Mathf.Abs(moveX) < 0.1f && Mathf.Abs(moveY) < 0.1f)
                yield break;

            //Debug.LogFormat("moveX : {0}  moveY : {1}", moveX, moveY);

            _TargetPos.Set(pos.x - moveX * backSpeed, pos.y, pos.z - moveY * backSpeed);

            _Camera.transform.localPosition = _TargetPos;
            //Debug.LogFormat("x : {0}  y : {1}", _TargetPos.x, _TargetPos.y);
            yield return new WaitForFixedUpdate();

            yield return CheckAndMoveBack();
        }

        // 移动
        protected virtual bool Move(float offsetX, float offsetY)
        {
            if (horizontalLock)
                offsetX = 0;
            if (verticalLock)
                offsetY = 0;
            // 反转
            offsetX = -offsetX * moveSpeed;
            offsetY = -offsetY * moveSpeed;

            //移动
            Vector3 pos = _Camera.transform.localPosition;
            pos.x += offsetX;
            float moveElement = 0f;
            moveElement = pos.z;
            moveElement += offsetY;

            var pinchLength = GetPinchLeagth(pos.y - _pinchPosition.y, pos.z - _pinchPosition.z);
            if (pos.y < _pinchPosition.y)
            {
                pinchLength = -pinchLength;
            }

            bool smooth = true;
            if (pos.x < _CameraLimitRange.x)
            {
                pos.x = _CameraLimitRange.x;
                smooth = false;
            }
            else if (pos.x > _CameraLimitRange.z)
            {
                pos.x = _CameraLimitRange.z;
                smooth = false;
            }


            if (moveElement < _CameraLimitRange.w)
            {
                moveElement = _CameraLimitRange.w;
                smooth = false;
            }

            else if (moveElement > _CameraLimitRange.y)
            {
                moveElement = _CameraLimitRange.y;
                smooth = false;
            }

            pos.z = moveElement;

            _Camera.transform.localPosition = pos;

            if (!_Moved && !smooth)
            {
                if (_Camera.gameObject.activeInHierarchy)
                    StartCoroutine(CheckAndMoveBack());
            }

            return smooth;
        }

        IEnumerator TweenPos(Vector3 pos, float duration)
        {
            Vector2 speed = (pos - _Camera.transform.localPosition) / duration;
            while (_IsTweenPos)
            {
                Vector3 move = pos - _Camera.transform.localPosition;
                if (move.magnitude <= 0)
                {
                    _IsTweenPos = false;
                    yield break;
                }
                Vector2 distance = speed * Time.deltaTime;
                Move(distance.x, distance.y);
                yield return null;
            }
        }

        IEnumerator TweenSize(float size, float duration)
        {
            float speed = (size - _Camera.orthographicSize) / duration;
            while (_IsTweenSize)
            {
                if (size == _Camera.orthographicSize)
                {
                    _IsTweenSize = false;
                    yield break;
                }
                _Camera.orthographicSize += speed * Time.deltaTime;
                yield return null;
            }
        }

        public void TweenPosTo(Vector3 pos, float duration)
        {
            StopTweenPosTo();
            _IsTweenPos = true;
            StartCoroutine(TweenPos(pos, duration));
        }

        public void StopTweenPosTo()
        {
            _IsTweenPos = false;
            StopCoroutine("TweenPos");
        }
        public void TweenSizeTo(float size, float duration)
        {
            StopTweenSizeTo();
            _IsTweenSize = true;
            StartCoroutine(TweenSize(size, duration));
        }
        public void StopTweenSizeTo()
        {
            _IsTweenSize = false;
            StopCoroutine("TweenSizeTo");
        }

        protected virtual void OnTween() { }
        protected virtual void OnTouch(Vector2 screenPos)
        {

        }
        protected virtual void OnTouchRelease(Vector2 screenPos)
        {

        }
        protected void OnDragStart(Vector2 pos) { }
        protected void OnDraging(Vector2 pos) { }
        protected void OnDragEnd(Vector2 pos) { }
        protected void OnClick(Transform obj) { }
        protected void OnPress(Transform obj) { }

        public void SetMapLimit(float xLeft, float xRight, float yBot, float yTop)
        {
            mapLimit = new Vector4(xLeft, yTop, xRight, yBot);
        }

        public void OnResetRotation()
        {
            transform.localRotation = mOrgRotation;
        }

        void OnPinchGesture(PinchGesture gesture)
        {
            if(target != null && isRotation)
            {
                OnPinchRotateTargetGesture(gesture);
            }
            else
            {
                OnPinchNormalGesture(gesture);
            }
        }

        void OnPinchRotateTargetGesture(PinchGesture gesture)
        {
            var z = LocalOffset.z + gesture.Delta * pinchSpeed;
            if(z < minPinchVal)
            {
                z = minPinchVal;
            }
            else if(z > maxPinchVal)
            {
                z = maxPinchVal;
            }
            LocalOffset.z = z;
        }

        void OnPinchNormalGesture(PinchGesture gesture)
        {
            if(_Camera == null)
            {
                _Camera = GetComponent<Camera>();
            }
            var pos = _Camera.transform.localPosition + _Camera.transform.forward * -gesture.Delta * pinchSpeed;

            float pinchLength = 0;
            if (target != null)
            {
                pinchLength = GetProjection(_Camera.transform.forward, pos, target.transform.position);
                Vector3 relativePosition = target.transform.position - pos;
                Vector3 cubeForward = _Camera.transform.forward;
                if (Vector3.Dot(cubeForward, relativePosition) < 0)
                {
                    pinchLength = -pinchLength;
                }
            }
            else
            {
                pinchVal += gesture.Delta * pinchSpeed;
                pinchLength = pinchVal;
            }

            bool smooth = true;

            if (pinchLength < _CameraPinchLimitRange.x)
            {
                var difference = _CameraPinchLimitRange.x - pinchLength;
                pinchVal = _CameraPinchLimitRange.x;
                pos = pos - _Camera.transform.forward * difference;
            }

            else if (pinchLength > _CameraPinchLimitRange.y)
            {
                var difference = pinchLength - _CameraPinchLimitRange.y;
                pinchVal = _CameraPinchLimitRange.y;
                pos = pos + _Camera.transform.forward * difference;
            }

            _Camera.transform.localPosition = pos;
        }

        float GetPinchLeagth(float x, float y)
        {
            return Mathf.Sqrt(x * x + y * y);
        }

        protected IEnumerator CheckAndPinchBack()
        {
            if (_Camera == null)
                yield break;

            if (!_Camera.gameObject.activeInHierarchy)
                yield break;

            Vector3 pos = _Camera.transform.localPosition;
            float moveX = 0, moveY = 0, moveZ = 0;

            float moveElementY = pos.y;

            if (moveElementY < _CameraPinchLimit.x)
                moveY = moveElementY - _CameraPinchLimit.x;
            else if (moveElementY > _CameraPinchLimit.y)
                moveY = moveElementY - _CameraPinchLimit.y;

            float factor = (moveElementY - _pinchLength) / (_CameraPinchLimit.y - _CameraPinchLimit.x);
            factor = 1 - factor * pinchScale;

            if (pos.x < _CameraLimit.x * factor)
                moveX = pos.x - _CameraLimit.x * factor;
            else if (pos.x > _CameraLimit.z * factor)
                moveX = pos.x - _CameraLimit.z * factor;

            float moveElementZ = pos.z;

            if (moveElementZ < _CameraLimit.w * factor)
                moveZ = moveElementZ - _CameraLimit.w * factor;
            else if (moveElementZ > _CameraLimit.y * factor)
                moveZ = moveElementZ - _CameraLimit.y * factor;

            if (Mathf.Abs(moveX) < 0.1f && Mathf.Abs(moveZ) < 0.1f && Mathf.Abs(moveY) < 0.1f)
                yield break;

            //Debug.LogFormat("moveX : {0}  moveY : {1}", moveX, moveY);

            _TargetPos.Set(pos.x - moveX * backSpeed, pos.y - moveY * backSpeed, pos.z - moveZ * backSpeed);

            _Camera.transform.localPosition = _TargetPos;
            //Debug.LogFormat("x : {0}  y : {1}", _TargetPos.x, _TargetPos.y);
            yield return new WaitForFixedUpdate();

            yield return CheckAndMoveBack();
        }

        // 移动
        protected virtual bool RotateSelf(float rotateX, float rotateY)
        {
            // 反转
            rotateX = -rotateX * rotateSpeed;
            rotateY = rotateY * rotateSpeed;

            //移动
            Vector3 angles = _Camera.transform.eulerAngles;
            angles.y += rotateX;
            angles.x += rotateY;

            Quaternion slerpRotation = Quaternion.Slerp(transform.rotation, transform.rotation * Quaternion.Euler(rotateY, rotateX, 0), Time.deltaTime);
            _Camera.transform.rotation = transform.rotation * Quaternion.Euler(rotateY, rotateX, 0);

            return true;
        }

        protected virtual bool RotateByTagget(float rotateX, float rotateY)
        {
            if (target == null)
            {
                return false;
            }
            // 反转
            TargetAngleX -= rotateY * rotateSpeed;
            TargetAngleY += rotateX * rotateSpeed;

            if(TargetAngleX < TargetAngleMinX)
            {
                TargetAngleX = TargetAngleMinX;
            }
            else if(TargetAngleX > TargetAngleMaxX)
            {
                TargetAngleX = TargetAngleMaxX;
            }

            return true;
        }

        private Vector3 FixVector3(Vector3 orgVector)
        {
            float n = (float)Math.Pow(10, 3);
            return new Vector3(FixFloat(orgVector.x, n), FixFloat(orgVector.y, n), FixFloat(orgVector.z, n));
        }

        private float FixFloat(float x, float n)
        {
            float tmp = x * n;
            tmp = Mathf.Ceil(tmp);
            return tmp / n;
        }

        protected virtual void _OnUpdateTick()
        {
            if (target == null || !isRotation)
            {
                return;
            }

            _fixPos = Vector3.SmoothDamp(_fixPos, target.position, ref _smoothPos, smoothTime);

            _fixAngleY = Mathf.SmoothDampAngle(_fixAngleY, TargetAngleY, ref _smooth.y, 0.3f);
            _fixAngleX = Mathf.SmoothDampAngle(_fixAngleX, TargetAngleX, ref _smooth.x, 0.3f);

            Quaternion rotation = Quaternion.Euler(_fixAngleX, _fixAngleY, TargetAngleZ);
            transform.rotation = rotation;


            var lookPosAt = _fixPos + transform.rotation * LocalOffset;
            lookPosAt = FixVector3(lookPosAt);
            transform.position = lookPosAt;
        }

        // 移动
        protected virtual bool SpaceMove(float offsetX, float offsetY)
        {
            if (horizontalLock)
                offsetX = 0;
            if (verticalLock)
                offsetY = 0;
            // 反转
            offsetX = -offsetX * moveSpeed;
            offsetY = -offsetY * moveSpeed;

            //移动
            Vector3 pos = _Camera.transform.localPosition + _Camera.transform.up * offsetY + _Camera.transform.right * offsetX;

            _Camera.transform.localPosition = pos;

            return true;
        }

        protected virtual float GetSite(Vector3 form, Vector3 to, Vector3 site)
        {
            return (to.y - form.y) * (site.z - form.z) - (to.z - form.z) * (site.y - form.y);
        }

        public float Angle(Vector3 cen, Vector3 first, Vector3 second)
        {
            float M_PI = Mathf.PI;

            float ma_x = first.x - cen.x;
            float ma_y = first.y - cen.y;
            float ma_z = first.z - cen.z;
            float mb_x = second.x - cen.x;
            float mb_y = second.y - cen.y;
            float mb_z = second.z - cen.z;
            float v1 = (ma_x * mb_x) + (ma_y * mb_y) + (ma_z * mb_z);
            float ma_val = Mathf.Sqrt(ma_x * ma_x + ma_y * ma_y + ma_z * ma_z);
            float mb_val = Mathf.Sqrt(mb_x * mb_x + mb_y * mb_y + mb_z * mb_z);
            float cosM = v1 / (ma_val * mb_val);
            float angleAMB = Mathf.Acos(cosM) * 180 / M_PI;

            return angleAMB;
        }

        public float GetProjection(Vector3 up, Vector3 pos, Vector3 targetPos)
        {
            float t = (up.x * (targetPos.x - pos.x) + up.y * (targetPos.y - pos.y) + up.z * (targetPos.z - pos.z)) / (up.x * up.x + up.y * up.y + up.z * up.z);
            Vector3 projectionPos = new Vector3(up.x * t + pos.x, up.y * t + pos.y, up.z * t + pos.z);
            
            return Vector3.Distance(projectionPos, pos);
        }

        public void OnResetCamera()
        {
            if (target == null)
                return;

            _smoothPos = Vector3.zero;
            _fixPos = target.position;

            _fixAngleX = TargetAngleX;
            _fixAngleY = TargetAngleY;

            Quaternion rotation = Quaternion.Euler(TargetAngleX, TargetAngleY, TargetAngleZ);
            transform.rotation = rotation;

            var lookPosAt = _fixPos + transform.rotation * LocalOffset;
            lookPosAt = FixVector3(lookPosAt);
            transform.position = lookPosAt;
        }

        public void SetOffsetZ(float value)
        {
            LocalOffset.z = value;
        }
    }
}

