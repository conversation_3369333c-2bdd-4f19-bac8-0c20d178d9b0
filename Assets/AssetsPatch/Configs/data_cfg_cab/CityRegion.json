[{"id": 1, "levelID": [10001, 10002, 10003, 10004, 10005, 10006], "modelId": 2119, "modelName": "InnerCity_Land_05", "tagID": 0}, {"id": 2, "levelID": [10007, 10008, 10009, 10010, 10011, 10012], "modelId": 2118, "modelName": "InnerCity_Land_04", "tagID": 0}, {"id": 3, "levelID": [10013, 10014, 10015, 10016, 10017, 10018], "modelId": 2117, "modelName": "InnerCity_Land_03", "tagID": 0}, {"id": 4, "levelID": [10019, 10020, 10021, 10022, 10023, 10024], "modelId": 2116, "modelName": "InnerCity_Land_02", "tagID": 0}, {"id": 5, "levelID": [10025, 10026, 10027, 10028, 10029, 10030], "modelId": 2115, "modelName": "InnerCity_Land_01", "tagID": 1}, {"id": 6, "levelID": [10031, 10032, 10033, 10034, 10035, 10036], "modelId": 2120, "modelName": "InnerCity_Land_06", "tagID": 0}, {"id": 7, "levelID": [10037, 10038, 10039, 10040, 10041, 10042], "modelId": 2121, "modelName": "InnerCity_Land_07", "tagID": 0}, {"id": 8, "levelID": [10043, 10044, 10045, 10046, 10047, 10048], "modelId": 2122, "modelName": "InnerCity_Land_08", "tagID": 0}, {"id": 9, "levelID": [10049, 10050, 10051, 10052, 10053, 10054], "modelId": 2123, "modelName": "InnerCity_Land_09", "tagID": 0}, {"id": 10, "levelID": [10055, 10056, 10057, 10058, 10059, 10060], "modelId": 2124, "modelName": "InnerCity_Land_10", "tagID": 0}, {"id": 11, "levelID": [10061, 10062, 10063, 10064, 10065, 10066], "modelId": 2125, "modelName": "InnerCity_Land_11", "tagID": 0}, {"id": 12, "levelID": [10067, 10068, 10069, 10070, 10071, 10072], "modelId": 2126, "modelName": "InnerCity_Land_12", "tagID": 0}, {"id": 13, "levelID": [10073, 10074, 10075, 10076, 10077, 10078], "modelId": 2127, "modelName": "InnerCity_Land_13", "tagID": 0}, {"id": 14, "levelID": [10079, 10080, 10081, 10082, 10083, 10084], "modelId": 2128, "modelName": "InnerCity_Land_14", "tagID": 0}, {"id": 15, "levelID": [10085, 10086, 10087, 10088, 10089, 10090], "modelId": 2129, "modelName": "InnerCity_Land_15", "tagID": 0}, {"id": 16, "levelID": [10091, 10092, 10093, 10094, 10095, 10096], "modelId": 2130, "modelName": "InnerCity_Land_16", "tagID": 0}, {"id": 17, "levelID": [10097, 10098, 10099, 10100, 10101, 10102], "modelId": 2131, "modelName": "InnerCity_Land_17", "tagID": 2}]