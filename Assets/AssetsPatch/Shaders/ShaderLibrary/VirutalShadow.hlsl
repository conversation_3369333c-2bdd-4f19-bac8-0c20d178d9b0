#ifndef _VIRTUAL_SHADOW_MAP_
#define _VIRTUAL_SHADOW_MAP_


#include  "RuntimeVirutalTexture.hlsl"

// int GetVirtualShadowTextureIndex(float3 positionWS)
// {
//     float4 pos = mul(_VirtualLightWorldToLocalMatrix,float4(positionWS,1));
//     // pos.x +=1;
//     // pos.y +=1;
//     /// TODO: 先这样写,后续改成virtual texture 来获取数据
//     float offsetX = pos.x - _VirtualShadowParams.y;
//     float offsetZ = pos.y - _VirtualShadowParams.z;
//
//     // 筛选Index
//     int xIndex = offsetX * _VirtualShadowParams.w + 0.5;
//     int zIndex = offsetZ * _VirtualShadowParams.w + 0.5;
//    // xIndex = offsetX / 32.0;
//     int idx = zIndex + xIndex * _VirtualShadowParams2.y;
//
//     return max(0,min(idx,3));
// }
//

// float4 TransformWorldToVirutalShadowCoord(float3 worldPos,out int index)
// {
//     index = GetVirtualShadowTextureIndex(worldPos);
//     // index = 1;
//     return mul(_WorldToVirtualShadow[index], float4(worldPos, 1.0));
// }

#ifdef VIRUTAL_POINT
real SampleVirtualShadowmapPoint(float3 worldPos,float2 uv,int index )
{
    real attenuation ;
    if(index == 0)
    {
        attenuation = SAMPLE_TEXTURE2D_SHADOW(_VirtualShadowmapTexturePoint_0,
        sampler_VirtualShadowmapTexturePoint_0,float3(uv ,0.9 ));

    }else if (index == 1){
        attenuation = SAMPLE_TEXTURE2D_SHADOW(_VirtualShadowmapTexturePoint_1,
      sampler_VirtualShadowmapTexturePoint_0,float3(uv ,0.9 ));
    }

    // return uv.y;
    return attenuation;
}

real InternalPointTest(float2 uv ,int index,float z)
{

    #ifndef UNITY_REVERSED_Z
    z *= -1;
    #endif
    real attenuation = 0 ;
    if(index == 0)
    {
        // for(float i = 0.010;i <0.8;i+= 0.1)
        {
            float t = real(SAMPLE_TEXTURE2D_SHADOW(_VirtualShadowmapTexturePoint_0,
                sampler_VirtualShadowmapTexturePoint_0, float3(uv.x,uv.y,z )));
            attenuation +=t * 1;
        }
    }else if (index == 1){
        // for(float i = 0.01;i < 0.8;i+= 0.1)
        {
            float t = real(SAMPLE_TEXTURE2D_SHADOW(_VirtualShadowmapTexturePoint_1,
                sampler_VirtualShadowmapTexturePoint_0, float3(uv.x,uv.y,z )));
            attenuation +=t * 1;
        }
    }
    return attenuation;
}

real SampleVirtualShadowmapPoint(float3 worldPos,int index,float shadowAttenuation)
{
    #if USE_CLUSTERED_LIGHTING
    int lightIndex = index;
    #else
    int lightIndex = GetPerObjectLightIndex(index);
    #endif
    int perObjectLightIndex = lightIndex;
    int dataIndex = _VirutalPointLightIndex2ArrayIndex[perObjectLightIndex];
    if(dataIndex < 0)
        return shadowAttenuation;

    float3 offset = worldPos - _VirutalPointPositions[dataIndex].xyz;
    half3 dir = normalize(offset);
    // worldPos = worldPos - dir * 2; // 采样离点光源远一点,避免产生 凹凸 阴影
    float ligthLength = length(offset);

    if(_VirutalPointPositions[dataIndex].w < ligthLength )
        return shadowAttenuation;

    float cubemapFaceId = CubeMapFaceID(dir);


    int pointCubemapFaceId = dataIndex * 6 + cubemapFaceId;

    float4 state = _Points_CubemapFace_State[pointCubemapFaceId];
    if (state.x < 1)
        return 1;

    float4 shadowCoord = mul(_WorldToVirtualShadow_Points[pointCubemapFaceId],float4(worldPos,1));

    if(shadowCoord.w != 0)
        shadowCoord.xyz /= shadowCoord.w;

    float attenuation = shadowAttenuation ;
    if(dataIndex == 0)
    {
        attenuation = SAMPLE_TEXTURE2D_SHADOW(_VirtualShadowmapTexturePoint_0,
            sampler_VirtualShadowmapTexturePoint_0,shadowCoord.xyz);
    }else if(dataIndex == 1)
    {
        attenuation = SAMPLE_TEXTURE2D_SHADOW(_VirtualShadowmapTexturePoint_1,
            sampler_VirtualShadowmapTexturePoint_0,shadowCoord.xyz);
    }

    // attenuation = BEYOND_SHADOW_FAR(shadowCoord) ? 1.0 : attenuation;
    return attenuation ;
}
#endif

real PointTest(float2 uv ,int index,float z)
{
    #ifdef VIRUTAL_POINT
    return InternalPointTest(uv,index,z);
    #endif

    return 1;
}

real SampleVirtualShadowmap(float3 worldPos)
{
    return SampleRVT(worldPos);
}

real SampleVirtualShadowmap(
    TEXTURE2D_SHADOW_PARAM(ShadowMap, sampler_ShadowMap), float4 shadowCoord, ShadowSamplingData samplingData,
    half4 shadowParams, bool isPerspectiveProjection = true)
{
    // Compiler will optimize this branch away as long as isPerspectiveProjection is known at compile time
    if (isPerspectiveProjection)
    {
        if( shadowCoord.w != 0)
        {
            shadowCoord.xyz /= shadowCoord.w;
        }
    }

    real attenuation;
    real shadowStrength = shadowParams.x;

    #ifdef _SHADOWS_SOFT
    if(shadowParams.y != 0)
    {
        attenuation = SampleShadowmapFiltered(TEXTURE2D_SHADOW_ARGS(ShadowMap, sampler_ShadowMap), shadowCoord, samplingData);
    }
    else
    #endif
    {
        // 1-tap hardware comparison
        attenuation = real(SAMPLE_TEXTURE2D_SHADOW(ShadowMap, sampler_ShadowMap, shadowCoord.xyz));
    }

    attenuation = LerpWhiteTo(attenuation, shadowStrength);
    // return 0;
    // return attenuation;
    // Shadow coords that fall out of the light frustum volume must always return attenuation 1.0
    // TODO: We could use branch here to save some perf on some platforms.
    return BEYOND_SHADOW_FAR(shadowCoord) ? 1.0 : attenuation;
}


half GetMainLightShadowFade_VirtualShadowMap(float3 positionWS)
{
    float3 camToPixel = positionWS - _WorldSpaceCameraPos;
    float distanceCamToPixel2 = dot(camToPixel, camToPixel);

    float fade = saturate(distanceCamToPixel2 * 0.0005);
    // float fade = saturate(distanceCamToPixel2 * float(0.01) + float(-0));
    return half(fade);
}

real GetDirectionLightShadowAttenuation_VirtualShadowMap(InputData inputData,float sourceShadowAttenuation)
{
    float sst = SampleRVT(inputData.positionWS);
    return  min(sst, sourceShadowAttenuation);
}

real GetDirectionLightShadowAttenuation_VirtualShadowMap(InputData inputData)
{
    return SampleRVT(inputData.positionWS);
}

real GetPointLightShadowAttenuation_VirtualShadowMap(InputData inputData,int index,float shadowAttenuation)
{
    #if (VIRUTAL_POINT)
    return SampleVirtualShadowmapPoint(inputData.positionWS,index,shadowAttenuation);
    #else

    return shadowAttenuation;

    #endif
}


#endif
