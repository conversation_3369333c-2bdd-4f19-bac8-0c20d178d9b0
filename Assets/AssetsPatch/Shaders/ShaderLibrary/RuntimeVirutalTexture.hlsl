#ifndef _RUNTIME_VIRUTAL_TEXTURE_COM_
#define _RUNTIME_VIRUTAL_TEXTURE_COM_

#include "VirtualShadowDefine.hlsl"

#ifdef ENABLE_VIRTUAL_SHADOW_MAP

int CacleGroup(float3 positionWS)
{
    float2 uv = mul(_GlobalToRVT_BASE, float4(positionWS, 1)).xy;
    float absX = abs(uv.x);
    float absY = abs(uv.y);
    float4 weights1 = float4(float4(absX, absX, absX, absX) < _VSM_Groups);
    float4 weights2 = float4(float4(absY, absY, absY, absY) < _VSM_Groups);

    float4 weights = weights1 * weights2;

    weights.yzw = saturate(weights.yzw - weights.xyz);

    float4 mask = float4(0, 1, 2, 3);
    float total = weights[0] + weights[1] + weights[2] + weights[3];
    return lerp(3, dot(weights, mask), saturate(total));
}

float2 clampUV(float2 uv,float4 ruv)
{
    return clamp(uv, ruv.xy, ruv.zw);
}


float4 BilinearSmaple(TEXTURE2D_SHADOW_PARAM_VSM(sourceTexture, samplerSourceTexture),float2 uv,float z,float4 ruv,float4 texSize)
{
    #ifdef _VSM_BILINEARSAMPLE_
    float2 offsetXY = texSize.xy;
    float2 offsetInvertXY = texSize.zw;
    // float offset = texSize.z;
    int xcount = floor(uv.x * offsetXY.x);
    float left = xcount * offsetInvertXY.x;
    float right = left + offsetInvertXY.x;

    int ycount = floor(uv.y * offsetXY.y);
    float bottom = ycount * offsetInvertXY.y;
    float top = bottom + offsetInvertXY.y;

    float2 left_bottom = float2(left,bottom);
    float2 left_top = float2(left,top);

    float2 right_bottom = float2(right,bottom);
    float2 right_top = float2(right,top);

    left_bottom = clampUV(left_bottom,ruv);
    left_top = clampUV(left_top,ruv);
    right_bottom = clampUV(right_bottom,ruv);
    right_top = clampUV(right_top,ruv);

    float4 lb = SAMPLE_TEXTURE2D(sourceTexture,samplerSourceTexture,left_bottom);
    float4 lt = SAMPLE_TEXTURE2D(sourceTexture,samplerSourceTexture,left_top);

    float4 rb = SAMPLE_TEXTURE2D(sourceTexture,samplerSourceTexture,right_bottom);
    float4 rt = SAMPLE_TEXTURE2D(sourceTexture,samplerSourceTexture,right_top);

    #ifdef UNITY_REVERSED_Z
    rb.x = 1 - rb.x;
    rt.x = 1 - rt.x;
    lt.x = 1 - lt.x;
    lb.x = 1 - lb.x;
    #endif

    #ifdef _VSM_ENABLE_ANTIALIASING

    rb.x = min(rb.x,z);
    rt.x = min(rt.x,z);
    lt.x = min(lt.x,z);
    lb.x = min(lb.x,z);

    rb.y = rb.x * rb.x;
    rt.y = rt.x * rt.x;
    lt.y = lt.x * lt.x;
    lb.y = lb.x * lb.x;
    #endif

    // float subOffset = 1.0 / offset; // 1 /2
    float4 h_bottom = lerp(lb,rb,(uv.x - left) * offsetXY.x );
    float4 h_top = lerp(lt,rt,(uv.x - left) *   offsetXY.x);
    float4 v = lerp(h_bottom,h_top,(uv.y - bottom) *  offsetXY.y);
#else
    uv = clampUV(uv,ruv);
    float4 v = SAMPLE_TEXTURE2D(sourceTexture,samplerSourceTexture,uv);

    #ifdef UNITY_REVERSED_Z
        v.x = 1 - v.x;
    #endif

#endif

    return v;
}

float SmapleDepthByZ(TEXTURE2D_SHADOW_PARAM_VSM(sourceTexture, samplerSourceTexture),float2 uv,float z,float4 ruv,float4 texSize)
{
    float v = BilinearSmaple(sourceTexture,samplerSourceTexture,uv,z,ruv,texSize).r;
    return z <= v;
}

float ANTIALIASINGSmaple(TEXTURE2D_SHADOW_PARAM_VSM(sourceTexture, samplerSourceTexture),float2 uv,float z,float4 ruv,float4 texSize)
{
    // source z = 0 far - 1 near
    // return z;
    // trans z
    // target z = 0 near - 1 far



    // #endif

    // z = 0.51
    // b1 0.5,0.25
    // b2 1,1
    // bs1 = x = 0.75,y = 0.625
    // pf = 0.75 * 0.75 =0.5625
    // var = 0.625 - 0.5625 = 0.0625
    // d = 0.75 - 0.51 = 0.24
    // pmax = 0.0625 / (0.0625 + 0.24 * 0.24) = 0.0625 / (0.0625 + 0.0576) = 0.0625 / 0.1201 = 0.5203
    // vz = z <= base.x = 1
    // return max(0.5203, 1) = 1


    // b1 0.5,0.25
    // b2 0.501,0.251001
    // bs1 = x = 0.5005,y = 0.2505005
    // pf = 0.5005 * 0.5005 =0.25050025
    // var = 0.2505005 - 0.25050025 = 0.00000025
    // d = 0.501 - 0.51 = -0.009
    // pmax = 0.00000025 / (0.00000025 + 0.009 * 0.009) = 0.00000025 / (0.00000025 + 0.000081) = 0.00000025 / 0.00008125 = 0.003
    // vz = z <= base.x =  0.51 <= 0.5005 = 0
    // return max(0.003, 0) = 0.003

    float4 base = BilinearSmaple(sourceTexture,samplerSourceTexture,uv,z ,ruv,texSize );
    // return z <= base.x;
    // if(base.y >= 1)
    //     return 0;
    // return 1;

    float pf = base.x * base.x;
    float var = base.y - pf;
    float d =  base.x - z;
    float pMax = (var ) / (var + d * d);

    float amount = _VirtualShadowParams.y;
    pMax = clamp( (pMax - amount) / (1.000001 - amount), 0, 1);

    return max(saturate(pMax),z <= base.x);
}

#if _VSM_ENABLE_ANTIALIASING

#define SAMPLE_TEXTURE2D_SHADOW_CT(sourceTex,samplersourceTex,uv,ruv,texSize) ANTIALIASINGSmaple(sourceTex,samplersourceTex,uv.xy,uv.z,ruv,texSize)

#else

#define SAMPLE_TEXTURE2D_SHADOW_CT(sourceTex,samplersourceTex,uv,ruv,texSize) SmapleDepthByZ(sourceTex,samplersourceTex,uv.xy,uv.z,ruv,texSize)

#endif

float InternalSampleRVTShadow(float2 uv, float z, float2 startAndUV,
                                   TEXTURE2D_SHADOW_PARAM_VSM(sourceTexture, samplerSourceTexture),
                                   float4 sourceTextGridSize,float4 sourceTextSize)
{
    float attenuation = 0;
    float scaler = 0.999999;
    float2 rangeUV = startAndUV + sourceTextGridSize.zw * scaler;
    float4 rUV = float4(startAndUV,rangeUV);
    #if _VSM_SHADOW_SOFT_7x7_
    real fetchesWeights[16];
    real2 fetchesUV[16];
    // float4      _MainLightShadowmapSize;  // (xy: 1/width and 1/height, zw: wiTestVRTdth and height)
    SampleShadow_ComputeSamples_Tent_7x7(real4(sourceTextSize.zw * 0.25,sourceTextSize.xy *4), uv, fetchesWeights, fetchesUV);
    // vsm_Depth_SourceTexture
    attenuation =   fetchesWeights[0] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[0].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[1] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[1].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[2] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[2].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[3] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[3].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[4] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[4].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[5] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[5].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[6] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[6].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[7] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[7].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[8] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[8].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[9] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[9].xy, z),rUV,sourceTextSize);
    attenuation += fetchesWeights[10] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[10].xy,z),rUV,sourceTextSize);
    attenuation += fetchesWeights[11] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[11].xy,z),rUV,sourceTextSize);
    attenuation += fetchesWeights[12] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[12].xy,z),rUV,sourceTextSize);
    attenuation += fetchesWeights[13] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[13].xy,z),rUV,sourceTextSize);
    attenuation += fetchesWeights[14] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[14].xy,z),rUV,sourceTextSize);
    attenuation += fetchesWeights[15] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[15].xy,z),rUV,sourceTextSize);

    #elif _VSM_SHADOW_SOFT_5x5_

    real fetchesWeights[9];
    real2 fetchesUV[9];
    // float4      _MainLightShadowmapSize;  // (xy: 1/width and 1/height, zw: width and height)
    SampleShadow_ComputeSamples_Tent_5x5(real4(sourceTextSize.zw,sourceTextSize.xy), uv, fetchesWeights, fetchesUV);
    // vsm_Depth_SourceTexture
    attenuation =   fetchesWeights[0] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[0].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[1] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[1].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[2] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[2].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[3] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[3].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[4] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[4].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[5] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[5].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[6] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[6].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[7] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[7].xy, z),rUV,sourceTextSize);
    attenuation +=  fetchesWeights[8] * SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture, samplerSourceTexture, float3(fetchesUV[8].xy, z),rUV,sourceTextSize);

    #elif _VSM_SHADOW_SOFT_NONE_
        attenuation = SAMPLE_TEXTURE2D_SHADOW_CT(sourceTexture,samplerSourceTexture,float3(uv,z),rUV,sourceTextSize);
    #else
    attenuation = 1;
    #endif

    return attenuation;
}

float InternalSampleRVT(float3 worldPos, half shadowStrength,
                float4x4 globalToBlockUV,
                Texture2D blockTexture, SamplerState samplerblockTexture,
                Texture2D sourceTexture, SamplerState samplerSourceTexture,
                float4 sourceTextGridSize,
                float4 blockSize,const float4 sourceTextSize)
{
    float4 bUV = mul(globalToBlockUV, float4(worldPos, 1));
    float2 blockUV = bUV.xy;
    float z = bUV.z; // ��c#��ת��,near 0 - 1 far

    // #ifdef UNITY_REVERSED_Z
    //     z = 1 - z;
    // #endif
    //
    if (z < 0)
    return 1;

    UNITY_BRANCH
    if (blockUV.x < 0 || blockUV.x > 1 || blockUV.y < 0 || blockUV.y > 1)
        return 1;

    float2 uv = blockUV;
    float4 data = SAMPLE_TEXTURE2D(blockTexture, samplerblockTexture, uv);
    float index = data.r * sourceTextGridSize.x * sourceTextGridSize.y;
    index = round(index);

    UNITY_BRANCH
    if (index == 0)
        return 1;

    index -= 1; // ��Ϊ���ݴ洢�Ǵ�1��ʼ��,���������ת��0��ʼ
    uv = fmod(uv, float2(blockSize.y, blockSize.y)) * blockSize.z;
    float2 uvTemp = uv;
    int shadowIndex = index;
    float u = fmod(shadowIndex, sourceTextGridSize.x);
    float v = (int)(shadowIndex * sourceTextGridSize.z);
    float u_f = sourceTextGridSize.z * u;
    float v_f = sourceTextGridSize.w * v;

    float2 startUV = float2(u_f, v_f);
    uvTemp *= sourceTextGridSize.zw; // ��ͼ����
    uvTemp = startUV + uvTemp;
    uv = uvTemp;
    float attenuation = 0;

    attenuation = InternalSampleRVTShadow (uv,z,startUV, sourceTexture,samplerSourceTexture,sourceTextGridSize,sourceTextSize);
    attenuation = LerpWhiteTo(attenuation, shadowStrength);

    return attenuation;
}

float SampleRVTByGroup(float3 worldPos, half shadowStrength)
{
    const int group = CacleGroup(worldPos);
    
    UNITY_BRANCH
    if (group == 0)
    {
        return InternalSampleRVT(worldPos, shadowStrength,_VSM_RVT_SAMPLE_PARAMS(0));
    }
    else if (group == 1)
    {
        return InternalSampleRVT(worldPos, shadowStrength,_VSM_RVT_SAMPLE_PARAMS(1));
    }

    #if ENABLE_VIRTUAL_TEXTURE_3_4
    
    else if(group == 2)
    {
        return InternalSampleRVT(worldPos, shadowStrength,_VSM_RVT_SAMPLE_PARAMS(2));
    }
    else if(group == 3)
    {
        return InternalSampleRVT(worldPos, shadowStrength,_VSM_RVT_SAMPLE_PARAMS(3));
    }
    
#endif

    else
    {
        return 1;
    }
}


float4 SampleRVTAllInternal(float2 uv,TEXTURE2D_SHADOW_PARAM_VSM(sourceTexture, samplerSourceTexture),
                            float4 sourceTextGridSize)
{

    // float4 vt = tex2D(vsm_Depth_SourceTexture,uv);
    // SampleShadowmap(sourceTexture, )
    float4 vt =  SAMPLE_TEXTURE2D(sourceTexture,samplerSourceTexture,uv);
    // return float4(vt.x,vt.y,0,1);
    // float4 vt = sourceTexture.Sample(sampler_SourceTexture_1,uv);
    float v = vt.r;
    // v = SAMPLE_DEPTH_TEXTURE(sourceTexture,sampler_SourceTexture_1,uv);
    // float v = tex2D(sourceTexture,uv).r;

    // return float4( abs(v),0,0,1);
    // #if false
    float2 uv2 = fmod(uv, sourceTextGridSize.zw) / sourceTextGridSize.xy * 2;
    // uv2 = 0;
    float attenuation = 0;
    for (float i = 0; i < 1; i += 0.1)
    {
        float t = real(SAMPLE_TEXTURE2D_SHADOW_VSM(sourceTexture, samplerSourceTexture, float3(uv.x,uv.y,i )));
        attenuation += t * 0.1;
    }

    return float4(attenuation , uv2.x * 0.5, uv2.y* 0.5, 1);
    // #endif
}

float4 SampleRVTAllInternal(float2 uv, int group)
{
    uv.y = 1 - uv.y;
    uv.x = 1 - uv.x;

    if (group == 0)
    {
        return SampleRVTAllInternal(uv,_VSM_RVT_SourceTexture(0),_VSM_RVT_SourceTextGridSize(0));
    }

    UNITY_BRANCH
    if (group == 1)
    {
        return SampleRVTAllInternal(uv,_VSM_RVT_SourceTexture(1),_VSM_RVT_SourceTextGridSize(1));
    }

    #if ENABLE_VIRTUAL_TEXTURE_3_4
    UNITY_BRANCH
    if (group == 2)
    {
        return SampleRVTAllInternal(uv,_VSM_RVT_SourceTexture(2),_VSM_RVT_SourceTextGridSize(2));
    }

    UNITY_BRANCH
    if (group == 3)
    {
        return SampleRVTAllInternal(uv,_VSM_RVT_SourceTexture(3),_VSM_RVT_SourceTextGridSize(3));
    }
    #endif

    return float4(0, 0, 0, 1);
}


float4 SampleBlockInternal(float2 uv,TEXTURE2D_PARAM(bloclTex, sampleTex))
{
    uv.y = 1 - uv.y;
    uv.x = 1 - uv.x;
    float4 data = SAMPLE_TEXTURE2D(bloclTex, sampleTex, uv);
    float index = data.r;
    return float4(index , 0, 0, 1);
}

float4 SampleBlockInternal(float2 uv, int group)
{
    if (group == 0)
    {
        return SampleBlockInternal(uv,_VSM_RVT_BlockTexture(0));
    }

    UNITY_BRANCH
    if (group == 1)
    {
        return SampleBlockInternal(uv,_VSM_RVT_BlockTexture(1));
    }
    UNITY_BRANCH
    if (group == 2)
    {
        return SampleBlockInternal(uv,_VSM_RVT_BlockTexture(2));
    }

    UNITY_BRANCH
    if (group == 3)
    {
        return SampleBlockInternal(uv,_VSM_RVT_BlockTexture(3));
    }

    return float4(0, 0, 0, 1);
}


#endif // ENABLE_VIRTUAL_SHADOW_MAP

float4 SampleBlock(float2 uv, int group)
{
    #ifdef ENABLE_VIRTUAL_SHADOW_MAP
        return SampleBlockInternal(uv, group);
    #else
        return 1;
    #endif
}



float SampleRVT(float3 worldPos, float shadowStrength)
{
    #if _RECEIVE_SHADOWS_OFF
        return 1;
    #else

        #ifdef ENABLE_VIRTUAL_SHADOW_MAP
            return SampleRVTByGroup(worldPos, shadowStrength);
        #else
            return 1;
        #endif

    #endif
}

float SampleRVT(float3 worldPos)
{
    #ifdef ENABLE_VIRTUAL_SHADOW_MAP
    return SampleRVT(worldPos, _VirtualShadowParams.x);
    #else
    return 1;
    #endif
}


float4 SampleRVTAll(float2 uv, int group)
{
    #ifdef ENABLE_VIRTUAL_SHADOW_MAP
        return SampleRVTAllInternal(uv, group);
    #else
        return float4(0, 0, 0, 1);
    #endif
}


#endif

