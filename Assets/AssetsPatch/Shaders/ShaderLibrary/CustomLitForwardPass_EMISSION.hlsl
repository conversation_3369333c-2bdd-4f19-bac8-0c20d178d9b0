#ifndef CUSTOM_UNIVERSAL_FORWARD_LIT_PASS_INCLUDED_NO_EMISSION
#define CUSTOM_UNIVERSAL_FORWARD_LIT_PASS_INCLUDED_NO_EMISSION

#include "Packages/com.unity.render-pipelines.universal/Shaders/LitInput.hlsl"
#include "Assets/WorkArt/Shader/ShaderLibrary/CustomLitForwardPassBase.hlsl"


inline void InitializeStandardLitSurfaceData2(float2 uv, out SurfaceData outSurfaceData)
{
    outSurfaceData = (SurfaceData)0;
    half4 albedoAlpha = SampleAlbedoAlpha(uv, TEXTURE2D_ARGS(_BaseMap, sampler_BaseMap));
    outSurfaceData.alpha = Alpha(albedoAlpha.a, _BaseColor, _Cutoff);

    half4 specGloss = SampleMetallicSpecGlossEx(uv, albedoAlpha.a);
    outSurfaceData.albedo = albedoAlpha.rgb * _BaseColor.rgb;

#if _SPECULAR_SETUP
    outSurfaceData.metallic = 1.0h;
    outSurfaceData.specular = specGloss.rgb;
#else
    outSurfaceData.metallic = specGloss.r;
    outSurfaceData.specular = half3(0.0h, 0.0h, 0.0h);
#endif

    outSurfaceData.smoothness = specGloss.a;
    outSurfaceData.normalTS = SampleNormalEx(uv, TEXTURE2D_ARGS(_BumpMap, sampler_BumpMap), _BumpScale);
    outSurfaceData.occlusion = SampleOcclusionEx(uv);
    outSurfaceData.emission = SampleEmissionExOpen(uv, _EmissionColor.rgb, TEXTURE2D_ARGS(_EmissionMap, sampler_EmissionMap));
}


// Used in Standard (Physically Based) shader
half4 LitPassFragment2(Varyings input) : SV_Target
{
    UNITY_SETUP_INSTANCE_ID(input);
    UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

    SurfaceData surfaceData;
    InitializeStandardLitSurfaceData2(input.uv, surfaceData);

    InputData inputData;
    InitializeInputDataEx(input, surfaceData.normalTS, inputData);

    half4 color = UniversalFragmentPBR_Lightmap(inputData, surfaceData.albedo,
        surfaceData.metallic, surfaceData.specular, surfaceData.smoothness, surfaceData.occlusion,
        surfaceData.emission, surfaceData.alpha);

    color.rgb = MixFog(color.rgb, inputData.fogCoord);
    color.a = OutputAlpha(color.a,_Surface);

    return color;
}


#endif
