// Shader created with Shader Forge v1.38 
// Shader Forge (c) Neat Corporation / <PERSON> - http://www.acegikmo.com/shaderforge/
// 注意：手动更改此数据可能会妨碍您在 Shader Forge 中打开它
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:0,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:0,bdst:0,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:False,aust:True,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:False,fgod:False,fgor:False,fgmd:0,fgcr:0.5,fgcg:0.5,fgcb:0.5,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:False,fnfb:False,fsmp:False;n:type:ShaderForge.SFN_Final,id:3138,x:34763,y:32370,varname:node_3138,prsc:2|emission-3969-OUT;n:type:ShaderForge.SFN_Color,id:7241,x:32880,y:32621,ptovrint:False,ptlb:MainColor,ptin:_MainColor,varname:node_7241,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Tex2d,id:8818,x:32778,y:31904,ptovrint:False,ptlb:MainTex,ptin:_MainTex,varname:node_8818,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-6233-OUT;n:type:ShaderForge.SFN_Desaturate,id:8392,x:33064,y:32045,varname:node_8392,prsc:2|COL-8818-RGB,DES-4038-OUT;n:type:ShaderForge.SFN_ValueProperty,id:4038,x:32778,y:32179,ptovrint:False,ptlb:desaturate,ptin:_desaturate,varname:node_4038,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Multiply,id:65,x:33452,y:32183,varname:node_65,prsc:2|A-8392-OUT,B-7241-RGB,C-5330-RGB;n:type:ShaderForge.SFN_Multiply,id:2296,x:33499,y:32441,varname:node_2296,prsc:2|A-8818-A,B-7241-A,C-5330-A;n:type:ShaderForge.SFN_VertexColor,id:5330,x:32763,y:32406,varname:node_5330,prsc:2;n:type:ShaderForge.SFN_ValueProperty,id:4155,x:31721,y:32105,ptovrint:False,ptlb:MainTexV,ptin:_MainTexV,varname:node_4155,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:7878,x:31721,y:31880,ptovrint:False,ptlb:MainTexU,ptin:_MainTexU,varname:node_7878,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Time,id:1178,x:31721,y:31945,varname:node_1178,prsc:2;n:type:ShaderForge.SFN_Multiply,id:4970,x:31990,y:31826,varname:node_4970,prsc:2|A-7878-OUT,B-1178-T;n:type:ShaderForge.SFN_Multiply,id:3362,x:31969,y:32086,varname:node_3362,prsc:2|A-1178-T,B-4155-OUT;n:type:ShaderForge.SFN_Append,id:8070,x:32157,y:31856,varname:node_8070,prsc:2|A-4970-OUT,B-3362-OUT;n:type:ShaderForge.SFN_TexCoord,id:7199,x:32170,y:32129,varname:node_7199,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Add,id:6233,x:32320,y:31968,varname:node_6233,prsc:2|A-8070-OUT,B-7199-UVOUT;n:type:ShaderForge.SFN_Multiply,id:1995,x:34051,y:32337,varname:node_1995,prsc:2|A-65-OUT,B-2296-OUT;n:type:ShaderForge.SFN_Tex2d,id:2270,x:33051,y:32918,ptovrint:False,ptlb:Mask,ptin:_Mask,varname:node_2270,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False;n:type:ShaderForge.SFN_Multiply,id:3048,x:33846,y:32710,varname:node_3048,prsc:2|A-2270-G,B-2270-R;n:type:ShaderForge.SFN_Multiply,id:5290,x:34331,y:32423,varname:node_5290,prsc:2|A-1995-OUT,B-3717-OUT,C-3048-OUT;n:type:ShaderForge.SFN_ValueProperty,id:3717,x:34023,y:32875,ptovrint:False,ptlb:Multiply,ptin:_Multiply,varname:node_3717,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Power,id:3969,x:34493,y:32516,varname:node_3969,prsc:2|VAL-5290-OUT,EXP-7783-OUT;n:type:ShaderForge.SFN_ValueProperty,id:7783,x:34245,y:32661,ptovrint:False,ptlb:Power,ptin:_Power,varname:node_7783,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;proporder:7241-8818-3717-7783-4038-4155-7878-2270;pass:END;sub:END;*/

Shader "G10/F_Add" {
    Properties {
        [HDR]_MainColor ("MainColor", Color) = (0.5,0.5,0.5,1)
        _MainTex ("MainTex", 2D) = "white" {}
        _Multiply ("Multiply", Float ) = 1
        _Power ("Power", Float ) = 1
        _desaturate ("desaturate", Float ) = 0
        _MainTexV ("MainTexV", Float ) = 0
        _MainTexU ("MainTexU", Float ) = 0
        _Mask ("Mask", 2D) = "white" {}
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
            "RenderPipeline"="UniversalPipeline"
        }
        Pass {
            Name "FORWARD"
            Tags {
                "LightMode"="UniversalForward"
            }
            Blend One One
            Cull Off
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_FORWARDBASE
            #include "UnityCG.cginc"
            #pragma multi_compile_fwdbase
            #pragma only_renderers d3d9 d3d11 glcore gles gles3
            #pragma target 3.0
            uniform float4 _MainColor;
            uniform sampler2D _MainTex; uniform float4 _MainTex_ST;
            uniform float _desaturate;
            uniform float _MainTexV;
            uniform float _MainTexU;
            uniform sampler2D _Mask; uniform float4 _Mask_ST;
            uniform float _Multiply;
            uniform float _Power;
            struct VertexInput {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.vertexColor = v.vertexColor;
                o.pos = UnityObjectToClipPos( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
////// Lighting:
////// Emissive:
                float4 node_1178 = _Time;
                float2 node_6233 = (float2((_MainTexU*node_1178.g),(node_1178.g*_MainTexV))+i.uv0);
                float4 _MainTex_var = tex2D(_MainTex,TRANSFORM_TEX(node_6233, _MainTex));
                float4 _Mask_var = tex2D(_Mask,TRANSFORM_TEX(i.uv0, _Mask));
                float3 emissive = pow((((lerp(_MainTex_var.rgb,dot(_MainTex_var.rgb,float3(0.3,0.59,0.11)),_desaturate)*_MainColor.rgb*i.vertexColor.rgb)*(_MainTex_var.a*_MainColor.a*i.vertexColor.a))*_Multiply*(_Mask_var.g*_Mask_var.r)),_Power);
                float3 finalColor = emissive;
                return fixed4(finalColor,1);
            }
            ENDCG
        }
        Pass {
            Name "ShadowCaster"
            Tags {
                "LightMode"="ShadowCaster"
            }
            Offset 1, 1
            Cull Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_SHADOWCASTER
            #include "UnityCG.cginc"
            #include "Lighting.cginc"
            #pragma fragmentoption ARB_precision_hint_fastest
            #pragma multi_compile_shadowcaster
            #pragma only_renderers d3d9 d3d11 glcore gles 
            #pragma target 3.0
            struct VertexInput {
                float4 vertex : POSITION;
            };
            struct VertexOutput {
                V2F_SHADOW_CASTER;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.pos = UnityObjectToClipPos( v.vertex );
                TRANSFER_SHADOW_CASTER(o)
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
                SHADOW_CASTER_FRAGMENT(i)
            }
            ENDCG
        }
    }
    FallBack "Diffuse"
    CustomEditor "ShaderForgeMaterialInspector"
}
