#if !BESTHTTP_DISABLE_ALTERNATE_SSL && (!UNITY_WEBGL || UNITY_EDITOR)
#pragma warning disable
using System;

using BestHTTP.SecureProtocol.Org.BouncyCastle.Crypto.Digests;
using BestHTTP.SecureProtocol.Org.BouncyCastle.Crypto.Parameters;
using BestHTTP.SecureProtocol.Org.BouncyCastle.Crypto.Utilities;
using BestHTTP.SecureProtocol.Org.BouncyCastle.Utilities;

namespace BestHTTP.SecureProtocol.Org.BouncyCastle.Crypto.Macs
{

	/// <summary>
	/// Implementation of the Skein parameterised MAC function in 256, 512 and 1024 bit block sizes,
	/// based on the <see cref="BestHTTP.SecureProtocol.Org.BouncyCastle.Crypto.Engines.ThreefishEngine">Threefish</see> tweakable block cipher.
	/// </summary>
	/// <remarks>
	/// This is the 1.3 version of Skein defined in the Skein hash function submission to the NIST SHA-3
	/// competition in October 2010.
	/// <p/>
	/// Skein was designed by <PERSON><PERSON> - <PERSON> - <PERSON> - <PERSON>hir
	/// <PERSON>re - <PERSON>ayoshi Kohno - Jon Callas - <PERSON>.
	/// </remarks>
	/// <seealso cref="BestHTTP.SecureProtocol.Org.BouncyCastle.Crypto.Digests.SkeinEngine"/>
	/// <seealso cref="BestHTTP.SecureProtocol.Org.BouncyCastle.Crypto.Parameters.SkeinParameters"/>
	public class SkeinMac
		: IMac
	{
		/// <summary>
		/// 256 bit block size - Skein-256
		/// </summary>
		public const int SKEIN_256 = SkeinEngine.SKEIN_256;
		/// <summary>
		/// 512 bit block size - Skein-512
		/// </summary>
		public const int SKEIN_512 = SkeinEngine.SKEIN_512;
		/// <summary>
		/// 1024 bit block size - Skein-1024
		/// </summary>
		public const int SKEIN_1024 = SkeinEngine.SKEIN_1024;

		private readonly SkeinEngine engine;

		/// <summary>
		/// Constructs a Skein MAC with an internal state size and output size.
		/// </summary>
		/// <param name="stateSizeBits">the internal state size in bits - one of <see cref="SKEIN_256"/> <see cref="SKEIN_512"/> or
		///                       <see cref="SKEIN_1024"/>.</param>
		/// <param name="digestSizeBits">the output/MAC size to produce in bits, which must be an integral number of
		///                      bytes.</param>
		public SkeinMac(int stateSizeBits, int digestSizeBits)
		{
			this.engine = new SkeinEngine(stateSizeBits, digestSizeBits);
		}

		public SkeinMac(SkeinMac mac)
		{
			this.engine = new SkeinEngine(mac.engine);
		}

		public string AlgorithmName
		{
			get { return "Skein-MAC-" + (engine.BlockSize * 8) + "-" + (engine.OutputSize * 8); }
		}

		/// <summary>
		/// Optionally initialises the Skein digest with the provided parameters.
		/// </summary>
		/// See <see cref="BestHTTP.SecureProtocol.Org.BouncyCastle.Crypto.Parameters.SkeinParameters"></see> for details on the parameterisation of the Skein hash function.
		/// <param name="parameters">the parameters to apply to this engine, or <code>null</code> to use no parameters.</param>
		public void Init(ICipherParameters parameters)
		{
			SkeinParameters skeinParameters;
			if (parameters is SkeinParameters)
			{
				skeinParameters = (SkeinParameters)parameters;
			}
			else if (parameters is KeyParameter)
			{
				skeinParameters = new SkeinParameters.Builder().SetKey(((KeyParameter)parameters).GetKey()).Build();
			}
			else
			{
				throw new ArgumentException("Invalid parameter passed to Skein MAC init - "
                    + Org.BouncyCastle.Utilities.Platform.GetTypeName(parameters));
			}
			if (skeinParameters.GetKey() == null)
			{
				throw new ArgumentException("Skein MAC requires a key parameter.");
			}
			engine.Init(skeinParameters);
		}

		public int GetMacSize()
		{
			return engine.OutputSize;
		}

		public void Reset()
		{
			engine.Reset();
		}

		public void Update(byte inByte)
		{
			engine.Update(inByte);
		}

		public void BlockUpdate(byte[] input, int inOff, int len)
		{
			engine.BlockUpdate(input, inOff, len);
		}

#if NETCOREAPP2_1_OR_GREATER || NETSTANDARD2_1_OR_GREATER || UNITY_2021_2_OR_NEWER
		public void BlockUpdate(ReadOnlySpan<byte> input)
		{
			engine.BlockUpdate(input);
		}
#endif

		public int DoFinal(byte[] output, int outOff)
		{
			return engine.DoFinal(output, outOff);
		}

#if NETCOREAPP2_1_OR_GREATER || NETSTANDARD2_1_OR_GREATER || UNITY_2021_2_OR_NEWER
		public int DoFinal(Span<byte> output)
		{
			return engine.DoFinal(output);
		}
#endif
	}
}
#pragma warning restore
#endif
