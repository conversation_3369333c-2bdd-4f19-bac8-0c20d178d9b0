using System;
using System.Collections.Generic;
using Core;
using Core.Network;
using UnityEngine;
namespace GameMain.Interface
{
    /// <summary>
    /// 网络管理器
    /// </summary>
    public interface GameNetMgr
    {
        public static GameNetMgr Ins;

        public static bool HasInstance()
        {
            return Ins != null;
        }

        public static void DestroyInstance()
        {
            Ins?.UnInit();
        }

        public void Init();

        public void UnInit();

        /// <summary>
        /// 链接服务端
        /// </summary>
        public void Connect(string host, Action onContented);

        /// <summary>
        /// 关闭连接
        /// </summary>
        public void Close();

        /// <summary>
        /// 重新链接服务器
        /// </summary>
        public void Reconnect();

        public void SendDirect(uint commandId, byte[] message);

    }
}
