using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.UI;

//using HullDelaunayVoronoi.Primitives;
//using HullDelaunayVoronoi.Delaunay;

public partial class UtilToolBase
{
    /// <summary>
    /// 下载的app名称
    /// </summary>
    public const string AppName = "martian.apk";

    public static GameObject CreateGameObject(string name, Transform transform, bool stable = false)
    {
        GameObject gameObject;
        if (stable)
        {
            gameObject = GameObject.Find(name);
            if (gameObject != null)
            {
                return gameObject;
            }
        }
        Debug.LogFormat("CreateGameObject {0}", name);

        gameObject = new GameObject(name);
        if (transform != null)
        {
            gameObject.transform.SetParent(transform, false);
        }
        if (stable)
        {
            GameObject.DontDestroyOnLoad(gameObject);
        }
        return gameObject;
    }

    public static T GetInPath<T>(Transform tf, string path) where T : Component
    {
        if (tf == null) return null;

        Transform child = tf.Find(path);
        if (child == null) return null;

        return child.GetComponent<T>();
    }

    #region 关闭游戏
    /// <summary>
    /// 调用Unity的方法关闭游戏，安卓会有闪退报错
    /// </summary>
    public static void UnityQuitGameApp()
    {

#if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
    }
    /// <summary>
    /// 检查并退出应用App,如果有sdk走sdk的退出
    /// </summary>
    public static void CheckQuitGameApp()
    {
        Debug.LogFormat("CheckQuitGameApp {0}", Game.Sdk.CabbageSdkMgr.HasInstance());
#if USE_SDK
        if (Game.Sdk.CabbageSdkMgr.HasInstance())
        {
            Game.Sdk.CabbageSdkMgr.Ins.QuitGameApp();
        }
        else
#endif
        {
            UnityQuitGameApp();
        }
    }
    #endregion

    #region 时间处理
    /// <summary>
    /// 毫秒转秒
    /// </summary>
    public static float MsToSec(int ms)
    {
        return (ms * 0.001f);
    }
    /// <summary>
    /// 秒转毫秒
    /// </summary>
    public static int SecToMs(float s)
    {
        return Mathf.RoundToInt(s * 1000f);
    }
    /// <summary>
    /// 东八区,3600*8
    /// </summary>
    public const long UTC_OFFSET_LOCAL = 28800L;
    /// <summary>
    /// 1秒UTCTICK
    /// </summary>
    public const long UTCTICK_PER_SECONDS = 10000000L;
    /// <summary>
    /// 每天的秒数
    /// </summary>
    public const int DAY_SECONDS = 86400;
    /// <summary>
    /// 每小时的秒数
    /// </summary>
    public const int HOUR_SECONDS = 3600;
    /// <summary>
    /// 每分的秒数
    /// </summary>
    public const int MINUTE_SECONDS = 60;
    /// <summary>
    /// utc 转本地时间
    /// </summary>
    /// <param name="secondsFromUtcStart"></param>
    /// <returns></returns>
    public static DateTime ToUtcTime2Local(long secondsFromUtcStart)
    {
        DateTime epochTime = new DateTime(1970, 1, 1);
        return epochTime.AddTicks((secondsFromUtcStart + UTC_OFFSET_LOCAL) * UTCTICK_PER_SECONDS);
    }
    /// <summary>
    /// DateTime转utc
    /// </summary>
    public static uint ToUtcSeconds(DateTime dateTime)
    {
        DateTime epochTime = new DateTime(1970, 1, 1);
        if (dateTime.CompareTo(epochTime) >= 0)
        {
            TimeSpan span = (TimeSpan)(dateTime - epochTime);
            if (span.TotalSeconds > UTC_OFFSET_LOCAL)
            {
                return (uint)(span.TotalSeconds - UTC_OFFSET_LOCAL);
            }
        }
        return 0;
    }
    /// <summary>
    /// 获得本日的零点
    /// </summary>
    /// <param name="utcSec"></param>
    /// <returns></returns>
    public static long GetZeroBaseSecond(long utcSec)
    {
        DateTime epochTime = new DateTime(1970, 1, 1);
        DateTime zoneTime = epochTime.AddTicks((utcSec + UTC_OFFSET_LOCAL) * UTCTICK_PER_SECONDS);
        DateTime zeroTime = new DateTime(zoneTime.Year, zoneTime.Month, zoneTime.Day, 0, 0, 0);
        TimeSpan span = (TimeSpan)(zeroTime - epochTime);
        return (long)span.TotalSeconds - UTC_OFFSET_LOCAL;
    }
    /// <summary>
    /// 获得今日还有多少秒数到零点
    /// </summary>
    /// <param name="nowSec"></param>
    /// <returns></returns>
    public static uint GetNewDayDeltaSec(int nowSec)
    {
        DateTime localTime = ToUtcTime2Local((long)nowSec);
        DateTime zeroTime = new DateTime(localTime.Year, localTime.Month, localTime.Day, 0, 0, 0, DateTimeKind.Utc);
        TimeSpan span = (TimeSpan)(zeroTime.AddSeconds(DAY_SECONDS) - localTime);
        return (uint)span.TotalSeconds;
    }
    /// <summary>
    /// 是否同一天
    /// </summary>
    public static bool IsSameDay(long secondsFromUtcStart1, long secondsFromUtcStart2)
    {
        DateTime epochTime = new DateTime(1970, 1, 1);
        DateTime localTime1 = epochTime.AddTicks((secondsFromUtcStart1 + UTC_OFFSET_LOCAL) * UTCTICK_PER_SECONDS);
        DateTime localTime2 = epochTime.AddTicks((secondsFromUtcStart2 + UTC_OFFSET_LOCAL) * UTCTICK_PER_SECONDS);
        return DateTime.Equals(localTime1.Date, localTime2.Date);
    }
    /// <summary>
    /// 是否超过一天
    /// </summary>
    public static bool IsOverOneDay(int timeSpanSeconds)
    {
        TimeSpan span = new TimeSpan(timeSpanSeconds * UTCTICK_PER_SECONDS);
        return span.Days > 0;
    }

    /// <summary>
    /// 小时转秒
    /// </summary>
    public static int Hours2Second(int hour)
    {
        return hour * HOUR_SECONDS;
    }
    /// <summary>
    /// 分钟转秒
    /// </summary>
    public static int Minutes2Seconds(int min)
    {
        return min * MINUTE_SECONDS;
    }
    /// <summary>
    /// 帧转时间
    /// </summary>
    public static float FrameToTime(int frame)
    {
        return frame * Time.fixedDeltaTime;
    }
    /// <summary>
    /// 时间转帧
    /// </summary>
    public static int TimeToFrame(float time)
    {
        return (int)Math.Ceiling((double)(time / Time.fixedDeltaTime));
    }

    /// <summary>
    /// DateTime
    /// </summary>
    public static DateTime SecondsToDateTime(int y, int m, int d, int secondsInDay)
    {
        int hour = secondsInDay / HOUR_SECONDS;
        secondsInDay = secondsInDay % HOUR_SECONDS;
        int min = secondsInDay / MINUTE_SECONDS;
        int second = secondsInDay % MINUTE_SECONDS;
        return new DateTime(y, m, d, hour, min, second);
    }
    /// <summary>
    /// 秒格式输出
    /// </summary>
    public static string SecondsToTimeText(uint secs)
    {
        uint hours = secs / HOUR_SECONDS;
        secs -= hours * HOUR_SECONDS;
        uint mins = secs / MINUTE_SECONDS;
        secs -= mins * MINUTE_SECONDS;
        return string.Format("{0:D2}:{1:D2}:{2:D2}", hours, mins, secs);
    }
    /// <summary>
    /// 获取 utc 1970-1-1到现在的秒数
    /// </summary>
    public static long Get1970ToNowSeconds()
    {
        return (System.DateTime.UtcNow.ToUniversalTime().Ticks - 621355968000000000L) / 10000000;
    }
    /// <summary>
    /// 获取 utc 1970-1-1到现在的毫秒数 
    /// </summary>
    public static long Get1970ToNowMilliseconds()
    {
        return (System.DateTime.UtcNow.ToUniversalTime().Ticks - 621355968000000000L) / 10000;
    }
    /// <summary>
    /// 日期时间转换成UTC秒数
    /// </summary>
    /// <param name="vDate"></param>
    /// <returns></returns>

    public static double DateTimeToUTC(DateTime vDate)
    {
        //转成UTC
        vDate = vDate.ToUniversalTime();
        //基数是1970-01-01　00：00：00
        DateTime dtZone = new DateTime(1970, 1, 1, 0, 0, 0);
        //结果
        return vDate.Subtract(dtZone).TotalSeconds;
    }
    /// <summary>
    /// UTC秒数转换成日期时间
    /// </summary>
    /// <param name="vDate"></param>
    /// <returns></returns>
    public static DateTime UTCToDateTime(double sec)
    {
        //基数是1970-01-01　00：00：00
        DateTime dtZone = new DateTime(1970, 1, 1, 0, 0, 0);
        dtZone = dtZone.AddSeconds(sec).ToLocalTime();
        //结果
        return dtZone;
    }
    #endregion

    /// <summary>
    /// 十六进制颜色转换
    /// </summary>
    /// <param name="hexValue"></param>
    /// <returns></returns>
    public static Color32 HexColor32(uint hexValue)
    {
        byte r = (byte)((hexValue & 0xFF000000) >> 24);
        byte g = (byte)((hexValue & 0x00FF0000) >> 16);
        byte b = (byte)((hexValue & 0x0000FF00) >> 8);
        byte a = (byte)((hexValue & 0x000000FF));

        return new Color32(r, g, b, a);
    }

    /// <summary>
    /// 十六进制颜色转换
    /// </summary>
    /// <param name="hexValue"></param>
    /// <returns></returns>
    public static Color HexColor(uint hexValue)
    {
        float r = ((hexValue & 0xFF000000) >> 24) / 255.0f;
        float g = ((hexValue & 0x00FF0000) >> 16) / 255.0f;
        float b = ((hexValue & 0x0000FF00) >> 8) / 255.0f;
        float a = ((hexValue & 0x000000FF)) / 255.0f;

        return new Color(r, g, b, a);
    }

    public static long GetLong(string num)
    {
        long t = 0;
        long.TryParse(num, out t);
        return t;
    }
    public static int GetInt(string num)
    {
        int t = 0;
        int.TryParse(num, out t);
        return t;
    }

    public static uint GetUint(string num)
    {
        uint t = 0;
        uint.TryParse(num, out t);
        return t;
    }

    public static float GetFloat(string num)
    {
        float t = 0;
        float.TryParse(num, out t);
        return t;
    }
    public static bool GetBool(string num)
    {
        bool t = false;
        bool.TryParse(num, out t);
        return t;
    }

    public static void CheckAndCreateDocument(string doc)
    {
        doc = Path.GetDirectoryName(doc);
        if (!Directory.Exists(doc))
        {
            Directory.CreateDirectory(doc);
        }
    }
}
