using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.Rendering.Universal.Internal;

/// <summary>
/// 通用 全屏的
/// </summary>
public class FullScreenExPassFeature : ScriptableRendererFeature
{
    [System.Serializable]
    public class FullScreenExSettings
    {
        public RenderPassEvent renderEvent = RenderPassEvent.AfterRendering; // Current Render Pass Event
    }

    public class FullScreenExRenderPass : FinalBlitPass
    {
        RenderTargetIdentifier mSource;
        //RenderTargetHandle mTmpRT;
        const string mProfilerTag = "RerenderFullScreenEx";

        public FullScreenExRenderPass(RenderPassEvent evt) : base(evt, null, null)
        {
            renderPassEvent = evt;
        }

        public void Setup(RenderTargetIdentifier src)
        {
            mSource = src;
        }

        // This method is called before executing the render pass.
        // It can be used to configure render targets and their clear state. Also to create temporary render target textures.
        // When empty this render pass will render to the active camera render target.
        // You should never call CommandBuffer.SetRenderTarget. Instead call <c>ConfigureTarget</c> and <c>ConfigureClear</c>.
        // The render pipeline will ensure target setup and clearing happens in an performance manner.
        public override void Configure(CommandBuffer cmd, RenderTextureDescriptor cameraTextureDescriptor)
        {
            var tmpDesc = cameraTextureDescriptor;
            tmpDesc.depthBufferBits = 0;
            //mTmpRT.Init(mRTName);

            //cmd.GetTemporaryRT(mTmpRT.id, tmpDesc, FilterMode.Bilinear);
            //Shader.SetGlobalTexture(mRTName, new RenderTexture(tmpDesc));
        }

        // Here you can implement the rendering logic.
        // Use <c>ScriptableRenderContext</c> to issue drawing commands or execute command buffers
        // https://docs.unity3d.com/ScriptReference/Rendering.ScriptableRenderContext.html
        // You don't have to call ScriptableRenderContext.submit, the render pipeline will call it at specific points in the pipeline.
        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            CommandBuffer cmd = CommandBufferPool.Get(mProfilerTag);
            OnRunExecute?.Invoke(cmd,ref renderingData);
            //Debug("Execute is null.");
            // if (OnRunExecute == null)
            //     Debug("OnRunExecute is null.");
            // Call ScriptableRenderer.SetRenderTarget
            //cmd.Blit(mSource, mTmpRT.Identifier());
            //cmd.Blit(mTmpRT.Identifier(), mSource);
            //cmd.CopyTexture(mSource, mTmpRT.Identifier());

            //cmd.DrawMesh()
            //Shader.SetGlobalTexture(mRTName, mSource);
            context.ExecuteCommandBuffer(cmd);
            CommandBufferPool.Release(cmd);
        }

        /// Cleanup any allocated resources that were created during the execution of this render pass.
        public override void FrameCleanup(CommandBuffer cmd)
        {
            //cmd.ReleaseTemporaryRT(mTmpRT.id);
        }
    }

    FullScreenExRenderPass m_ScriptablePass;

    [SerializeField]
    public FullScreenExSettings settings = new FullScreenExSettings();

    public delegate void FullScreenExecute(CommandBuffer cmd, ref RenderingData renderingData);
    private static FullScreenExecute OnRunExecute = null;

    public static void AddRender(FullScreenExecute fullScreenExecute)
    {
        OnRunExecute += fullScreenExecute;

        //Debug("AddRender");
    }

    public static void RemoveRender(FullScreenExecute fullScreenExecute)
    {
        OnRunExecute -= fullScreenExecute;
        //Debug("RemoveRender");
    }

    public override void Create()
    {
        m_ScriptablePass = new FullScreenExRenderPass(settings.renderEvent);

        //Debug("Create FullScreenRenderPass " + settings.renderEvent);
    }

    // Here you can inject one or multiple render passes in the renderer.
    // This method is called when setting up the renderer once per-camera.
    public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
    {
        //Debug("AddRenderPasses aa ");
        if(renderingData.cameraData.camera.CompareTag("MainCamera") || renderingData.cameraData.isSceneViewCamera)
        {
            //m_ScriptablePass.Setup(renderer.cameraColorTarget);
            renderer.EnqueuePass(m_ScriptablePass);

            //Debug("AddRenderPasses bb ");
        }

    }

    // public static void Debug(string debug)
    // {
    //     #if UNITY_ANDROID
    //    // UnityEngine.Debug.Log(debug);
    //     #endif
    // }

}


