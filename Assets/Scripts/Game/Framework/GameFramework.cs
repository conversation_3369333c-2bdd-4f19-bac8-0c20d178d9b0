#define USING_BUGLY
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Core;
using System;
using GameMain;
using GameMain.Interface;
using Cysharp.Threading.Tasks;
using UObject = UnityEngine.Object;


namespace Game.Framework
{
    /// <summary>
    /// 资源类型，越前面优先加载
    /// </summary>
    public enum ResType
    {
        /// <summary>
        /// 默认资源
        /// </summary>
        Default = 0,
        /// <summary>
        /// 数据
        /// </summary>
        Data,
        /// <summary>
        /// Shader材质
        /// </summary>
        Shader,
        /// <summary>
        /// 字体材质
        /// </summary>
        Font,
        /// <summary>
        /// Fmod音效资源
        /// </summary>
        Fmod,
        /// <summary>
        /// UI预设
        /// </summary>
        UIPrefab,
        /// <summary>
        /// 精灵图集
        /// </summary>
        SpriteAtlas,
        /// <summary>
        /// UI散图
        /// </summary>
        UIAtlas,
        /// <summary>
        /// 模型预设
        /// </summary>
        ModelPrefab,
        /// <summary>
        /// 模型图集
        /// </summary>
        ModelAtlas,
        /// <summary>
        /// 其他资源
        /// </summary>
        Other,
    }

    public class GameFramework : MonoSingleton<GameFramework>
    {
        private bool _IsRestart = false;
        bool mIsInitHotfix = false;

        /// <summary>
        /// 重启
        /// </summary>
        public void SetRestart()
        {
            _IsRestart = true;
        }
        /// <summary>
        /// 是否重启
        /// </summary>
        /// <returns></returns>
        public bool GetIsRestart()
        {
            return _IsRestart;
        }
        public override void Init()
        {
            base.Init();
            _IsStarted = false;
        }
        public override void UnInit()
        {
            base.UnInit();

            //关闭日志管理器
            LogMgr.Ins.UnInit();


            DestroyBaseSys();
            DestroyCoreSys();
        }

        public async UniTask InitSystem()
        {
            //启动日志管理器
            LogMgr.Ins.StartLogic();

            DLogger.Log("InitSystem 1");
            //显示参数初始化
            InitTargetSetting();

            DLogger.Log("InitSystem 2");
            //读取基本配置
            await AppVersionMgr.Ins.InitIni();
            DLogger.Log("InitSystem 3");
            //语言表初始化
            InitLocalization();
            await Localization.InitLanguage();
            DLogger.Log("InitSystem 4");
        }

        public void InitLocalization()
        {
            // 本地化系统初始化
            Localization.Init();
        }

        protected virtual async UniTaskVoid Start()
        {
            await InitSystem();

            InitBaseSys();
            await UniTask.Yield();

            _IsStarted = true;
        }

        /// <summary>
        /// 初始化目标平台参数
        /// </summary>
        public void InitTargetSetting()
        {
#if !UNITY_WEBGL
            Screen.orientation = ScreenOrientation.AutoRotation;
            Screen.autorotateToLandscapeLeft = false;
            Screen.autorotateToLandscapeRight = false;
            Screen.autorotateToPortrait = true;
            Screen.autorotateToPortraitUpsideDown = false;
#endif
            Application.targetFrameRate = Constants.GameFrameRate;
            //QualitySettings.vSyncCount = 0;
#if (UNITY_IOS || UNITY_ANDROID) && !UNITY_EDITOR
        Application.targetFrameRate = Constants.GameFrameRate;
        Application.backgroundLoadingPriority = ThreadPriority.High;
        //Application.runInBackground = true;
        // 关闭休眠
        Screen.sleepTimeout = SleepTimeout.NeverSleep;
#endif
        }
        /// <summary>
        /// 初始化Bugly SDK
        /// </summary>
        public void InitBugly()
        {
            //Bugly
#if USING_BUGLY
#if UNITY_ANDROID && !UNITY_EDITOR
            //BuglyAgent.InitWithAppId("8459d411c7");
#elif UNITY_IOS && !UNITY_EDITOR
            //BuglyAgent.InitWithAppId("15bab9835a");
#endif
#endif
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        override protected void OnDestroy()
        {
            UnInit();
            DLogger.Log("~GameFramework was destroyed");
            base.OnDestroy();
        }

        public void StartDelay()
        {
            NotchScreenTools.Ins.CheckNotchScreen();
        }
        private bool _IsStarted = false;
        public bool IsStarted
        {
            get
            {
                return _IsStarted;
            }
        }
        public void CheckInitBoot()
        {
            #if USE_SDK
            if (Sdk.CabbageSdkMgr.Ins.IsNeedAuthorization())
            {
                if (Sdk.CabbageSdkMgr.Ins.IsFirstAuthorization)
                {
                    InitBoot();
                }
            }
            else
#endif
            {
                InitBoot();
            }
        }

        private bool _IsInitBooted = false;
        public bool IsInitBooted
        {
            get
            {
                return _IsInitBooted;
            }
        }
        public void InitBoot()
        {
            if (_IsInitBooted)
            {
                return;
            }
            _IsInitBooted = true;
            try
            {
                //检查强更
                AppDownloadMgr.Ins.CheckAppVersion();

                InitBugly();
            }
            catch (Exception ex)
            {
                DLogger.Error(ex.ToString());
            }

            //GameStateMgr.Ins.InitBoot();

        }

        protected virtual void Update()
        {
            try
            {
                _Tick(Time.deltaTime);

            }
            catch (Exception e)
            {
                DLogger.Error("[GameFramework] Update Exception = {0}", e.Message);
            }
        }
        private void _Tick(float deltaTime)
        {
            if (!_IsInited)
            {
                return;
            }


            if (!mIsInitHotfix) return;
            TickBaseSys(deltaTime);
            TickCoreSys(deltaTime);
        }

        /// <summary>
        /// 初始化基础系统
        /// </summary>
        public virtual void InitBaseSys()
        {
            Debug.Log("InitBaseSys ++");

            PackageMgr.CreateInstance();
            PackageMgr.Ins.AfterInit(AppVersionMgr.Ins.UsePackageFileList());
            GameChannelMgr.CreateInstance();

            #if USE_SDK
            Game.Sdk.CabbageSdkMgr.InitInstance();
#endif

            Debug.Log("InitBaseSys --");
        }
        /// <summary>
        /// 销毁基础系统
        /// </summary>
        public virtual void DestroyBaseSys()
        {
            #if USE_SDK
            Game.Sdk.CabbageSdkMgr.DestroyInstance();
#endif
            VideoPlayerMgr.DestroyInstance();
            AppVersionMgr.DestroyInstance();
            PackageMgr.DestroyInstance();
            GameChannelMgr.DestroyInstance();
            GameStateMgr.DestroyInstance();
            UIUpdateController.DestroyInstance();
        }
        //基础系统
        protected virtual void TickBaseSys(float deltaTime)
        {
            //int deltaMs = UtilToolBase.SecToMs(deltaTime);
            //CachePoolMgr.Ins.Tick(deltaTime);
        }
        

        /// <summary>
        /// 检查是否初始化还是重启
        /// </summary>
        public async UniTask CheckAndInitCoreSys()
        {
            try
            {
                if (_IsRestart)
                {
                    //StartCoroutine(RestartCoreSys());
                    await RestartCoreSys();
                }
                else
                {
                    //初始化核心系统
                    DLogger.Log("InitCoreSys");
                    UIUpdateController.Ins.SetPercent(0);
                    UIUpdateController.Ins.SetUpdateMsg(Localization.GetValue(LocalLanCode.InitCoreSys));
                    if (!mIsInitHotfix)
                        await GameHotfixMgr.Ins.InitHotfixs();

                    mIsInitHotfix = true;

                    StartDelay();
                    //安卓模式下等sdk初始化完成再回调
                    if (!Application.isEditor && Application.platform != RuntimePlatform.Android)
                        CheckInitBoot();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError(ex.ToString());
            }

        }
        /// <summary>
        /// 重启核心系统
        /// </summary>
        public async UniTask RestartCoreSys()
        {
            DLogger.Log("RestartCoreSys");
            UIUpdateController.Ins.SetPercent(0.1f);
            UIUpdateController.Ins.SetUpdateMsg(Localization.GetValue(LocalLanCode.InitCoreSys));
            //清除资源池
            CachePoolMgr.Ins.Destroy();
            //清除资源
            //GameLuaMgr.Instance.ResetInitFinished();
            GameResMgr.Ins.ResetInitFinished();
            GameUtilMgr.Ins.UnInit();

            UIUpdateController.Ins.SetPercent(0.2f);
            //GameLuaMgr.Instance.UnInit();
            GameResMgr.Ins.UnInit();
            SortImageAtlasMgr.Ins.UnInit();

            UIUpdateController.Ins.SetPercent(0.48f);
            //重新初始化
            //GameLuaMgr.Instance.Init();

            GameResMgr.Ins.Init();
            SortImageAtlasMgr.Ins.Init();

            UIUpdateController.Ins.SetPercent(0.8f);

            await GameHotfixMgr.Ins.InitCoreSystem();
            UIUpdateController.Ins.SetPercent(1f);
        }
        /// <summary>
        /// 清除核心系统
        /// </summary>
        public void DestroyCoreSys()
        {
            GameNetMgr.DestroyInstance();
            GameResMgr.DestroyInstance();
            //GameLuaMgr.DestroyInstance();
            GameUtilMgr.DestroyInstance();
            SortImageAtlasMgr.DestroyInstance();
            CachePoolMgr.DestroyInstance();
        }
        protected virtual void TickCoreSys(float deltaTime)
        {

        }

        //下载版本描述文件并检查资源版本
        public async UniTask DownloadConfAndCheckRes()
        {
            await PreDownloadMgr.Ins.CheckUpdateResource();
        }

        public async UniTask DownloadConfAndCheckResOnRelogin()
        {
            await PreDownloadMgr.Ins.CheckUpdateResource(false);
        }

        //在登录启动游戏前，下载版本描述
        public async UniTask DownloadConfAndCheckResOnStartGame(Action callBack)
        {
            
            await PreDownloadMgr.Ins.CheckUpdateResource(false);

            if (callBack != null)
            {
                callBack();
            }
        }

        public async UniTask BeginToInitSystem()
        {
            UIUpdateController.Ins.SetVersionText(); //显示包体版本号

            //初始化核心系统 
            await CheckAndInitCoreSys();
        }
    }
}

