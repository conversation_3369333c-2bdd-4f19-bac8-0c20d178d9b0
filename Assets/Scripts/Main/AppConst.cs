using System.IO;
using UnityEngine;

namespace GameMain
{
    public class AppConst
    {
        /// <summary>
        /// 自定义目录
        /// </summary>
        public static readonly string WritePath = "LocalFile";

        /// <summary>
        /// 可读可写路径
        /// </summary>
        public static string WritablePath
        {
            get
            {
                return Core.Constants.WritablePath;
            }
        }

        /// <summary>
        /// 安装包可读路径
        /// </summary>
        public static string StreamingPath = Application.streamingAssetsPath;

        /// <summary>
        /// WWW读取路径
        /// </summary>
        public static string StreamingURL =
#if UNITY_STANDALONE_WIN || UNITY_EDITOR || UNITY_IPHONE
            "file://" + Application.streamingAssetsPath;
#else
            Application.streamingAssetsPath;
#endif

#if UNITY_EDITOR
        public static bool DebugMode = true;                        //调试模式-用于内部测试
        public const bool LuaByteMode = false;                      //Lua字节码模式-默认关闭 
        public static bool LuaBundleMode                     //Lua代码AssetBundle模式
        {
            get { return Core.Constants.ResourceMode == (int)Core.Constants.DevelopFlag.Download; }
        }
        public static bool LuaEncryptMode                   //Lua代码加密模式
        {
            get
            {
                return Core.Constants.ResourceMode == (int)Core.Constants.DevelopFlag.Download;
            }
        }
        public static bool LuaRawMode                     //Lua代码Raw模式
        {
            get
            {
                return Core.Constants.ResourceMode == (int)Core.Constants.DevelopFlag.Download;
            }
        }

#else
#if UNITY_STANDALONE
           public const bool DebugMode = false;                        //调试模式-用于内部测试
           public const bool LuaByteMode = false;                      //Lua字节码模式-默认关闭 
           public const bool LuaBundleMode = true;                     //Lua代码AssetBundle模式
           public const bool LuaEncryptMode = true;                   //Lua代码加密模式
           public const bool LuaRawMode = true;                       //Lua代码Raw模式
#elif UNITY_ANDROID
           public const bool DebugMode = false;                        //调试模式-用于内部测试
           public const bool LuaByteMode = false;                      //Lua字节码模式-默认关闭 
           public const bool LuaBundleMode = true;                     //Lua代码AssetBundle模式
           public const bool LuaEncryptMode = true;                   //Lua代码加密模式
           public const bool LuaRawMode = true;                       //Lua代码Raw模式
#elif UNITY_IOS
            public const bool DebugMode = false;                        //调试模式-用于内部测试
            public const bool LuaByteMode = false;                       //Lua字节码模式-默认关闭 
            public const bool LuaBundleMode = true;                     //Lua代码AssetBundle模式
            public const bool LuaEncryptMode = true;                   //Lua代码加密模式
            public const bool LuaRawMode = true;                       //Lua代码Raw模式
#endif
#endif
        public const string LuaPathName = "luapath";
        //Lua打包目录
        public const string LuaPathDir = LuaPathName + "/";

        public static void ClearPath()
        {
            mLuaDir = string.Empty;
            mBaseLuaDir = string.Empty;
        }
        //游戏lua目录
        static string mLuaDir = string.Empty;
        public static string LuaDir
        {
            get
            {
                if (string.IsNullOrEmpty(mLuaDir))
                {
                    mLuaDir = WritablePath + "/Lua/Lua/";
                }
                return mLuaDir;
            }
        }

        //tolua基础库
        static string mBaseLuaDir = string.Empty;
        public static string BaseLuaDir
        {
            get
            {
                if (string.IsNullOrEmpty(mBaseLuaDir))
                {
                    mBaseLuaDir = WritablePath + "/Lua/ToLua/";
                }
                return mBaseLuaDir;
            }
        }
        //新的lua
        public const string LuaOriginalName = "lua_org";
        public const string LuaNewName = "lua_new";
        public const string LuaDifferenceName = "lua_dif";
        public const int LuaRawVersion = 1;
        public const string CSDllDoc = "csdll";
        /// <summary>
        /// lua ab包
        /// </summary>
        public const string ExtName = ".ab";
        /// <summary>
        /// 版本号第1位乘数，比如1.7转成数字是107
        /// </summary>
        public const int MAJOR_FACTOR = 100;
    }
}

