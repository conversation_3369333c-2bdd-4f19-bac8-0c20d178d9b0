{"name": "com.unity.assetbundlebrowser", "displayName": "<PERSON><PERSON> Bundle Browser", "version": "1.7.0", "unity": "2018.1", "description": "The Asset Bundle Browser tool makes it possible to inspect the content of built AssetBundles.  It also has basic functionality to view and edit the configuration of AssetBundles and to launch builds.\n\nUse this tool as an alternative to selecting assets and setting their asset bundle manually in the inspector. Installing it will create a new menu item in Window > AssetBundle Browser.\n\nNote: For new projects consider using the Addressables package instead.", "keywords": ["asset", "bundle", "bundles", "assetbundles"], "category": "<PERSON><PERSON>", "dependencies": {}}