using System;
using UnityEngine.Experimental.Rendering;
using UnityEngine.Rendering.Universal.VirtualShadow;
using UnityEngine.Rendering.Universal.VirtualTexture;
using UnityEngine.Serialization;

namespace UnityEngine.Rendering.Universal
{
    /// <summary>
    /// virtual shadow map config.
    /// </summary>
    [System.Serializable]
    public class VirtualShadowData
    {

        public int PointRenderTextrueSize = 1024;

        public int PointRenderTextrueCacheCountLimit = 3;

        public float far = 400f;

        public float lightDrawShadowZOffset = 10f;

        public int RVTBlockSize = 30;

        public int RVTRadius = 16;

        public Vector2Int RVTTexGridSize = new Vector2Int(10,10);

        // public Vector2Int RVTTexSize = new Vector2Int(1920, 1080);

        public bool DynamicAdjustmentBlockSizeAndRadius = false;

        public float AdjustmentRadiusPercentage = 0.25f;

        public int SourceTexGridMaxSize = 128;

        public int OneTickDrawGrid = 4;

        [FormerlySerializedAs("RaduisRecommendAlgorithm")] public AdjustmentRaduisRecommendAlgorith raduisRecommendAlgorith = AdjustmentRaduisRecommendAlgorith.MaxBounds;

        public RendererGridComputerAlgorith rendererGridComputerAlgorith = RendererGridComputerAlgorith.AABBScanning;

        public FilterMode ShadowFilterMode = FilterMode.Point;

        // public Material ShadowMaterial = null;

        public Vector2Int GetRVTBlockSize()
        {
            return new Vector2Int(RVTBlockSize, RVTBlockSize);
        }

        public Vector4 gridDistanCount = new(2,1,1,1);
        public Vector4 gridDistancing = new(8,16,24,32);

        public Vector2Int RVTTexSizeByGroup0 = new Vector2Int(512, 512);
        public Vector2Int RVTTexSizeByGroup1 = new Vector2Int(512, 512);
        public Vector2Int RVTTexSizeByGroup2 = new Vector2Int(128, 128);
        public Vector2Int RVTTexSizeByGroup3 = new Vector2Int(128, 128);

        public bool enableAntiAliasing = true;

        public float checkDistance = 10f;

        [Range(0,1f)]
        public float mVarianceShadowExpansion = 0.5f;

        public Vector2Int GetRVTTexSize(RVTTokenGroup group)
        {
            if (group == RVTTokenGroup.Group_0)
                return RVTTexSizeByGroup0;

            if (group == RVTTokenGroup.Group_1)
                return RVTTexSizeByGroup1;
            if (group == RVTTokenGroup.Group_2)
                return RVTTexSizeByGroup2;
            if (group == RVTTokenGroup.Group_3)
                return RVTTexSizeByGroup3;
            return Vector2Int.zero;
        }

        public ComputeShader mGPUGrahicsComputeShader;

    }

    public enum PowerNumberType
    {
        _2 = 2,
        _4 = 4,
        _8 = 8,
        _16 = 16,
        _32 = 32,
        _64 = 64,
        _128 = 128,
        _256 = 256,
        _512 = 512,
        _1024 = 1024,
        _2048 = 2048,
        _4096 = 4096,
        // _8192 = 8192,
    }

}
