using System;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using UnityEngine.Rendering.Universal.VirtualShadow;
using UnityEngine.Rendering.Universal.VirtualTexture;

namespace UnityEngine.Rendering.Universal.VirtualShadow
{
    [ExecuteAlways]
    [ExecuteInEditMode]
    public class DebugShadows : MonoBehaviour
    {
        [NonSerialized] public VirtualShadowDebugSetting Settings;
        public static DebugShadows Instance;

        [HideInInspector] public Mesh mesh;
        public  bool drawBlock = false;

        public bool drawSphere = false;

        public RVTTokenGroup drawGroup;

        public bool isDrawAllGroup = false;

        public Color blockColor;

        public float blockZOffset;

        public bool drawGrids;

        public int drawBlockCount = 4;

        [Range(0,1)]
        public float scaler = 1;

        public int maxBatches;
        public int maxTris;
        public int maxVerts;
        public bool clearDebug = false;

        public bool debugTestGrid;
        public bool debugTestGrid2;

        private void Awake()
        {
            Instance = this;
            VirtualShadowManager.Instance.InitVirtualShadowData();
        }

        private void ClearDebug()
        {
            maxBatches = -1;
            maxTris = -1;
            maxVerts = -1;
            clearDebug = false;
        }

        private void CollectionDebug()
        {
#if UNITY_EDITOR
            maxBatches = Mathf.Max(maxBatches,UnityEditor.UnityStats.batches);
            maxVerts = Mathf.Max(maxVerts,UnityEditor.UnityStats.vertices);
            maxTris = Mathf.Max(maxTris,UnityEditor.UnityStats.triangles);
#endif
        }

        private void Update()
        {
            if(clearDebug)
                ClearDebug();
            CollectionDebug();
        }

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        static void Rebinds2()
        {
            ReBinds();
        }

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
        static void Rebinds3()
        {
            ReBinds();
        }

#if UNITY_EDITOR
        [UnityEditor.Callbacks.DidReloadScripts]
#endif
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterAssembliesLoaded)]
        static void ReBinds()
        {
            if (Instance != null)
                return;

            string name = $"__{nameof(DebugShadows)}__";
            var go = GameObject.Find(name);
            if (go == null)
            {
                return;
            }

            DebugShadows debugShadows = go.GetComponent<DebugShadows>();

            Instance = debugShadows;
        }

        public static void InitInstance()
        {
            if (Instance == null)
            {
                string name = $"__{nameof(DebugShadows)}__";
                var go = GameObject.Find(name);
                if (go == null)
                {
                    go = new GameObject(name);
                }
                DebugShadows debugShadows = go.GetComponent<DebugShadows>();
                if (debugShadows == null)
                    debugShadows = go.AddComponent<DebugShadows>();

                Instance = debugShadows;
            }
        }

        private void OnDestroy()
        {
            Instance = null;
        }

        private void OnEnable()
        {
            VirtualShadowManager.Instance.ClearAll();
        }

        // [Button("清除 Dirty")]
        public void RefreshShadow()
        {
            VirtualShadowManager.Instance.ClearAll();
        }

        private void OnDrawGizmos()
        {
            if (VirtualShadowManager.Instance == null)
                return;

            if (VirtualShadowAssetHelper.asset == null)
                return;

            if (VirtualShadowAssetHelper.asset.ShadowRenderType != ShadowRenderType.VirtualShadow)
                return;

            if (Settings == null)
                return;

            {
                if (Settings == null)
                    return;

                if (!Settings.IsDebugRVT)
                    return;


                if (mesh == null)
                {
                    mesh = new Mesh();
                    var go = GameObject.CreatePrimitive(PrimitiveType.Cube);
                    mesh = go.GetComponent<MeshFilter>().sharedMesh;
                    GameObject.DestroyImmediate(go);
                }
                var backColor = Gizmos.color;

                var tokens = VirtualShadowManager.Instance.mRVTShadowTokenCaches;
                foreach (var rvtToken in tokens.mTokens)
                {

                    if (rvtToken == null)
                        break;

                    if (rvtToken.mCToken == null)
                        break;
                    if (!isDrawAllGroup)
                    {
                        if (drawGroup != rvtToken.mGroup)
                            continue;
                    }
                    var sm = Gizmos.matrix;
                    Gizmos.matrix = tokens.mRVTMinGridToGlobal;

                    var token = rvtToken;
                    Gizmos.color = new UnityEngine.Color(1, 0, 0, 0.4f);
                    //// shadow grids
                    foreach (var instanceRvtGrid in token.GetWaitGridDatas())
                    {
                        Gizmos.DrawMesh(mesh, new Vector3(instanceRvtGrid.rvtGrid.x, instanceRvtGrid.rvtGrid.y, 0),
                            Quaternion.identity,
                            Vector3.one * 0.7f);
                    }

                    Gizmos.color = new UnityEngine.Color(0, 1, 0, 0.4f);

// #if !_Development_VSM_

                    Gizmos.color = Color.green * 1;
                    // for (RVTTokenGroup i = 0; i < RVTTokenGroup.Max; i++)
                    #if false
                    {
                        Gizmos.color = new Color(Mathf.Sin((float)rvtToken.mGroup* 1.12323f), Mathf.Cos((float)rvtToken.mGroup * 0.12323f), 0, 1);
                        foreach (var item in VirtualShadowManager.Instance.GetGrids(rvtToken.mGroup))
                        {
                            if (item.Value.usingObjs.Count > 0)
                            {

                                var grid = item.Key;
                                Gizmos.DrawMesh(mesh,
                                    new Vector3(grid.x, grid.y, 0), Quaternion.identity,
                                    Vector3.one * 0.889f);

                            }
                        }
                    }
#endif
// #endif
                    if (drawGrids)
                    {
                        {
                            Color mcolor = new Color(Mathf.Sin((float)rvtToken.mGroup * 1.12323f),
                                Mathf.Cos((float)rvtToken.mGroup * 0.12323f), 0, 1);


                            #if UNITY_EDITOR
                            var grids = VirtualShadowManager.Instance.GetGrids(rvtToken.mGroup);
                            foreach (var item in grids)
                            {
                                Gizmos.matrix = (item.Value.project * item.Value.view).inverse;

                                if (item.Value.gridUV == Vector2Int.zero)
                                    Gizmos.color = Color.red;
                                else if (item.Value.gridUV == Vector2Int.one)
                                {
                                    Gizmos.color = Color.red * 0.5f;
                                }
                                else
                                {
                                    Gizmos.color = mcolor;
                                }
                                Gizmos.DrawMesh(mesh,
                                    new Vector3(0, 0, blockZOffset), Quaternion.identity,
                                    new Vector3(Mathf.Lerp(1.89f, 1, scaler), Mathf.Lerp(1.89f, 1, scaler), 0.01f));
                            }
                            #endif
                        }
                    }

                    #if _Development_VSM_

                    if (debugTestGrid2)
                    {
                        Gizmos.matrix = tokens.mRVTMinGridToGlobal;
                        var tPos = VirtualShadowManager.Instance.mShadowRendererObjMgr.mEdgeWalkinkPos;
                        Gizmos.color = Color.magenta;
                        foreach (var pos in tPos)
                        {
                            Gizmos.DrawMesh(mesh,
                                pos, Quaternion.identity,
                                new Vector3(Mathf.Lerp(0.189f, 1, scaler), Mathf.Lerp(0.189f, 1, scaler), 0.01f));
                        }
                    }
                    #endif

                    Gizmos.matrix = tokens.mRVTMinGridToGlobal;


                    // Gizmos.matrix = rvtToken.mCToken.mRVTUVToGlobal;
                    Gizmos.color = new UnityEngine.Color(0, 0, 0, 0.4f);
                    // rvt total size
                    // Gizmos.DrawMesh(mesh, Vector3.zero, Quaternion.identity,
                    //     new Vector3(rvtToken.mCToken.mRvtSize.x, rvtToken.mCToken.mRvtSize.y, 1));

                    Gizmos.color = backColor;

                    Gizmos.matrix = sm;

                    Gizmos.color = Color.red * 0.5f;
                    foreach (var instanceRvtGrid in token.GetWaitGridDatas())
                    {
                        Gizmos.matrix = instanceRvtGrid.view.inverse;
                        Gizmos.DrawMesh(mesh, new Vector3(0, 0, 5), Quaternion.identity,
                            Vector3.one * 0.9f);
                    }

                    Gizmos.matrix = VirtualShadowManager.Instance.mRVTShadowTokenCaches.mRVTMinGridToGlobal;
                    Gizmos.color = Color.red * 1.2f;


                    #if _Development_VSM_ && UNITY_EDITOR
                    if (drawSphere)
                    {
                        Gizmos.matrix = sm;
                        {
                            var grids = VirtualShadowManager.Instance.GetGrids(rvtToken.mGroup);
                            Gizmos.color = Color.red * 1.2f;
                            foreach (var keyValuePair in grids)
                            {
                                Vector4 sphere = keyValuePair.Value.splitData.cullingSphere;
                                Gizmos.DrawSphere(new Vector3(sphere.x, sphere.y, sphere.z), sphere.w * 0.1f);
                            }
                        }
                    }
                    #endif

#if false
                    foreach (var keyValuePair in noGrids)
                    {
                        float randv = keyValuePair.Value.x * 0.1f;
                        float offsetV = 0;
                        offsetV = keyValuePair.Value.x;
                        Vector3 offset = new Vector3(offsetV, offsetV, 0) * 0.5f;
                        Gizmos.color = new Color(Mathf.Sin(randv * 999.12323f), Mathf.Cos(randv * 3234.12323f), 0, 1);
                        if (keyValuePair.Key == Vector2Int.zero || keyValuePair.Key == Vector2Int.one)
                            Gizmos.color = Color.red;
                        Gizmos.DrawMesh(mesh,
                            new Vector3(keyValuePair.Key.x, keyValuePair.Key.y, keyValuePair.Value.x) + offset,
                            Quaternion.identity,
                            Vector3.one * Mathf.Lerp(keyValuePair.Value.x, 1, scaler) * 0.89f);
                    }
#endif
                    if (drawBlock)
                    {

                        {
                            Gizmos.matrix = token.mCToken.mBlockLocalToWorldMatrix;
                            Gizmos.color = blockColor;
                            int centerIdx = token.mRVT.mBlockSize.x / 2;
                            int startIdx = centerIdx - drawBlockCount / 2;
                            int max = startIdx + drawBlockCount;
                            float scaler = 1.0f / token.mRVT.mBlockSize.x;
                            for (int i = startIdx; i < max; i++)
                            {
                                for (int j = startIdx; j < max; j++)
                                {
                                    Gizmos.DrawMesh(mesh, new Vector3((i +0.5f) * scaler, (j + 0.5f)*scaler, blockZOffset), Quaternion.identity,
                                       new Vector3(0.01f,0.01f,0.01f) );
                                }
                            }
                        }
                    }

                }
                Gizmos.color = backColor;
            }
        }



        private void DrawBounds(Bounds? bounds, Color color, float aplha)
        {
            if (bounds != null)
            {
                Gizmos.color = color;
                SetGizmosAplha(aplha);
                var gb = bounds.Value;
                Gizmos.DrawCube(gb.center,
                    gb.size);
            }
        }

        private void SetGizmosAplha(float a)
        {
            Gizmos.color = new Color(Gizmos.color.r, Gizmos.color.g, Gizmos.color.b, a);
        }
    }
}
