using System;
using System.Collections.Generic;
using System.Linq;
using JetBrains.Annotations;
using UnityEngine.Rendering.Universal.VirtualTexture;

/// <summary>
/// 该文件定义了VirtualGridRendererObjManager类，用于管理虚拟网格渲染器对象。
/// 包含了网格、对象集群网格的管理，以及相关的预处理、绘制、清除等操作。
/// </summary>
namespace UnityEngine.Rendering.Universal.VirtualShadow
{
    /// <summary>
    /// VirtualGridRendererObjManager类负责管理虚拟网格渲染器对象，
    /// 提供了对象的添加、移除、绘制等功能，并处理网格和集群网格的相关操作。
    /// </summary>
    public class VirtualGridRendererObjManager
    {
        /// <summary>
        /// 存储不同组别的网格数据，每个Dictionary对应一个组，
        /// 其中Vector2Int为网格的UV坐标，RenderGrid为网格对象。
        /// </summary>
        private List<Dictionary<Vector2Int, RenderGrid>> mGrids = new();

        /// <summary>
        /// 存储对象集群网格，键为对象实例ID，值为RenderGridCluster对象，
        /// 用于管理与该对象相关的网格。
        /// </summary>
        private Dictionary<int, RenderGridCluster> mObjClusterGrids = new(128);

        /// <summary>
        /// 存储等待移除的对象集群ID，用于在合适的时机移除这些集群。
        /// </summary>
        private HashSet<int> mWaitRemoveClusters = new(128);

        /// <summary>
        /// 表示块索引，用于计算网格的索引位置。
        /// </summary>
        private Vector2Int mBlockIndexs;

        /// <summary>
        /// 表示最大块大小，由mBlockIndexs计算得出。
        /// </summary>
        private int mMaxBlockSize;

        /// <summary>
        /// 平均边界大小，用于计算推荐的半径大小。
        /// </summary>
        private float mAverageBoundSize;
        /// <summary>
        /// 最大边界大小，用于计算推荐的半径大小。
        /// </summary>
        private float mMaxBoundSize;
        /// <summary>
        /// 帧边界计数，用于计算平均边界大小。
        /// </summary>
        private int mFrameBoundCount = 0;

        /// <summary>
        /// 存储每个组中需要移除的块索引列表，用于清理不再使用的网格。
        /// </summary>
        private List<List<int>> mRemoveBlockIndexs = new();

        /// <summary>
        /// 边界统计算法，用于推荐半径大小的计算，默认使用最大边界算法。
        /// </summary>
        private AdjustmentRaduisRecommendAlgorith mRaduisRecommendAlgorith = AdjustmentRaduisRecommendAlgorith.MaxBounds;

        /// <summary>
        /// 网格统计算法，用于计算对象在网格中的分布，默认使用轮廓扫描算法。
        /// </summary>
        private RendererGridComputerAlgorith mRendererGridComputerAlgorith = RendererGridComputerAlgorith.OutlineScanning;

                /// <summary>
        /// 存储需要释放的网格UV坐标，用于清理不再使用的网格。
        /// </summary>
        private List<Vector2Int> mReleaseGrids = new();

        /// <summary>
        /// 构造函数，初始化mRemoveBlockIndexs和mGrids列表，
        /// 为每个RVTTokenGroup创建对应的列表项。
        /// </summary>
        public VirtualGridRendererObjManager()
        {
            for (RVTTokenGroup i = 0; i < RVTTokenGroup.Max; i++)
            {
                mRemoveBlockIndexs.Add(new());
                mGrids.Add(new());
            }
        }

        /// <summary>
        /// 清除所有数据，包括等待移除的集群、集群网格和普通网格。
        /// </summary>
        public void ClearAll()
        {
            mWaitRemoveClusters.Clear();
            ClearClusterGrids();
            ClearGrids();
        }

        /// <summary>
        /// 重绘所有对象，清除所有集群网格中的网格，并移除等待移除的集群网格，
        /// 最后清除所有普通网格。
        /// </summary>
        public void RedrawAllObjs()
        {
            foreach (var renderGridCluster in mObjClusterGrids)
            {
                renderGridCluster.Value.ClearAllGrids();
            }

            RemoveClusterGridsByWaits();
            ClearGrids();
        }

        /// <summary>
        /// 预处理操作，清除每个组中需要移除的块索引列表。
        /// </summary>
        public void PreProcess()
        {
            foreach (var ints in mRemoveBlockIndexs)
            {
                ints.Clear();
            }
        }

        /// <summary>
        /// 清除所有普通网格，释放每个网格并清空对应的字典。
        /// </summary>
        private void ClearGrids()
        {
            foreach (var renderGridsDict in mGrids)
            {
                foreach (var releaseGrid in renderGridsDict)
                {
                    ReleaseGrid(releaseGrid.Value);
                }

                renderGridsDict.Clear();
            }

        }

        /// <summary>
        /// 释放指定的网格，将其块索引添加到对应的移除列表中，
        /// 并通过对象池释放该网格对象。
        /// </summary>
        /// <param name="grid">要释放的网格对象</param>
        private void ReleaseGrid(RenderGrid grid)
        {
            if (grid.blockIndex != -1)
                mRemoveBlockIndexs[(int)grid.Group].Add(grid.blockIndex);
            RVTObjectPool<RenderGrid>.Release(grid);
        }

        /// <summary>
        /// 获取所有对象集群网格中的GPU图形对象，并添加到指定列表中。
        /// </summary>
        /// <param name="gObjs">用于存储GPU图形对象的列表</param>
        public void GetGPUGraphicsObjects(List<GPUGraphicsObject> gObjs)
        {
            foreach (var mObjClusterGrid in mObjClusterGrids)
            {
                if (mObjClusterGrid.Value.mGPUGraphicsObject != null)
                {
                    gObjs.Add(mObjClusterGrid.Value.mGPUGraphicsObject);
                }
            }
        }

        /// <summary>
        /// 清除所有集群网格，通过对象池释放每个集群网格对象，并清空集群网格字典。
        /// </summary>
        private void ClearClusterGrids()
        {
            foreach (var renderGridCluster in mObjClusterGrids)
            {
                RVTObjectPool<RenderGridCluster>.Release(renderGridCluster.Value);
            }
            mObjClusterGrids.Clear();
        }

        /// <summary>
        /// 获取指定组别的网格数据。
        /// </summary>
        /// <param name="shadowTokenMGroup">组别的枚举值</param>
        /// <returns>指定组别的网格数据字典</returns>
        public IReadOnlyDictionary<Vector2Int, RenderGrid> GetGrids(RVTTokenGroup shadowTokenMGroup)
        {
            return mGrids[(int)shadowTokenMGroup];
        }

        /// <summary>
        /// 获取指定组中需要移除的网格块索引列表。
        /// </summary>
        /// <param name="shadowTokenMGroup">组别的枚举值</param>
        /// <returns>指定组中需要移除的网格块索引列表</returns>
        public IReadOnlyList<int> GetRemoveGrids(RVTTokenGroup shadowTokenMGroup)
        {
            return mRemoveBlockIndexs[(int)shadowTokenMGroup];
        }

        /// <summary>
        /// 设置块索引和最大块大小。
        /// </summary>
        /// <param name="vector2Int">块索引向量</param>
        public void StepBlockSize(Vector2Int vector2Int)
        {
            mBlockIndexs = vector2Int;
            mMaxBlockSize = mBlockIndexs.x * mBlockIndexs.y;
        }

        /// <summary>
        /// 设置边界统计算法，如果算法发生改变则返回true。
        /// </summary>
        /// <param name="raduisRecommendAlgorith">边界统计算法枚举值</param>
        /// <returns>算法是否发生改变</returns>
        public bool StepRaduisRecommendAlgorith(AdjustmentRaduisRecommendAlgorith raduisRecommendAlgorith)
        {
            if (mRaduisRecommendAlgorith != raduisRecommendAlgorith)
            {
                mRaduisRecommendAlgorith = raduisRecommendAlgorith;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 第一组距离，用于判断对象是否在合适的范围内。
        /// </summary>
        private float firstGroupDistance;
        /// <summary>
        /// 网格距离计数，用于计算网格分组。
        /// </summary>
        public Vector4 gridDistanCount = Vector4.one;

        /// <summary>
        /// 获取绝对值减1，如果值为非负则直接返回，否则返回其绝对值减1。
        /// </summary>
        /// <param name="v">输入值</param>
        /// <returns>计算后的结果</returns>
        private int GetCount(int v)
        {
            if (v >= 0)
                return v;
            return -v - 1;
        }

        /// <summary>
        /// 计算网格的起始位置和分组索引。
        /// </summary>
        /// <param name="grid">网格UV坐标</param>
        /// <param name="index">输出的分组索引</param>
        private void ComputeStartPosAndGroup(Vector2Int grid, out int index)
        {
            int uIndex = 4 - 1;
            for (var i = 0; i < 4; i++)
            {
                if (GetCount(grid.x) < gridDistanCount[i] && GetCount(grid.y) < gridDistanCount[i])
                {
                    uIndex = i;
                    break;
                }
            }
            {
                index = uIndex;
            }
        }

        /// <summary>
        /// 设置第一组距离，根据网格距离计数和最小尺寸计算。
        /// </summary>
        /// <param name="miniSize">最小尺寸</param>
        public void Step(int miniSize)
        {
            firstGroupDistance = gridDistanCount[0] * miniSize;
        }


        public void Init(VirtualShadowData virtualShadowData)
        {
            gridDistanCount = virtualShadowData.gridDistancing;
            mRendererGridComputerAlgorith = virtualShadowData.rendererGridComputerAlgorith;
            StepBlockSize(virtualShadowData.GetRVTBlockSize());
            StepRaduisRecommendAlgorith(virtualShadowData.raduisRecommendAlgorith);
        }

        public List<Vector3> mEdgeWalkinkPos = new();

        public void ProcessBoundsByNoGrids(Matrix4x4 globalToRVTLocal)
        {
            //Debug.LogFormat("ProcessBoundsByNoGrids {0}", mObjClusterGrids.Count);

            foreach (var keyValuePair in mObjClusterGrids)
            {
                var objGrids = keyValuePair.Value;
                //Debug.LogFormat("ProcessBoundsByNoGrids ++ {0}", objGrids.renderer.name);

                bool isCompute = objGrids.gridCluster == GridClusterDefine.Draw;
                if (!objGrids.curFrameUsing) // 这一帧没有使用,重新计算
                    isCompute = true;

                if (!isCompute) // 不需要计算的
                    continue;

                int objInstanceID = keyValuePair.Key;
                foreach (var it in objGrids.GetGrids())
                {
                    it.Value.isUsing = false;
                    it.Key.usingObjs.Remove(objInstanceID);
                }

                //Debug.LogFormat("ProcessBoundsByNoGrids {0}", objGrids.renderer.name);

                if (objGrids.curFrameUsing)
                {
                    var bounds = keyValuePair.Value.Bounds;

                    switch (mRendererGridComputerAlgorith)
                    {
                        case RendererGridComputerAlgorith.AABBScanning:
                            {
                                Vector2Int uvMinGrid = new();
                                Vector2Int uvMaxGrid = new();
                                VirtualHelper.CalcShadowBounds(ref bounds, ref globalToRVTLocal, ref uvMinGrid, ref uvMaxGrid);

                                for (int i = uvMinGrid.x; i <= uvMaxGrid.x; i++)
                                {
                                    for (int j = uvMinGrid.y; j <= uvMaxGrid.y; j++)
                                    {
                                        Vector2Int grid = new Vector2Int(i, j);
                                        ComputeStartPosAndGroup(grid, out int index);
                                        RVTTokenGroup group = (RVTTokenGroup)index;
                                        var rGrid = GetOrCreateGrid(grid, objInstanceID, group);

                                        if (!objGrids.TryGet(rGrid, out var bindStates))
                                        {
                                            bindStates = objGrids.AddGrid(rGrid);
                                        }

                                        bindStates.isUsing = true;
                                        rGrid.usingObjs.Add(objInstanceID);
                                    }
                                }
                            }
                            break;
                        case RendererGridComputerAlgorith.OutlineScanning:
                            {
                                mEdgeWalkinkPos.Clear();
                                VirtualHelper.ComputerEdgeWalkink(ref bounds, ref globalToRVTLocal, mEdgeWalkinkPos);

                                for (var i = 0; i < mEdgeWalkinkPos.Count; i++)
                                {
                                    Vector2Int grid = new Vector2Int((int)mEdgeWalkinkPos[i].x, (int)mEdgeWalkinkPos[i].y);
                                    ComputeStartPosAndGroup(grid, out int index);
                                    RVTTokenGroup group = (RVTTokenGroup)index;
                                    var rGrid = GetOrCreateGrid(grid, objInstanceID, group);

                                    if (!objGrids.TryGet(rGrid, out var bindStates))
                                    {
                                        bindStates = objGrids.AddGrid(rGrid);
                                    }

                                    bindStates.isUsing = true;
                                    rGrid.usingObjs.Add(objInstanceID);
                                }
                            }
                            break;
                    }
                }
                else
                {
                    RemoveCluster(objInstanceID);
                }
                foreach (var valuePair in objGrids.GetGrids())
                {
                    valuePair.Key.needDraw = true; // 所有的都要重新 draw 一遍
                }

                objGrids.gridCluster = GridClusterDefine.Cache;
            }


            foreach (var dict in mGrids)
            {
                mReleaseGrids.Clear();
                foreach (var keyValuePair in dict)
                {
                    if (!keyValuePair.Value.IsVaild)
                        mReleaseGrids.Add(keyValuePair.Key);
                }

                foreach (var releaseGrid in mReleaseGrids)
                {
                    if (dict.Remove(releaseGrid, out var obj))
                        ReleaseGrid(obj);
                }

                mReleaseGrids.Clear();
            }

            RemoveClusterGridsByWaits();
        }


        public void RemoveCluster(int objId)
        {
            mWaitRemoveClusters.Add(objId);
        }

        public void OnFrameDrawFinish()
        {
            RemoveClusterGridsByWaits();
        }


        public void PreCull()
        {
            RemoveClusterGridsByWaits();

            foreach (var renderGridCluster in mObjClusterGrids)
            {
                renderGridCluster.Value.curFrameUsing = false;
            }
            mFrameBoundCount = 0;
            mAverageBoundSize = 0;
            mMaxBoundSize = float.MinValue;
        }

        private void RemoveClusterGridsByWaits()
        {
            foreach (var objInstanceID in mWaitRemoveClusters)
            {
                if (mObjClusterGrids.Remove(objInstanceID, out var cluster))
                {
                    //Debug.LogFormat("RemoveClusterGridsByWaits {0}", cluster.renderer.name);
                    foreach (var it in cluster.GetGrids())
                    {
                        it.Value.isUsing = false;
                        it.Key.usingObjs.Remove(objInstanceID);
                    }

                    RVTObjectPool<RenderGridCluster>.Release(cluster);
                }
            }
            mWaitRemoveClusters.Clear();
        }

        public void AddBounds(Bounds meshRendererBounds, int objInstanceID, Renderer renderer, bool isDirty)
        {
            if (!mObjClusterGrids.TryGetValue(objInstanceID, out var objBound))
            {
                objBound = RVTObjectPool<RenderGridCluster>.Get();
                mObjClusterGrids.Add(objInstanceID, objBound);
                isDirty = true;
            }

            if (isDirty)
            {
                objBound.Bounds = meshRendererBounds;
                objBound.gridCluster = GridClusterDefine.Draw;
            }
            
            objBound.curFrameUsing = true;
// #if _VSM_USE_REDNER_CASTER_
            objBound.renderer = renderer;
            //objBound.mGPUGraphicsObject = renderer.gameObject.GetComponent<RVTShadowRender>().mGraphicsObject;
            // #endif
        }


        private RenderGrid GetOrCreateGrid(Vector2Int grid, int objId, RVTTokenGroup rvtTokenGroup)
        {
            var g = new Vector2(grid.x, grid.y) / VirtualHelper.CalcRVTTokenGroupMultiple(rvtTokenGroup);
            var groupGridUV = new Vector2Int(Mathf.FloorToInt(g.x), Mathf.FloorToInt(g.y));

            if (!mGrids[(int)rvtTokenGroup].TryGetValue(groupGridUV, out RenderGrid renderGrid))
            {
                renderGrid = RVTObjectPool<RenderGrid>.Get();
                renderGrid.gridUV = grid;

                renderGrid.Group = rvtTokenGroup;

                renderGrid.groupGridUV = groupGridUV;
                // renderGrid.blockIndex = GridToIndex( renderGrid.groupGridUV);
                renderGrid.blockIndex = GridToIndex(renderGrid.groupGridUV);
                mGrids[(int)rvtTokenGroup].Add(groupGridUV, renderGrid);
            }
            return renderGrid;
        }


        private int GridToIndex(Vector2Int grid)
        {
            grid += mBlockIndexs / 2;
            int blockIndex = grid.x + grid.y * mBlockIndexs.x;

            if (mMaxBlockSize <= blockIndex)
            {
                throw new ArgumentOutOfRangeException($"Grid Out Of Range. {grid},{mBlockIndexs},{mMaxBlockSize}");
            }


            return blockIndex;
        }

        public bool IsReDrawByGrid(RenderGrid renderGrid)
        {
            if (!renderGrid.IsVaild)
            {
                return true;
            }

            return renderGrid.needDraw;
        }


        public void ProcessFinishGrid(RenderGrid renderGrid)
        {
            renderGrid.needDraw = false;

            if (!renderGrid.IsVaild)
            {
                if (mGrids[(int)renderGrid.Group].Remove(renderGrid.gridUV, out var obj))
                {
                    Debug.LogError("ProcessFinishGrid Remove Grid");
                    ReleaseGrid(obj);
                }
            }
        }

        public void GetRenderers(HashSet<int> objIds,List<Renderer> renderers )
        {
            renderers.Clear();
            foreach (var objId in objIds)
            {
                if (mObjClusterGrids.TryGetValue(objId,out var value))
                {
                    if(value.renderer != null && value.renderer.gameObject != null)
                        renderers.Add(value.renderer);
                }
            }

        }

        public Vector4 GetCullingSphere(RVTConvertToken token, RenderGrid renderGrid, float radius,List<GPUGraphicsObject> gpuGraphicsObjs)
        {
            int count = 0;
            float zValue = 0;
            float maxDistance = float.MinValue;
            var gridUV = renderGrid.groupGridUV;
            Vector2 centerUV = new Vector2(gridUV.x + 0.5f, gridUV.y + 0.5f);
            foreach (var renderGridUsingObj in renderGrid.usingObjs)
            {
                if (mObjClusterGrids.TryGetValue(renderGridUsingObj, out var cluster))
                {
                    var uvPos = token.mGlobalToRVTUV.MultiplyPoint(cluster.Bounds.center);
                    maxDistance = Mathf.Max(maxDistance, Vector2.Distance(centerUV, new Vector2(uvPos.x, uvPos.y)));
                    count++;
                    zValue += uvPos.z;
                    /*if(cluster.mGPUGraphicsObject != null)
                        gpuGraphicsObjs.Add(cluster.mGPUGraphicsObject);*/
                }
            }

            if (count > 0)
                zValue /= count;

            Vector3 worldPos = token.UVToWorldPos(new Vector3(gridUV.x, gridUV.y, zValue));
            Vector4 cullingSphere = new Vector4(worldPos.x, worldPos.y, worldPos.z,
                Mathf.CeilToInt(maxDistance) * radius * 1f * 1.1f);
            return cullingSphere;
        }

        public enum GridClusterDefine
        {
            Draw,
            Cache,
            Remove,
        }
        
        [CanBeNull]
        public RenderGrid[] GetClusterGrids(int instanceId)
        {
            if (mObjClusterGrids.TryGetValue(instanceId, out var clusterGrid))
            {
                return clusterGrid.GetGrids().Keys.ToArray();
            }
            
            
            return null;
        }
        
        public class RenderGridCluster : RVTIObject
        {
            public GridClusterDefine gridCluster = GridClusterDefine.Draw;
            public Bounds Bounds;
            private Dictionary<RenderGrid, RenderGridBindObj> grids = new();
            public bool curFrameUsing = false;
            public Renderer renderer;
            public GPUGraphicsObject mGPUGraphicsObject;

            public bool TryGet(RenderGrid grid, out RenderGridBindObj ret)
            {
                return grids.TryGetValue(grid, out ret);
            }

            public IDictionary<RenderGrid, RenderGridBindObj> GetGrids()
            {
                return grids;
            }

            public RenderGridBindObj AddGrid(RenderGrid grid)
            {
                var bindObj = RVTObjectPool<RenderGridBindObj>.Get();
                grids.Add(grid, bindObj);
                return bindObj;
            }

            public void OnGet()
            {
            }

            public void OnRelease()
            {
                ClearAllGrids();
                
            }

            public void ClearAllGrids()
            {
                gridCluster = GridClusterDefine.Draw;
                foreach (var renderGridBindObj in grids)
                {
                    RVTObjectPool<RenderGridBindObj>.Release(renderGridBindObj.Value);
                }

                grids.Clear();

                mGPUGraphicsObject = null;
            }
        }

        public class RenderGridBindObj : RVTIObject
        {
            public RenderGrid grid = null;
            public bool isUsing = false;

            public void OnGet()
            {
            }

            public void OnRelease()
            {
                grid = null;
                isUsing = false;
            }
        }


        public class RenderGrid : RVTIObject
        {
            public bool needDraw = true;

            public HashSet<int> usingObjs = new();

            public Vector2Int gridUV;

            public Vector2Int groupGridUV;

            public int blockIndex = -1;

            public bool IsVaild
            {
                get => usingObjs.Count != 0;
            }

            public RVTTokenGroup Group;

            #if UNITY_EDITOR
            public Matrix4x4 view;
            public Matrix4x4 project;
            public ShadowSplitData splitData;
            #endif

            public void OnGet()
            {
            }

            public void OnRelease()
            {
                usingObjs.Clear();
                needDraw = true;
                blockIndex = -1;
                Group = RVTTokenGroup.Group_0;
            }
        }


        public void RedrawGridByGroup(IReadOnlyDictionary<Vector2Int, RenderGrid> readOnlyDictionary)
        {
            foreach (var keyValuePair in readOnlyDictionary)
            {
                keyValuePair.Value.needDraw = true;
            }
        }
    }


    /// <summary>
    /// bounds 统计算法
    /// </summary>
    public enum AdjustmentRaduisRecommendAlgorith
    {
        MaxBounds, // 最大的bounds
        AverageBounds,
    }

    /// <summary>
    /// 格子的统计算法
    /// </summary>
    public enum RendererGridComputerAlgorith
    {
        AABBScanning,// aabb扫描
        OutlineScanning,// 轮廓扫描
    }
    
}
