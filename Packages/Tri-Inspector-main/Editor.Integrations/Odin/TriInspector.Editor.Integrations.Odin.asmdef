{"name": "TriInspector.Editor.Integrations.Odin", "rootNamespace": "", "references": ["TriInspector", "TriInspector.Editor", "UnityEditor.UI", "UnityEngine.UI"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["Sirenix.OdinInspector.Editor.dll", "Sirenix.Utilities.Editor.dll", "Sirenix.Utilities.dll"], "autoReferenced": false, "defineConstraints": ["ODIN_INSPECTOR"], "versionDefines": [], "noEngineReferences": false}