using System;
using System.Collections.Generic;
using TriInspector.Utilities;
using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;


namespace TriInspector.Editors
{
    class Unity_Editor : IEditor
    {
        private Editor _editor;
        public Unity_Editor(Editor editor)
        {
            _editor = editor;
        }
        public SerializedObject serializedObject { get =>_editor.serializedObject; }
        public void Repaint()
        {
            _editor.Repaint();
        }
    }
    
    class Unity_EditorWindow : IEditor
    {
        private TriEditorWindow _editor;
        public Unity_EditorWindow(TriEditorWindow editor)
        {
            _editor = editor;
        }
        public SerializedObject serializedObject { get => _editor._serializedObject; }
        public void Repaint()
        {
            _editor.Repaint();
        }
    }
    
    
    public interface IEditor
    {
        SerializedObject serializedObject { get; }
        void Repaint();
    }
}