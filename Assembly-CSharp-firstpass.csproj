<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{a0431b3a-6e27-fb34-197e-1acde9001a0f}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp-firstpass</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Assembly-CSharp-firstpass\</OutputPath>
    <DefineConstants>UNITY_2022_3_46;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_STANDALONE;TEXTCORE_1_0_OR_NEWER;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;ODIN_INSPECTOR;ODIN_INSPECTOR_3;ODIN_INSPECTOR_3_1;ODIN_VALIDATOR;ODIN_VALIDATOR_3_1;UNITY_POST_PROCESSING_STACK_V2;NODECANVAS;ODIN_INSPECTOR_3_2;ODIN_INSPECTOR_3_3;ODIN_VALIDATOR_3_2;ODIN_VALIDATOR_3_3;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="D:\Unity\2022.3.46f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="D:\Unity\2022.3.46f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Plugins\Sirenix\Odin Validator\EnsureCorrectOdinVersion.cs" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\ShaderFunctions\ReadMe.txt" />
    <None Include="Assets\Plugins\MeshTerrainEditor\EditorResources\MTE_EDITOR_RESOURCE_LOCATION.txt" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\URP14\Textures.shader" />
    <None Include="Assets\Plugins\MeshTerrainEditor\EditorResources\Languages\zh-Hans.xml" />
    <None Include="Assets\Plugins\MeshTerrainEditor\EditorResources\Shaders\Highlight.shader" />
    <None Include="Assets\Plugins\UniTask\package.json" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\URP14\TextureArray_Triplanar.shader" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\URP14\Grass.shader" />
    <None Include="Assets\Plugins\MeshTerrainEditor\EditorResources\Languages\en-US.xml" />
    <None Include="Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.Config.xml" />
    <None Include="Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Editor.xml" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\URP14\VertexColor.shader" />
    <None Include="Assets\Plugins\Sirenix\Odin Validator\Readme.txt" />
    <None Include="Assets\Plugins\Proxima\package.json" />
    <None Include="Assets\Plugins\Proxima\Third-Party Notices.txt" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\URP14\TextureArray_ColorAndNormal.shader" />
    <None Include="Assets\Plugins\MeshTerrainEditor\EditorResources\Shaders\PaintTexturePreview_URP.shader" />
    <None Include="Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Attributes.xml" />
    <None Include="Assets\Plugins\MeshTerrainEditor\EditorResources\hotkey.txt" />
    <None Include="Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.xml" />
    <None Include="Assets\Plugins\MeshTerrainEditor\EditorResources\Shaders\ConverterSplitPreview_URP.shader" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\URP14\TextureArray_ColorOnly.shader" />
    <None Include="Assets\Plugins\Sirenix\Readme.txt" />
    <None Include="Assets\Plugins\Proxima\WebSocketSharp\README.txt" />
    <None Include="Assets\Plugins\Proxima\WebSocketSharp\LICENSE.txt" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\URP14\ReadMe.txt" />
    <None Include="Assets\Plugins\Sirenix\Assemblies\link.xml" />
    <None Include="Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.Editor.xml" />
    <None Include="Assets\Plugins\Proxima\link.xml" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\URP14\TextureArray.shader" />
    <None Include="Assets\Plugins\MeshTerrainEditor\Shaders\URP\MTECommonURP.hlsl" />
    <None Include="Assets\Plugins\MeshTerrainEditor\EditorResources\Shaders\ConverterSplitPreview_Builtin.shader" />
    <None Include="Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.xml" />
    <None Include="Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinValidator.Editor.xml" />
    <None Include="Assets\Plugins\MeshTerrainEditor\EditorResources\Shaders\PaintTexturePreview_Builtin.shader" />
    <Reference Include="UnityEngine">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Reflection.Editor">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Reflection.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.collections@2.5.7\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.ext.nunit@2.0.3\net40\unity-custom\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="LitJson">
      <HintPath>D:\Project\client\Assets\Plugins\LitJson.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Serialization.Config">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.Config.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Editor">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Editor.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>D:\Project\client\Assets\Plugins\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Attributes">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Hashing">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.collections@2.5.7\Unity.Collections.Tests\System.IO.Hashing\System.IO.Hashing.dll</HintPath>
    </Reference>
    <Reference Include="MTEEditorAssets">
      <HintPath>D:\Project\client\Assets\Plugins\MeshTerrainEditor\EditorResources\MTEEditorAssets.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>D:\Project\client\Packages\com.code-philosophy.hybridclr\Plugins\dnlib.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.visualscripting@1.9.4\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>D:\Project\client\Packages\com.code-philosophy.hybridclr\Plugins\LZ4.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>D:\Project\client\Assets\Plugins\DOTween\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="proxima-websocket-sharp">
      <HintPath>D:\Project\client\Assets\Plugins\Proxima\WebSocketSharp\proxima-websocket-sharp.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.Utilities.Editor">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.nuget.newtonsoft-json@3.2.1\Runtime\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>D:\Project\client\Library\PackageCache\com.unity.nuget.mono-cecil@1.11.5\Mono.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="Coffee.UIParticle.R">
      <HintPath>D:\Project\client\Library\PackageCache\com.coffee.ui-particle@4.11.2\Runtime\Coffee.UIParticle.R.dll</HintPath>
    </Reference>
    <Reference Include="Sirenix.OdinValidator.Editor">
      <HintPath>D:\Project\client\Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinValidator.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.GradleProject">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.GradleProject.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>D:\Unity\2022.3.46f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="ICSharpCode.SharpZipLib.csproj">
      <Project>{9c506229-610c-0e4b-d036-3a4e2c5e10ea}</Project>
      <Name>ICSharpCode.SharpZipLib</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityEditor.csproj">
      <Project>{ac2f2cea-5f4d-2521-9100-a99822067e71}</Project>
      <Name>FMODUnityEditor</Name>
    </ProjectReference>
    <ProjectReference Include="Proxima.csproj">
      <Project>{c0720ac4-aec5-5da5-a31e-a887f067c27e}</Project>
      <Name>Proxima</Name>
    </ProjectReference>
    <ProjectReference Include="Core.csproj">
      <Project>{718d5b2d-583a-93de-718b-ccf9e4420a12}</Project>
      <Name>Core</Name>
    </ProjectReference>
    <ProjectReference Include="com.unity.cinemachine.editor.csproj">
      <Project>{dfb74bc9-50cd-99a6-6b81-95c955cfc346}</Project>
      <Name>com.unity.cinemachine.editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Burst.Editor.csproj">
      <Project>{9b2673c3-1a77-7f16-dd67-300ef11961b3}</Project>
      <Name>Unity.Burst.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="BestHTTP.csproj">
      <Project>{54471c48-90a9-dabc-379c-480412fe54f4}</Project>
      <Name>BestHTTP</Name>
    </ProjectReference>
    <ProjectReference Include="SoftMask.Editor.csproj">
      <Project>{30db1487-8e83-cf9d-67d6-960ba61f75bb}</Project>
      <Name>SoftMask.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Common.Path.Editor.csproj">
      <Project>{2085dbbf-7682-034e-d060-edee3c34e72d}</Project>
      <Name>Unity.2D.Common.Path.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="MTE_plugins.csproj">
      <Project>{589ac3fa-377c-4691-93cf-f72aca3c865b}</Project>
      <Name>MTE_plugins</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Editor.csproj">
      <Project>{abcdaff7-8ceb-ea50-aeb8-4f5402d7e818}</Project>
      <Name>HybridCLR.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.ScriptableBuildPipeline.csproj">
      <Project>{37db7129-ce05-fff0-87e2-a8069dd40bc2}</Project>
      <Name>Unity.ScriptableBuildPipeline</Name>
    </ProjectReference>
    <ProjectReference Include="AVProVideo.Editor.csproj">
      <Project>{0969a1d2-903c-2272-98c7-1ff45434193c}</Project>
      <Name>AVProVideo.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="spine-csharp.csproj">
      <Project>{d633ad13-749b-c19b-79b5-de2e465c4f08}</Project>
      <Name>spine-csharp</Name>
    </ProjectReference>
    <ProjectReference Include="SoftMask.csproj">
      <Project>{8bc8e89f-73b9-7b1c-a723-2f426389d4bf}</Project>
      <Name>SoftMask</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Aseprite.Common.csproj">
      <Project>{5633dec5-f0b4-870f-56ef-de352c57af79}</Project>
      <Name>Unity.2D.Aseprite.Common</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityResonance.csproj">
      <Project>{710aab89-2b5d-111c-d2b6-4b78886180d2}</Project>
      <Name>FMODUnityResonance</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Config.Runtime.csproj">
      <Project>{5d6fc226-e608-c678-e44f-bf1b2b586072}</Project>
      <Name>Unity.RenderPipelines.Universal.Config.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.csproj">
      <Project>{05f5c917-0880-5c1f-f19d-9edf44b0f8b8}</Project>
      <Name>Unity.RenderPipelines.Core.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Core.ShaderLibrary.csproj">
      <Project>{597e0dce-7af3-4d47-fcd2-b0d83af1bdeb}</Project>
      <Name>Unity.RenderPipelines.Core.ShaderLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Collections.csproj">
      <Project>{0b7f2792-1a73-808f-2073-c19e264c2b04}</Project>
      <Name>Unity.Collections</Name>
    </ProjectReference>
    <ProjectReference Include="AVProVideo.Runtime.csproj">
      <Project>{f8f2a02a-6a30-6fe9-9cb3-304074bd7e3e}</Project>
      <Name>AVProVideo.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnity.csproj">
      <Project>{b2a04fda-593f-2822-75b6-7a0ce85de5c8}</Project>
      <Name>FMODUnity</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.csproj">
      <Project>{62f6f6b8-73f7-2f4f-cc1a-c6ab1d3e2a6e}</Project>
      <Name>UniTask</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.PixelPerfect.csproj">
      <Project>{70728ac3-6bf0-a816-ad91-7ae54a831d05}</Project>
      <Name>Unity.2D.PixelPerfect</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.TextMeshPro.csproj">
      <Project>{1dbe75aa-83d3-a401-15dc-b38cacf827e2}</Project>
      <Name>UniTask.TextMeshPro</Name>
    </ProjectReference>
    <ProjectReference Include="Protobuf.csproj">
      <Project>{e173ec61-5e46-586c-b7eb-1e4005756cdc}</Project>
      <Name>Protobuf</Name>
    </ProjectReference>
    <ProjectReference Include="PPv2URPConverters.csproj">
      <Project>{2f2a94df-cf4b-aa43-a508-9c7f5ca5cf45}</Project>
      <Name>PPv2URPConverters</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.TextMeshPro.csproj">
      <Project>{585d0195-7c2b-c560-4a76-f418f8ca6686}</Project>
      <Name>Unity.TextMeshPro</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Aseprite.Editor.csproj">
      <Project>{a5995a36-daca-143d-932b-51ec02dd3f99}</Project>
      <Name>Unity.2D.Aseprite.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VSCode.Editor.csproj">
      <Project>{c322b84d-69d2-d156-42df-c250667486ff}</Project>
      <Name>Unity.VSCode.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Toolchain.Win-x86_64-Linux-x86_64.csproj">
      <Project>{59801805-832b-a376-eb07-568e3f311a2e}</Project>
      <Name>Unity.Toolchain.Win-x86_64-Linux-x86_64</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Editor.csproj">
      <Project>{b68cf121-d620-d1ce-0542-3aa20b219cec}</Project>
      <Name>Unity.RenderPipelines.Universal.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.MemoryProfiler.csproj">
      <Project>{ff068c3b-30c5-5f07-94df-f0a8a3b1d7d7}</Project>
      <Name>Unity.MemoryProfiler</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.MemoryProfiler.Editor.MemoryProfilerModule.csproj">
      <Project>{9b141d0c-8856-7688-fa55-3e4961ab260c}</Project>
      <Name>Unity.MemoryProfiler.Editor.MemoryProfilerModule</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Runtime.csproj">
      <Project>{ca0e7e93-9c6a-c746-f2ff-0f16cc228c0c}</Project>
      <Name>Unity.RenderPipelines.Universal.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Burst.csproj">
      <Project>{085787af-59b7-541e-49e4-86fcaab3a35b}</Project>
      <Name>Unity.Burst</Name>
    </ProjectReference>
    <ProjectReference Include="Sirenix.OdinInspector.Modules.UnityMathematics.csproj">
      <Project>{0b79db10-9a7e-4258-fbde-7c0acbb264ba}</Project>
      <Name>Sirenix.OdinInspector.Modules.UnityMathematics</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualStudio.Editor.csproj">
      <Project>{a870aa00-26e7-6ba1-80a7-a98269def8d7}</Project>
      <Name>Unity.VisualStudio.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEditor.UI.csproj">
      <Project>{27df5c51-0832-0742-f448-c95d51cbbe66}</Project>
      <Name>UnityEditor.UI</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Animation.Runtime.csproj">
      <Project>{502c437b-ba35-f640-2168-a9e958312c02}</Project>
      <Name>Unity.2D.Animation.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="ThinkingAnalytics.csproj">
      <Project>{593def3f-3053-4b75-6b47-c01695e55642}</Project>
      <Name>ThinkingAnalytics</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.Flow.Editor.csproj">
      <Project>{bcf8702f-e89a-c9d1-30e0-824605e087c7}</Project>
      <Name>Unity.VisualScripting.Flow.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipeline.Universal.ShaderLibrary.csproj">
      <Project>{d404f66d-1a36-f2b9-eb1f-784f47182d41}</Project>
      <Name>Unity.RenderPipeline.Universal.ShaderLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="AVProVideo.Extensions.UnityUI.csproj">
      <Project>{95d141ff-790c-d328-374a-ce6d426b998f}</Project>
      <Name>AVProVideo.Extensions.UnityUI</Name>
    </ProjectReference>
    <ProjectReference Include="GameMain.csproj">
      <Project>{1a0c16d8-bfc8-ff21-e771-e4ba60c0aa50}</Project>
      <Name>GameMain</Name>
    </ProjectReference>
    <ProjectReference Include="FingerGestures.Editor.csproj">
      <Project>{ed8c39bb-9c94-4f49-dedb-bbdc83d277b7}</Project>
      <Name>FingerGestures.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.MemoryProfiler.Editor.csproj">
      <Project>{3891eb54-b763-5180-3b51-d94ee6c9cbf4}</Project>
      <Name>Unity.MemoryProfiler.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Postprocessing.Editor.csproj">
      <Project>{258d3d40-386b-3526-f193-f03d28a8b899}</Project>
      <Name>Unity.Postprocessing.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Autodesk.Fbx.Editor.csproj">
      <Project>{391e01fb-d162-9756-d719-59c37bf3c488}</Project>
      <Name>Autodesk.Fbx.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="spine-unity-editor.csproj">
      <Project>{c16d3fc2-d438-a49f-0d35-4d45f5039eb5}</Project>
      <Name>spine-unity-editor</Name>
    </ProjectReference>
    <ProjectReference Include="TriInspector.Editor.csproj">
      <Project>{fdb4f458-bbf7-37a8-275c-bf0147b0ace3}</Project>
      <Name>TriInspector.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Shaders.csproj">
      <Project>{8f72a324-c49e-c46f-f6af-9f7b197997ca}</Project>
      <Name>Unity.RenderPipelines.Universal.Shaders</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Searcher.Editor.csproj">
      <Project>{7d42c02c-6d85-8a03-4bd8-bb82c10a7eaa}</Project>
      <Name>Unity.Searcher.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.State.csproj">
      <Project>{1a1f4316-622d-fec9-0520-e6b31c966314}</Project>
      <Name>Unity.VisualScripting.State</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.AssetBundleBrowser.Editor.csproj">
      <Project>{d6d11745-a9b9-43f7-c9e4-2754ca91d8f1}</Project>
      <Name>Unity.AssetBundleBrowser.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.ScriptableBuildPipeline.Editor.csproj">
      <Project>{a6da49f0-bd13-57e1-237a-d48707f847e4}</Project>
      <Name>Unity.ScriptableBuildPipeline.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.SpriteShape.Editor.csproj">
      <Project>{7ba4672d-ebbd-636c-62d2-a088c6de11b3}</Project>
      <Name>Unity.2D.SpriteShape.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.IK.Editor.csproj">
      <Project>{8f7b006c-c372-aac8-46be-5fd2f3884192}</Project>
      <Name>Unity.2D.IK.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Assets_Runtime.csproj">
      <Project>{10edc0df-528c-69ca-d375-fb5b0f10ef18}</Project>
      <Name>Assets_Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.Addressables.csproj">
      <Project>{50805ec9-178d-ea81-fab8-39cebb1abc10}</Project>
      <Name>UniTask.Addressables</Name>
    </ProjectReference>
    <ProjectReference Include="Vertx.Debugging.Runtime.csproj">
      <Project>{86436546-ab8b-ec75-afd0-24febdcdf42e}</Project>
      <Name>Vertx.Debugging.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.IK.Runtime.csproj">
      <Project>{a9a98014-e3ed-cdef-c1bf-33f839d7d1b1}</Project>
      <Name>Unity.2D.IK.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="HybridCLR.Runtime.csproj">
      <Project>{7dacd347-4b99-43ff-7bd2-399821e768b0}</Project>
      <Name>HybridCLR.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.csproj">
      <Project>{60d92bfc-dadf-ea1a-c37a-2fb3e70e873a}</Project>
      <Name>Unity.RenderPipelines.Core.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Postprocessing.Runtime.csproj">
      <Project>{be55b292-cc44-17c8-5069-aa98556bdc44}</Project>
      <Name>Unity.Postprocessing.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="BezierSolution.Editor.csproj">
      <Project>{c283925b-85fa-bb2d-75b0-7ac0dbd621b4}</Project>
      <Name>BezierSolution.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.AI.Navigation.csproj">
      <Project>{042bcc92-e5f5-c956-3587-4f4935221218}</Project>
      <Name>Unity.AI.Navigation</Name>
    </ProjectReference>
    <ProjectReference Include="TriInspector.csproj">
      <Project>{8d28c0bf-be04-36ba-0385-036bb9bea187}</Project>
      <Name>TriInspector</Name>
    </ProjectReference>
    <ProjectReference Include="FingerGestures.csproj">
      <Project>{79f9773e-8a11-251a-e9a9-26d66a35c230}</Project>
      <Name>FingerGestures</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.InternalAPIEditorBridge.013.csproj">
      <Project>{31e34d08-e19b-74b3-338d-f590ee0946aa}</Project>
      <Name>Unity.InternalAPIEditorBridge.013</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.PixelPerfect.Editor.csproj">
      <Project>{55d62191-80c0-3596-d0e7-1fe60495bccf}</Project>
      <Name>Unity.2D.PixelPerfect.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Rider.Editor.csproj">
      <Project>{8dd1df9e-b562-8960-961c-82d26d34aa5e}</Project>
      <Name>Unity.Rider.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Sprite.Editor.csproj">
      <Project>{94bc3929-522a-25cf-655c-afc30fd694c4}</Project>
      <Name>Unity.2D.Sprite.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="MTE_script.csproj">
      <Project>{85e06ecf-65a7-dde8-83c0-e9d25218a5c3}</Project>
      <Name>MTE_script</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.Flow.csproj">
      <Project>{25695447-1c00-9e36-e9d9-6bdc4391846b}</Project>
      <Name>Unity.VisualScripting.Flow</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Mathematics.Editor.csproj">
      <Project>{8db537a7-44c2-cb1f-7884-df080dbee2b2}</Project>
      <Name>Unity.Mathematics.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="VirtualShadow.Editor.csproj">
      <Project>{ee0d9e27-d222-12ba-25df-4355e288d78f}</Project>
      <Name>VirtualShadow.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Tilemap.Extras.Editor.csproj">
      <Project>{d0ea45e1-e74e-51b8-8690-be051cf1f173}</Project>
      <Name>Unity.2D.Tilemap.Extras.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="FixRes.csproj">
      <Project>{bf0e1dd9-2c38-10e1-18ee-ce6ee48f2545}</Project>
      <Name>FixRes</Name>
    </ProjectReference>
    <ProjectReference Include="ThinkingSDK.csproj">
      <Project>{1229c93f-044c-11b1-d48e-98516ee62ba5}</Project>
      <Name>ThinkingSDK</Name>
    </ProjectReference>
    <ProjectReference Include="AVProVideo.Extensions.VisualEffectGraph.csproj">
      <Project>{60392d3c-f727-6655-9f7b-c4ab9700e8bd}</Project>
      <Name>AVProVideo.Extensions.VisualEffectGraph</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.SpriteShape.Runtime.csproj">
      <Project>{24e2aad8-bdd8-3f11-655e-893e74b52d9b}</Project>
      <Name>Unity.2D.SpriteShape.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Collections.Editor.csproj">
      <Project>{f4ac807e-3913-03d7-32f9-d039a3caf120}</Project>
      <Name>Unity.Collections.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="spine-unity.csproj">
      <Project>{ce5d6b30-a2dd-b9db-65ae-bf6bf68ceff5}</Project>
      <Name>spine-unity</Name>
    </ProjectReference>
    <ProjectReference Include="Coffee.UIParticle.csproj">
      <Project>{04540531-31b5-ecaf-3050-921f628688e1}</Project>
      <Name>Coffee.UIParticle</Name>
    </ProjectReference>
    <ProjectReference Include="NodeCanvas.csproj">
      <Project>{a1b0f2cf-3646-7174-a521-f380ecdc34d6}</Project>
      <Name>NodeCanvas</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.AI.Navigation.Editor.ConversionSystem.csproj">
      <Project>{37b9e22d-e2ed-f896-becc-7beb618e3242}</Project>
      <Name>Unity.AI.Navigation.Editor.ConversionSystem</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Profiling.Core.csproj">
      <Project>{5e941bb7-cbe4-faae-ad29-87e99f50f562}</Project>
      <Name>Unity.Profiling.Core</Name>
    </ProjectReference>
    <ProjectReference Include="DOTweenModuleUI.csproj">
      <Project>{801812f0-5219-48d4-4933-68c2244279a9}</Project>
      <Name>DOTweenModuleUI</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj">
      <Project>{49e066c2-10cb-8d1c-c2f8-8aa763a307a5}</Project>
      <Name>Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Sysroot.Linux_x86_64.csproj">
      <Project>{cc762302-541d-0b7f-a027-02468e02f405}</Project>
      <Name>Unity.Sysroot.Linux_x86_64</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.EditorCoroutines.Editor.csproj">
      <Project>{4bf3e7af-6ee5-f33a-d82a-d261fe824852}</Project>
      <Name>Unity.EditorCoroutines.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.Linq.csproj">
      <Project>{3486e4c8-d8e7-701f-2e3e-f3339cf23acd}</Project>
      <Name>UniTask.Linq</Name>
    </ProjectReference>
    <ProjectReference Include="VirtualShadow.Runtime.csproj">
      <Project>{44dca53c-a6f8-0a8d-28e6-c3526500da35}</Project>
      <Name>VirtualShadow.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="PsdPlugin.csproj">
      <Project>{c631cc4b-b46b-e67d-19bd-5e5155c81fb9}</Project>
      <Name>PsdPlugin</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.PlasticSCM.Editor.csproj">
      <Project>{178afc91-dcbb-4212-31a8-33233b1fde02}</Project>
      <Name>Unity.PlasticSCM.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="BezierSolution.Runtime.csproj">
      <Project>{c3f7fa2e-3825-134a-9a2b-8046ad151603}</Project>
      <Name>BezierSolution.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="BuildsAssets-Editor.csproj">
      <Project>{a8810701-7405-88e9-bebe-4ac87c851e04}</Project>
      <Name>BuildsAssets-Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Timeline.Editor.csproj">
      <Project>{974cfc55-6dec-6992-2367-180fba3b92ef}</Project>
      <Name>Unity.Timeline.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.Shared.Editor.csproj">
      <Project>{e65fc10e-7ceb-4d3b-4b48-15b2fc2a0ec9}</Project>
      <Name>Unity.VisualScripting.Shared.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.ShaderGraph.Editor.csproj">
      <Project>{db1808cf-7b7e-3145-8c76-a1be47eb7532}</Project>
      <Name>Unity.ShaderGraph.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.Core.csproj">
      <Project>{236e9e3d-167a-abc2-72c0-f5a211b08815}</Project>
      <Name>Unity.VisualScripting.Core</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Tilemap.Extras.csproj">
      <Project>{8e1cd453-3011-7306-8920-7a3393848527}</Project>
      <Name>Unity.2D.Tilemap.Extras</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.Core.Editor.csproj">
      <Project>{873b1364-17f5-1904-fba9-4ae558561472}</Project>
      <Name>Unity.VisualScripting.Core.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.UI.csproj">
      <Project>{2b8120b2-369f-90b0-cea1-bba27beb3963}</Project>
      <Name>UnityEngine.UI</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.InternalAPIEngineBridge.001.csproj">
      <Project>{42cb5865-e3b5-7013-28e9-32e645f7b174}</Project>
      <Name>Unity.InternalAPIEngineBridge.001</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Common.Runtime.csproj">
      <Project>{2abe2f52-c766-5d64-2643-1a6f90605acf}</Project>
      <Name>Unity.2D.Common.Runtime</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Timeline.csproj">
      <Project>{9c1383fb-3d7e-8616-d37a-fd6b0f8852c0}</Project>
      <Name>Unity.Timeline</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.SettingsProvider.Editor.csproj">
      <Project>{5648c828-4ff2-5fec-20a3-73b90ac4bd72}</Project>
      <Name>Unity.VisualScripting.SettingsProvider.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Main.csproj">
      <Project>{1ee9dca8-4a31-b4dd-7d76-a039fbb7f77b}</Project>
      <Name>Main</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.SysrootPackage.Editor.csproj">
      <Project>{ea6b9f79-b3f6-19ef-a9fc-0903f8b25acb}</Project>
      <Name>Unity.SysrootPackage.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="ParadoxNotion.csproj">
      <Project>{6794afec-0e43-2e73-9838-3836c1849b69}</Project>
      <Name>ParadoxNotion</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Psdimporter.Editor.csproj">
      <Project>{a21d2a30-d0ad-2841-f069-97d5958f9feb}</Project>
      <Name>Unity.2D.Psdimporter.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="AVProVideo.Extensions.Timeline.csproj">
      <Project>{bc1b84b7-c625-b7ef-bfa2-0d95568db6f4}</Project>
      <Name>AVProVideo.Extensions.Timeline</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.AI.Navigation.Updater.csproj">
      <Project>{be28a5a6-2cc6-2212-49c3-a5c5a8aa1388}</Project>
      <Name>Unity.AI.Navigation.Updater</Name>
    </ProjectReference>
    <ProjectReference Include="Autodesk.Fbx.csproj">
      <Project>{67923455-5989-2313-4f97-9da5a53420cb}</Project>
      <Name>Autodesk.Fbx</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.InternalAPIEditorBridge.001.csproj">
      <Project>{a139a64a-8868-780b-f515-184d321a89a3}</Project>
      <Name>Unity.InternalAPIEditorBridge.001</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.VisualScripting.State.Editor.csproj">
      <Project>{cb87a45b-99a3-5fb7-54a5-1e76ce7e8755}</Project>
      <Name>Unity.VisualScripting.State.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="FMODUnityResonanceEditor.csproj">
      <Project>{4d2d783a-89e5-af18-93e4-1cd1706fd8dd}</Project>
      <Name>FMODUnityResonanceEditor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Mathematics.csproj">
      <Project>{2a0b4c1e-4771-8b7d-74db-d77243411067}</Project>
      <Name>Unity.Mathematics</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Common.Editor.csproj">
      <Project>{05bb918b-c16b-abe5-9dda-77c4af7acb9a}</Project>
      <Name>Unity.2D.Common.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.TextMeshPro.Editor.csproj">
      <Project>{59744692-fbd8-8493-151d-b87157382913}</Project>
      <Name>Unity.TextMeshPro.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.AI.Navigation.Editor.csproj">
      <Project>{acb11963-d08a-122b-b569-acd3e3ef6d11}</Project>
      <Name>Unity.AI.Navigation.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Cinemachine.csproj">
      <Project>{5530a000-b42b-5b3d-0a18-1eb204591ffd}</Project>
      <Name>Cinemachine</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Tilemap.Editor.csproj">
      <Project>{a85ebaa7-1773-8e0d-ab44-368e91d1030f}</Project>
      <Name>Unity.2D.Tilemap.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Proxima.Editor.csproj">
      <Project>{8ea8bc99-78ec-b440-8f0a-591fd1f43326}</Project>
      <Name>Proxima.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="UniTask.DOTween.csproj">
      <Project>{d88bf341-7d5a-fb2d-c665-647b3be73561}</Project>
      <Name>UniTask.DOTween</Name>
    </ProjectReference>
    <ProjectReference Include="AVProVideo.Extensions.UnityUI.Editor.csproj">
      <Project>{a91f756c-26eb-ffe9-ff7e-5b8c73e05a43}</Project>
      <Name>AVProVideo.Extensions.UnityUI.Editor</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.2D.Animation.Editor.csproj">
      <Project>{c58eb1ba-b0c9-4a27-faba-f5da33d7fef9}</Project>
      <Name>Unity.2D.Animation.Editor</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
